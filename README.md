# 灵邀系统 - 智能邀约与CRM管理平台

[![FastAPI](https://img.shields.io/badge/FastAPI-0.115.12-green.svg)](https://fastapi.tiangolo.com)
[![Vue.js](https://img.shields.io/badge/Vue.js-3.x-brightgreen.svg)](https://vuejs.org)
[![Python](https://img.shields.io/badge/Python-3.8%2B-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/License-Proprietary-red.svg)]()

欢迎来到 **灵邀系统**！这是一个基于 Python FastAPI 和 Vue.js 构建的现代化、智能化的客户关系管理（CRM）和邀约管理平台。系统集成了团队管理、样品管理、AI对话、数据分析等多项功能，旨在通过高效的自动化流程和智能化的AI辅助，帮助企业和团队提升业务效率和管理水平。

## ✨ 核心功能亮点

### 🏢 企业级CRM管理
*   **团队管理:** 完整的组织架构管理，支持公司创建、团队管理、成员权限分配
*   **权限系统:** 细粒度的权限控制，支持角色管理和动态权限分配
*   **邀请系统:** 智能邀请链接生成，支持团队成员快速邀请和加入

### 🚀 用户与认证系统
*   **安全认证:** JWT令牌认证，支持手机号登录、短信验证码
*   **用户管理:** 完整的用户生命周期管理，支持用户状态跟踪
*   **会话管理:** 多设备登录支持，会话安全控制

### 🛍️ 业务管理模块
*   **样品管理:** 全生命周期样品信息管理，支持批量操作和状态跟踪
*   **订单处理:** 完整的订单流程管理，支持微信支付集成
*   **线索管理:** 客户线索跟踪和转化管理
*   **达人管理:** KOL资源管理和合作跟踪

### 🤖 AI智能助手
*   **智能对话:** 集成多种AI模型（Coze、FastGPT、豆包等），提供智能客服和业务咨询
*   **产品解析:** AI驱动的产品信息自动解析和分类
*   **内容生成:** 智能生成邀约内容和商务沟通文案
*   **全局调度:** 智能分配对话给不同专业领域的AI Agent

### 📊 数据分析与监控
*   **实时看板:** 基于Vue3和ECharts的数据可视化看板
*   **业务报表:** 多维度业务数据分析和报表生成
*   **系统监控:** 实时系统性能监控和健康检查
*   **日志分析:** 五层日志体系（系统、错误、接口、数据库、安全）

### 🛡️ 安全与性能
*   **安全防护:** IP限速、敏感词过滤、SQL注入防护
*   **性能优化:** 异步数据库连接池，缓存机制
*   **文件存储:** 腾讯云COS集成，支持文件上传下载
*   **API限流:** 智能API调用频率控制

## 🛠️ 技术栈

### 后端技术栈 (Python)

| 技术栈 | 版本 | 用途 |
|--------|------|------|
| ![Python](https://img.shields.io/badge/Python-3.8%2B-blue.svg) | 3.8+ | 主要编程语言 |
| ![FastAPI](https://img.shields.io/badge/FastAPI-0.115.12-green.svg) | 0.115.12 | 高性能Web API框架 |
| ![Uvicorn](https://img.shields.io/badge/Uvicorn-Latest-blue.svg) | Latest | ASGI服务器 |
| ![MySQL](https://img.shields.io/badge/MySQL-8.0-orange.svg) | 8.0 | 主数据库 |
| ![PyMySQL](https://img.shields.io/badge/PyMySQL-Latest-red.svg) | Latest | 数据库连接器 |
| ![Pydantic](https://img.shields.io/badge/Pydantic-2.x-yellow.svg) | 2.x | 数据验证和序列化 |
| ![JWT](https://img.shields.io/badge/JWT-python--jose-purple.svg) | Latest | 身份认证 |

**核心依赖:**
*   **异步数据库:** 自研异步连接池，支持高并发数据库操作
*   **微信支付SDK:** 集成微信支付V3接口，支持统一下单、回调处理
*   **阿里云SDK:** 短信服务、对象存储集成
*   **腾讯云COS:** 文件存储和CDN加速
*   **AI集成:** 支持Coze、FastGPT、豆包等多种AI模型
*   **WebSocket:** 实时日志推送和消息通知

### 前端技术栈 (Vue.js)

| 技术栈 | 版本 | 用途 |
|--------|------|------|
| ![Vue.js](https://img.shields.io/badge/Vue.js-3.x-brightgreen.svg) | 3.x | 渐进式前端框架 |
| ![Vite](https://img.shields.io/badge/Vite-5.x-purple.svg) | 5.x | 现代化构建工具 |
| ![Ant Design Vue](https://img.shields.io/badge/Ant%20Design%20Vue-4.x-blue.svg) | 4.x | 企业级UI组件库 |
| ![Pinia](https://img.shields.io/badge/Pinia-2.x-yellow.svg) | 2.x | 状态管理 |
| ![Vue Router](https://img.shields.io/badge/Vue%20Router-4.x-green.svg) | 4.x | 路由管理 |
| ![ECharts](https://img.shields.io/badge/ECharts-5.x-red.svg) | 5.x | 数据可视化 |
| ![Axios](https://img.shields.io/badge/Axios-1.x-orange.svg) | 1.x | HTTP客户端 |

**前端特性:**
*   **响应式设计:** 支持桌面端和移动端自适应
*   **组件化开发:** 可复用的Vue组件库
*   **实时通信:** WebSocket集成，支持实时数据推送
*   **国际化:** 支持多语言切换
*   **主题定制:** 可自定义主题颜色和样式

### 数据库与存储

| 技术 | 版本 | 用途 |
|------|------|------|
| ![MySQL](https://img.shields.io/badge/MySQL-8.0-blue.svg) | 8.0+ | 主数据库 |
| ![Redis](https://img.shields.io/badge/Redis-7.x-red.svg) | 7.x | 缓存和会话存储 |
| ![腾讯云COS](https://img.shields.io/badge/腾讯云COS-Latest-green.svg) | Latest | 对象存储 |

## 🏗️ 系统架构

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端管理后台    │    │    客户端应用     │    │   移动端应用     │
│   (Vue.js)      │    │   (Vue.js)      │    │   (规划中)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
              ┌─────────────────────────────┐
              │       API网关/负载均衡        │
              │      (Nginx/Uvicorn)       │
              └─────────────────────────────┘
                                 │
              ┌─────────────────────────────┐
              │      FastAPI应用服务器       │
              │    (Python/异步处理)        │
              └─────────────────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   MySQL数据库    │    │   Redis缓存     │    │   腾讯云COS     │
│   (主数据存储)   │    │   (会话/缓存)   │    │   (文件存储)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 核心模块架构
- **路由层 (Routes)**: API端点定义和请求处理
- **服务层 (Services)**: 业务逻辑处理和数据封装
- **数据层 (Data)**: 数据库操作和数据访问
- **模型层 (Models)**: 数据模型定义和验证
- **AI集成层**: 多AI模型集成和智能调度

## 🆕 最新功能更新

### v2.1.0 - 团队管理增强版
- ✅ **完整团队管理系统**: 公司创建、团队管理、成员权限控制
- ✅ **权限系统重构**: 细粒度权限控制，支持动态权限分配
- ✅ **邀请系统优化**: 智能邀请链接，支持未注册用户邀请
- ✅ **数据看板升级**: 实时数据展示，支持团队数据聚合

### v2.0.5 - AI能力增强
- ✅ **多AI模型集成**: 支持Coze、FastGPT、豆包等主流AI模型
- ✅ **智能调度系统**: 根据业务场景自动分配最适合的AI模型
- ✅ **对话风格定制**: 支持多种对话风格和场景模板
- ✅ **产品解析增强**: AI驱动的产品信息自动提取和分类

### v2.0.0 - 架构升级
- ✅ **异步架构重构**: 全面异步化，提升并发性能
- ✅ **微服务化改造**: 模块化设计，支持独立部署
- ✅ **实时通信**: WebSocket集成，支持实时消息推送
- ✅ **日志系统升级**: 五层日志体系，实时日志监控

## 🚀 快速开始：安装与运行

### 1. 准备环境

*   **Python:** 确保已安装 Python (建议版本 3.8 或更高版本) 和 `pip`。
*   **Node.js & npm/yarn:** 用于运行和构建前端管理界面 ( `admin-frontend` )。建议使用最新LTS版本。
*   **MySQL数据库:** 确保有可用的MySQL数据库实例。

### 2. 克隆仓库

```bash
git clone [您的仓库地址]
cd invitation-system-backend
```
(请将 `[您的仓库地址]` 替换为实际的仓库克隆URL)

### 3. 后端配置与运行

#### 3.1 安装Python依赖
```bash
# 建议使用虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt
```

#### 3.2 环境配置
创建 `.env` 文件，配置以下环境变量：

```bash
# 数据库配置
DB_HOST=localhost
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=limob_invitation_system

# JWT认证
JWT_SECRET_KEY=your_super_secret_jwt_key

# 微信支付配置
MCHID=your_merchant_id
PRIVATE_KEY_PATH=path/to/private_key.pem
CERT_SERIAL_NO=your_cert_serial_no
APIV3_KEY=your_apiv3_key
APPID=your_wechat_appid

# 阿里云服务
ALIYUN_ACCESS_KEY_ID=your_access_key_id
ALIYUN_ACCESS_KEY_SECRET=your_access_key_secret
SIGN_NAME=your_sms_sign_name
TEMPLATE_CODE=your_sms_template_code

# AI服务配置
COZE_API_KEY=your_coze_api_key
COZE_PRODUCT_BOT_ID=your_bot_id
FastGPT_Token=your_fastgpt_token
X-APISpace-Token=your_apispace_token

# 腾讯云COS
TENCENT_CLOUD_SECRET_ID=your_secret_id
TENCENT_CLOUD_SECRET_KEY=your_secret_key
TENCENT_CLOUD_REGION=ap-guangzhou
TENCENT_CLOUD_BUCKET=your_bucket_name

# 运行环境
ENVIRONMENT=development  # 或 production
```

#### 3.3 数据库初始化
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE limob_invitation_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 导入数据库结构（如果有SQL文件）
mysql -u root -p limob_invitation_system < database/schema.sql
```

#### 3.4 启动后端服务
```bash
python main.py
```

成功启动后，您将看到类似以下的启动信息：
```
╔══════════════════════════════════════════════════════════════════════════╗
║                              🚀 灵邀系统                                  ║
║                              v2.1.0                                      ║
╠══════════════════════════════════════════════════════════════════════════╣
║                Python 3.8.10  │  FastAPI 0.115.12                       ║
║                ⏱️ 启动时间: 2024-01-01 10:00:00                           ║
╟──────────────────────────────────────────────────────────────────────────╢
║  🌍 环境: 开发环境                                                        ║
║  🖥️ 系统资源: CPU 8核 | 内存占用 125.3MB                                  ║
║  🗃️ 数据库: localhost (limob_invitation_system)                          ║
║  🔄 连接池: 最大连接数 15                                                  ║
║  📊 路由统计: API 180+ 个 | 模块 25+ 个                                   ║
╟──────────────────────────────────────────────────────────────────────────╢
║  🌐 本地访问: http://127.0.0.1:8000                                       ║
║  🔗 局域网访问: http://*************:8000                                  ║
║  📖 API文档: http://127.0.0.1:8000/850483315-docs                        ║
║  📁 工作目录: /path/to/invitation-system-backend                          ║
╟──────────────────────────────────────────────────────────────────────────╢
║                    © 2024 灵能科技 | 保留所有权利                        ║
╚══════════════════════════════════════════════════════════════════════════╝
```

### 4. 前端管理界面 (`admin-frontend`) 配置与运行

*   **进入前端目录:**
    ```bash
    cd admin-frontend
    ```
*   **安装Node.js依赖:**
    ```bash
    npm install 
    # 或者 
    # yarn install
    ```
*   **运行前端开发服务器:**
    ```bash
    npm run dev
    # 或者
    # yarn dev
    ```
    前端服务通常会运行在不同的端口 (例如 `http://localhost:5173`，具体请查看控制台输出)。

*   **构建生产版本 (可选):**
    ```bash
    npm run build
    # 或者
    # yarn build
    ```

## 📖 功能与模块概览

### 后端 (`main.py` 及相关模块)

*   **API 入口 (`main.py`):** FastAPI应用的主入口，负责应用初始化、中间件配置、生命周期管理、IP限速等。
*   **配置 (`config.py`):** 存储数据库、微信支付、JWT、短信服务、AI服务、腾讯云存储等所有敏感配置和应用参数。通过 `.env` 文件加载环境变量。
*   **路由 (`路由/` 目录):** 定义了所有API端点，按功能模块划分，例如：
    *   `用户.py`: 用户注册、登录、信息管理。
    *   `AI对话.py`: 处理与AI模型的交互。
    *   `AI套餐.py`: AI服务套餐管理。
    *   `订单.py`: 订单创建与管理，微信支付回调处理。
    *   `产品解析.py`: AI产品信息解析接口。
    *   `知识库路由.py`: 知识库相关操作。
    *   `样品信息路由.py`: 样品管理。
    *   `达人.py`: KOL/达人信息管理。
    *   `超级管理.py`: 管理员特定操作接口。
    *   `系统.py`, `系统更新.py`: 系统信息、健康检查、更新等。
    *   `微信路由.py`: 处理微信支付通知等。
    *   `外部服务.py`: 可能用于调用第三方API。
    *   `报表路由.py`: 数据报表生成。
    *   `线索路由.py`: 线索管理。
*   **服务 (`服务/` 目录):** 包含核心业务逻辑，被路由层调用。例如：
    *   `异步用户服务.py`: 处理用户认证和业务逻辑。
    *   `异步AI对话服务.py`, `异步AI模型服务.py`: AI相关业务逻辑。
    *   `异步订单服务.py`: 订单处理逻辑。
    *   `腾讯云存储服务.py`: 文件上传下载到腾讯云COS。
*   **数据 (`数据/` 目录):** 负责数据库交互和数据持久化。
    *   `异步连接池.py`: 管理数据库连接池。
    *   `异步数据库函数.py`: 核心异步数据库操作函数。
    *   各模块的数据操作文件 (如 `异步用户产品数据.py`, `达人数据.py` 等)。
*   **数据模型 (`数据模型/` 目录):** 使用Pydantic定义的请求和响应数据模型，用于数据校验和API文档生成。
*   **日志 (`日志/` 目录):** 强大的日志系统，支持多种日志类型（系统、错误、接口、数据库、安全），并提供实时日志查看功能。
    *   `实时日志管理器.py`, `实时日志服务.py`: 实现WebSocket实时日志推送。
*   **AI (`AI/` 目录):**
    *   `COZE.py`: 与Coze AI平台交互的逻辑。
    *   各类提示词文件 (`.md`): 用于指导AI模型生成内容或进行分析，如 `产品信息解析提示词.md`, `智能回复提示词-低级.md`, `全局调度提示词.md` (新增)。
*   **依赖项 (`依赖项/`):** 包含如认证相关的辅助函数。
*   **工具 (`工具/`):** 通用工具函数，如文本转换。
*   **模板 (`模板/`):** Jinja2 HTML模板，用于服务端渲染部分后台管理页面 (如登录、看板、日志查看等)。
*   **静态 (`静态/`):** 存放后端服务直接提供的静态文件 (如果未使用前端框架完全分离)。

### 前端管理界面 (`admin-frontend/`)

*   **入口 (`src/main.js`):** Vue应用初始化，全局注册Ant Design Vue组件，集成Pinia状态管理和Vue Router。
*   **路由 (`src/router/index.js`):** 定义前端页面的路由规则，包括登录页、仪表盘、用户管理、通告管理、日志管理（接口调用、文件查看、实时日志）、系统设置、接口测试平台、批量导入线索等。实现了路由懒加载和全局前置守卫进行权限验证。
*   **布局 (`src/layouts/AdminLayout.vue`):** 后台管理界面的主布局，包含侧边栏导航和内容区域。
*   **视图 (`src/views/`):** 各个页面的Vue组件。
*   **状态管理 (`src/store/index.js` 或 `src/store/user.js`):** 使用Pinia管理全局状态，如用户认证信息。
*   **API服务 (`src/services/` 或直接在组件中使用axios):** 用于与后端API进行交互。

## 📖 API 文档与使用

*   **后端API交互文档:** 启动后端服务后，请访问以下地址在浏览器中查看和测试所有API接口 (URL可能根据 `main.py` 中配置而变化)：
    [http://localhost:8000/docs](http://localhost:8000/docs)  (FastAPI默认路径)
    或项目自定义路径: [http://localhost:8000/850483315-docs](http://localhost:8000/850483315-docs) (根据现有README)

## 🖥️ 后台管理系统访问

*   **管理入口:** 启动前端开发服务器后，通常访问前端服务的根路径，如 `http://localhost:5173` (具体端口看启动日志)，然后会被重定向到登录页或仪表盘。
    *   旧版或部分服务端渲染入口: [http://localhost:8000/limob/admin/login](http://localhost:8000/limob/admin/login) (根据现有README，这部分可能由后端的Jinja2模板提供)
*   **默认凭证:** 默认管理员账号信息请联系系统管理员或查看项目初始化脚本/文档。
*   **核心模块 (通过前端路由访问):**
    *   仪表盘 (`/dashboard`)
    *   用户管理 (`/users`)
    *   通告管理 (`/notifications`)
    *   日志管理 (接口调用 `/logs/apicalls`, 文件查看 `/logs/fileviewer`, 实时日志 `/logs/realtimelog`)
    *   接口测试平台 (`/api-testing`)
    *   批量导入线索 (`/leads/bulk-import`)
    *   系统设置 (`/settings`)

## 📡 实时日志功能 (后台特色)

后台管理系统集成了强大的实时日志功能，管理员可以通过WebSocket实时查看系统运行的各类日志：

*   **支持的日志类型:** 系统日志, 错误日志, 接口日志, 数据库日志, 安全日志。
*   **便捷的筛选与查看:** 按日志类型、级别筛选，日志自动滚动，实时洞察系统状态。
    (通过前端路由 `/logs/realtimelog` 访问)

## 🌳 项目文件结构概览

```
invitation-system-backend/
├── admin-frontend/                    # Vue.js 前端管理界面
│   ├── public/                        # 静态资源
│   ├── src/
│   │   ├── assets/                    # 静态资源 (images, css)
│   │   ├── components/                # 可复用Vue组件
│   │   ├── layouts/                   # 布局组件
│   │   ├── router/                    # 前端路由配置
│   │   ├── services/                  # API服务封装
│   │   ├── store/                     # Pinia状态管理
│   │   ├── views/                     # 页面视图组件
│   │   ├── App.vue                    # 根组件
│   │   └── main.js                    # Vue应用入口
│   ├── package.json                   # 前端依赖配置
│   └── vite.config.js                 # Vite构建配置
├── AI/                                # AI相关模块和提示词
│   ├── COZE.py                        # Coze AI平台集成
│   ├── 全局调度提示词.md               # AI智能调度提示词
│   ├── 产品信息解析提示词.md           # 产品解析AI提示词
│   └── 智能回复提示词-*.md             # 不同场景的AI回复模板
├── 日志/                              # 五层日志系统
│   ├── 文件/                          # 日志文件存储
│   ├── __init__.py                    # 日志模块初始化
│   ├── 实时日志服务.py                # WebSocket实时日志推送
│   └── 实时日志管理器.py              # 日志管理和分发
├── 服务/                              # 核心业务逻辑层
│   ├── 异步用户服务.py                # 用户业务逻辑
│   ├── 异步AI对话服务.py              # AI对话服务
│   ├── 异步订单服务.py                # 订单处理服务
│   ├── 异步样品信息服务.py            # 样品管理服务
│   ├── 腾讯云存储服务.py              # 文件存储服务
│   └── ...                           # 其他业务服务
├── 数据/                              # 数据访问层
│   ├── 缓存/                          # 数据缓存目录
│   ├── 临时文件/                      # 临时文件存储
│   ├── 异步连接池.py                  # 数据库连接池管理
│   ├── 异步数据库函数.py              # 核心数据库操作
│   ├── 团队基础数据.py                # 团队数据操作
│   ├── 团队成员数据.py                # 团队成员数据
│   ├── 团队权限数据.py                # 权限数据操作
│   ├── 公司数据.py                    # 公司数据操作
│   ├── 用户.py                        # 用户数据操作
│   └── ...                           # 其他数据操作模块
├── 数据模型/                          # Pydantic数据模型
│   ├── 响应模型.py                    # 统一响应模型
│   ├── 用户模型.py                    # 用户相关模型
│   ├── 团队模型.py                    # 团队相关模型
│   ├── 团队管理模型.py                # 团队管理模型
│   └── ...                           # 其他业务模型
├── 路由/                              # FastAPI API路由定义
│   ├── 基础路由.py                    # 基础API路由
│   ├── 用户.py                        # 用户管理路由
│   ├── 团队管理.py                    # 团队管理路由
│   ├── 团队权限管理.py                # 团队权限路由
│   ├── 团队邀请管理.py                # 团队邀请路由
│   ├── AI对话.py                      # AI对话路由
│   ├── 样品信息路由.py                # 样品管理路由
│   ├── 订单.py                        # 订单处理路由
│   ├── SuperAdmin_主路由.py           # 超级管理员路由
│   └── ...                           # 其他功能路由
├── 依赖项/                            # 项目依赖和中间件
│   ├── 认证.py                        # JWT认证和权限验证
│   └── ...                           # 其他依赖项
├── 工具/                              # 通用工具函数
│   ├── 团队工具.py                    # 团队相关工具函数
│   ├── 文本转换.py                    # 文本处理工具
│   └── ...                           # 其他工具函数
├── 模板/                              # Jinja2 HTML模板
│   ├── css/                           # 样式文件
│   ├── js/                            # JavaScript文件
│   └── *.html                         # HTML模板文件
├── 静态/                              # 静态文件服务
├── 测试/                              # 测试文件和脚本
│   ├── 团队管理验证脚本.py            # 团队功能测试
│   ├── 团队管理接口测试.py            # 接口测试
│   └── README.md                      # 测试说明
├── 文档/                              # 项目文档
│   ├── 团队管理模块部署指南.md        # 部署指南
│   ├── 团队管理API对接文档.md         # API文档
│   └── ...                           # 其他文档
├── .env                               # 环境变量配置
├── .gitignore                         # Git忽略文件
├── config.py                          # 应用配置文件
├── main.py                            # FastAPI 应用主入口
├── requirements.txt                   # Python依赖列表
├── 状态.py                            # 应用状态管理
└── README.md                          # 项目说明文档
```

### 🔧 关键配置文件说明

| 文件 | 作用 | 重要说明 |
|------|------|----------|
| `main.py` | 应用主入口 | FastAPI应用启动和配置 |
| `config.py` | 配置管理 | 从环境变量加载配置 |
| `.env` | 环境变量 | 敏感信息配置（不提交到Git） |
| `requirements.txt` | Python依赖 | 后端依赖包列表 |
| `admin-frontend/package.json` | 前端依赖 | 前端依赖包和脚本 |

## 最新更新 - 仪表盘功能优化

### 优化内容

#### 1. 前端仪表盘优化
- **数据获取逻辑优化**: 改进了仪表盘数据获取函数，支持多种响应格式，增强了容错性
- **图表数据真实化**: 用户增长图表和API统计图表现在基于真实数据生成，而不是硬编码的示例数据
- **最近活动动态生成**: 最近活动列表现在动态生成，显示更真实的系统活动
- **调试信息面板**: 在开发环境下添加了调试信息面板，方便开发者诊断数据加载问题
- **错误处理增强**: 增加了详细的错误处理和默认值设置，避免页面显示异常

#### 2. 后端API优化
- **统一响应格式**: 修复了仪表盘统计API的响应格式，确保前端能正确解析数据
- **重复API清理**: 删除了重复的仪表盘统计API定义
- **日志增强**: 增加了详细的API调用日志，便于调试和监控

#### 3. 用户登录统计组件优化
- **数据验证增强**: 增加了数据验证和错误处理逻辑
- **调试日志**: 添加了详细的调试日志，帮助诊断数据获取问题
- **默认值处理**: 在API调用失败时提供合理的默认值，避免页面崩溃

#### 4. API服务层优化
- **调试日志**: 在API服务层添加了详细的调试日志
- **错误处理**: 改进了错误处理机制，提供更详细的错误信息
- **方法验证**: 增加了API方法存在性验证，提供可用方法列表

### 功能特性

#### 仪表盘核心功能
1. **实时统计数据**
   - 用户总数
   - 今日新增用户
   - 通知消息数量
   - API调用统计
   - 用户增长趋势
   - API成功率

2. **系统状态监控**
   - CPU使用率
   - 内存使用率
   - 磁盘使用率
   - 在线用户数

3. **可视化图表**
   - 用户增长趋势图（基于真实数据）
   - API调用统计图（分时段显示）
   - 响应式设计，支持多种屏幕尺寸

4. **最近活动**
   - 动态生成的系统活动列表
   - 包含用户注册、系统通知、API调用、用户登录等活动
   - 实时时间显示

5. **用户活跃度统计**
   - 在线用户统计
   - 最近活跃用户
   - 不活跃用户分析
   - 活跃度分布图表
   - 最近登录用户列表

#### 开发者功能
1. **调试信息面板**（仅开发环境）
   - 显示加载状态
   - 错误信息展示
   - 数据结构查看
   - 自动刷新状态

2. **详细日志**
   - API调用日志
   - 数据处理日志
   - 错误追踪日志

### 技术实现

#### 前端技术栈
- Vue 3 + Composition API
- Ant Design Vue
- ECharts 图表库
- Day.js 时间处理
- Axios HTTP客户端

#### 后端技术栈
- FastAPI
- 异步数据库操作
- 统一响应模型
- 详细日志记录

### 使用说明

#### 启动开发环境
```bash
# 前端
cd admin-frontend
npm install
npm run dev

# 后端
python main.py
```

#### 查看调试信息
在开发环境下，仪表盘页面会显示调试信息面板，包含：
- 当前加载状态
- 错误信息（如果有）
- 最后更新时间
- 统计数据和系统状态的JSON格式

#### API调用调试
打开浏览器开发者工具的控制台，可以看到详细的API调用日志：
- API服务层调用日志
- 数据处理过程日志
- 错误详情和堆栈信息

### 故障排除

#### 常见问题
1. **仪表盘数据不显示**
   - 检查后端API是否正常运行
   - 查看浏览器控制台的错误信息
   - 确认API路径配置正确

2. **图表不显示**
   - 确认ECharts库已正确加载
   - 检查图表容器的DOM元素是否存在
   - 查看控制台是否有JavaScript错误

3. **用户统计数据异常**
   - 检查用户列表API是否正常
   - 确认数据库连接正常
   - 查看后端日志中的错误信息

#### 调试步骤
1. 打开浏览器开发者工具
2. 查看控制台日志
3. 检查网络请求状态
4. 查看调试信息面板（开发环境）
5. 检查后端API日志

### 未来计划
- [ ] 添加更多系统监控指标
- [ ] 实现实时数据推送
- [ ] 增加自定义仪表盘配置
- [ ] 添加数据导出功能
- [ ] 优化移动端显示效果

## 🚦 部署指南

### 开发环境部署
```bash
# 1. 克隆项目
git clone [repository-url]
cd invitation-system-backend

# 2. 后端部署
python -m venv venv
source venv/bin/activate  # Linux/Mac
pip install -r requirements.txt
cp .env.example .env  # 并配置环境变量
python main.py

# 3. 前端部署
cd admin-frontend
npm install
npm run dev
```

### 生产环境部署
```bash
# 使用Docker部署 (推荐)
docker-compose up -d

# 或使用传统方式
# 1. 配置Nginx反向代理
# 2. 使用Gunicorn或Uvicorn启动后端
# 3. 构建并部署前端
cd admin-frontend
npm run build
```

### Docker部署 (推荐)
```yaml
# docker-compose.yml
version: '3.8'
services:
  backend:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DB_HOST=mysql
      - ENVIRONMENT=production
    depends_on:
      - mysql
      - redis
  
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=your_password
      - MYSQL_DATABASE=limob_invitation_system
    volumes:
      - mysql_data:/var/lib/mysql
  
  redis:
    image: redis:alpine
    volumes:
      - redis_data:/data

volumes:
  mysql_data:
  redis_data:
```

## 🔒 安全注意事项

### 环境变量安全
- 🔐 **敏感信息保护**: 所有API密钥、数据库密码等敏感信息必须配置在`.env`文件中
- 🚫 **版本控制**: 确保`.env`文件已添加到`.gitignore`，不会提交到版本控制系统
- 🔄 **密钥轮换**: 定期更新JWT密钥和API密钥

### 部署安全
- 🛡️ **HTTPS部署**: 生产环境必须使用HTTPS
- 🔒 **数据库安全**: 使用强密码，限制数据库访问权限
- 🚦 **防火墙配置**: 只开放必要的端口
- 📊 **日志监控**: 启用安全日志监控，及时发现异常

## 🤝 贡献指南

### 开发流程
1. **Fork项目** 并创建功能分支
2. **遵循代码规范** (PEP8 for Python, ESLint for JavaScript)
3. **编写测试** 确保新功能正常工作
4. **更新文档** 包括API文档和README
5. **提交Pull Request** 并描述变更内容

### 代码规范
- **Python**: 遵循PEP8规范，使用类型注解
- **Vue.js**: 使用Vue 3 Composition API，遵循Vue官方风格指南
- **Git提交**: 使用语义化提交信息 (feat, fix, docs, style, refactor, test, chore)

### Bug报告
提交Issue时请包含：
- 🐛 **问题描述**: 详细描述遇到的问题
- 🔍 **复现步骤**: 提供详细的复现步骤
- 💻 **环境信息**: 操作系统、Python版本、Node.js版本等
- 📝 **错误日志**: 相关的错误日志和截图

## 📄 许可证

本项目采用专有许可证，版权归 **灵能科技** 所有。

### 使用限制
- ✅ **内部使用**: 允许在授权范围内使用
- ❌ **商业分发**: 未经许可不得用于商业分发
- ❌ **二次开发**: 未经许可不得进行二次开发和分发
- ❌ **源码泄露**: 严禁泄露源代码

### 技术支持
- 📧 **邮件支持**: <EMAIL>
- 💬 **在线客服**: 工作日 9:00-18:00
- 📚 **文档中心**: [docs.limob.tech](https://docs.limob.tech)

## 🙏 致谢

感谢以下开源项目和技术社区：
- [FastAPI](https://fastapi.tiangolo.com/) - 现代Python Web框架
- [Vue.js](https://vuejs.org/) - 渐进式JavaScript框架
- [Ant Design Vue](https://antdv.com/) - 企业级UI组件库
- [ECharts](https://echarts.apache.org/) - 数据可视化图表库

---

<div align="center">

**© 2024 灵能科技 | 保留所有权利**

[![](https://img.shields.io/badge/Made%20with-❤️-red.svg)](https://github.com/your-username/invitation-system-backend)
[![](https://img.shields.io/badge/Powered%20by-FastAPI-green.svg)](https://fastapi.tiangolo.com)
[![](https://img.shields.io/badge/Frontend-Vue.js-brightgreen.svg)](https://vuejs.org)

</div>

## 最近更新

### 启动信息优化

系统启动信息显示已优化，新增以下功能：

- 增加系统资源信息（CPU核心数、内存使用）
- 增加数据库连接池信息
- 显示本地和局域网访问地址
- 新增启动时间显示
- 新增版权信息
- 添加彩色显示效果
- 优化布局和分隔线
- 显示API路由和模块统计

示例效果：

```
╔══════════════════════════════════════════════════════════════════════════╗
║                              🚀 灵邀系统                                  ║
║                              v1.0.0                                      ║
╠══════════════════════════════════════════════════════════════════════════╣
║                Python 3.13.1  │  FastAPI 0.115.12                        ║
║                ⏱️ 启动时间: 2023-11-01 08:35:55                           ║
╟──────────────────────────────────────────────────────────────────────────╢
║  🌍 环境: 生产环境                                                        ║
║  🖥️ 系统资源: CPU 16核 | 内存占用 75.1MB                                   ║
║  🗃️ 数据库: localhost (limob_invitation)                                  ║
║  🔄 连接池: 最大连接数 15                                                  ║
║  📊 路由统计: API 237 个 | 模块 23 个                                      ║
╟──────────────────────────────────────────────────────────────────────────╢
║  🌐 本地访问: http://127.0.0.1:8000                                       ║
║  🔗 局域网访问: http://*************:8000                                  ║
║  📖 API文档: http://127.0.0.1:8000/850483315-docs                        ║
║  📁 工作目录: G:\code\invitation-system-backend                           ║
╟──────────────────────────────────────────────────────────────────────────╢
║                    © 2023 灵动云科技 | 保留所有权利                        ║
╚══════════════════════════════════════════════════════════════════════════╝
```
