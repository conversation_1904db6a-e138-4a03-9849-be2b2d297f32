{"name": "admin-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "vite --mode development", "build": "vite build --mode production", "build:dev": "vite build --mode development", "preview": "vite preview --mode production"}, "dependencies": {"@ant-design/icons-vue": "^7.0.0", "ant-design-vue": "^4.1.0", "axios": "^1.6.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "highlight.js": "^11.9.0", "json-editor-vue3": "^1.1.1", "pinia": "^2.1.0", "vue": "^3.4.0", "vue-router": "^4.3.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.0", "vite": "^5.2.0"}}