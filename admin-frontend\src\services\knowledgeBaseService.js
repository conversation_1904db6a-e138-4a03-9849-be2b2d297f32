import apiClient from './apiClient'

const API_PREFIX = '/admin/langchain'

/**
 * 知识库管理服务类
 * 提供完整的知识库CRUD操作和高级功能
 */
class KnowledgeBaseService {

  /**
   * 获取知识库列表
   * @param {Object} params - 查询参数
   * @returns {Promise} 知识库列表响应
   */
  async getKnowledgeBaseList(params = {}) {
    try {
      console.log('📚 知识库服务 - 获取知识库列表:', params)

      const 请求数据 = {
        页码: params.页码 || 1,
        每页数量: params.每页数量 || 10,
        搜索关键词: params.搜索关键词 || '',
        知识库类型: params.知识库类型 || '',
        是否公开: params.是否公开
      }

      const response = await apiClient.post(`${API_PREFIX}/knowledge/list`, 请求数据)

      if (response.status === 100) {
        console.log('✅ 知识库服务 - 获取知识库列表成功')
        return {
          success: true,
          data: response.data
        }
      } else {
        console.error('❌ 知识库服务 - 获取知识库列表失败:', response.message)
        return {
          success: false,
          error: response.message || '获取知识库列表失败'
        }
      }
    } catch (error) {
      console.error('❌ 知识库服务 - 获取知识库列表异常:', error)
      return {
        success: false,
        error: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 创建知识库
   * @param {Object} knowledgeBaseData - 知识库数据
   * @returns {Promise} 创建结果
   */
  async createKnowledgeBase(knowledgeBaseData) {
    try {
      console.log('📚 知识库服务 - 创建知识库:', knowledgeBaseData)

      const response = await apiClient.post(`${API_PREFIX}/knowledge/create`, knowledgeBaseData)

      if (response.status === 100) {
        console.log('✅ 知识库服务 - 创建知识库成功')
        return {
          success: true,
          data: response.data
        }
      } else {
        console.error('❌ 知识库服务 - 创建知识库失败:', response.message)
        return {
          success: false,
          error: response.message || '创建知识库失败'
        }
      }
    } catch (error) {
      console.error('❌ 知识库服务 - 创建知识库异常:', error)
      return {
        success: false,
        error: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 获取知识库详情
   * @param {number} knowledgeBaseId - 知识id
   * @returns {Promise} 知识库详情
   */
  async getKnowledgeBaseDetail(knowledgeBaseId) {
    try {
      console.log('📚 知识库服务 - 获取知识库详情:', knowledgeBaseId)

      const 知识id = parseInt(knowledgeBaseId)
      if (isNaN(知识id)) {
        throw new Error('无效的知识id')
      }

      const 请求数据 = {
        知识id: 知识id
      }

      const response = await apiClient.post(`${API_PREFIX}/knowledge/detail`, 请求数据)

      if (response.status === 100) {
        console.log('✅ 知识库服务 - 获取知识库详情成功')
        return {
          success: true,
          data: response.data
        }
      } else {
        console.error('❌ 知识库服务 - 获取知识库详情失败:', response.message)
        return {
          success: false,
          error: response.message || '获取知识库详情失败'
        }
      }
    } catch (error) {
      console.error('❌ 知识库服务 - 获取知识库详情异常:', error)
      return {
        success: false,
        error: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 更新知识库
   * @param {number} knowledgeBaseId - 知识id
   * @param {Object} updateData - 更新数据
   * @returns {Promise} 更新结果
   */
  async updateKnowledgeBase(knowledgeBaseId, updateData) {
    try {
      console.log('📚 知识库服务 - 更新知识库:', knowledgeBaseId, updateData)

      const 知识id = parseInt(knowledgeBaseId)
      if (isNaN(知识id)) {
        throw new Error('无效的知识id')
      }

      const 请求数据 = {
        知识id: 知识id,
        ...updateData
      }

      const response = await apiClient.post(`${API_PREFIX}/knowledge/update`, 请求数据)

      if (response.status === 100) {
        console.log('✅ 知识库服务 - 更新知识库成功')
        return {
          success: true,
          data: response.data
        }
      } else {
        console.error('❌ 知识库服务 - 更新知识库失败:', response.message)
        return {
          success: false,
          error: response.message || '更新知识库失败'
        }
      }
    } catch (error) {
      console.error('❌ 知识库服务 - 更新知识库异常:', error)
      return {
        success: false,
        error: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 删除知识库
   * @param {number} knowledgeBaseId - 知识id
   * @returns {Promise} 删除结果
   */
  async deleteKnowledgeBase(knowledgeBaseId) {
    try {
      console.log('📚 知识库服务 - 删除知识库:', knowledgeBaseId)

      const 请求数据 = {
        知识id: knowledgeBaseId,
        确认删除: true
      }

      const response = await apiClient.post(`${API_PREFIX}/knowledge/delete`, 请求数据)

      if (response.status === 100) {
        console.log('✅ 知识库服务 - 删除知识库成功')
        return {
          success: true,
          data: response.data
        }
      } else {
        console.error('❌ 知识库服务 - 删除知识库失败:', response.message)
        return {
          success: false,
          error: response.message || '删除知识库失败'
        }
      }
    } catch (error) {
      console.error('❌ 知识库服务 - 删除知识库异常:', error)
      return {
        success: false,
        error: error.message || '网络请求失败'
      }
    }
  }

  // 克隆知识库功能已移除 - 如需复制知识库，请创建新知识库并重新上传文档

  /**
   * 向量化知识库
   * @param {number} knowledgeBaseId - 知识id
   * @returns {Promise} 向量化结果
   */
  async vectorizeKnowledgeBase(knowledgeBaseId) {
    try {
      console.log('📚 知识库服务 - 向量化知识库:', knowledgeBaseId)

      const response = await apiClient.post(`${API_PREFIX}/knowledge/${knowledgeBaseId}/vectorize`)

      if (response.status === 100) {
        console.log('✅ 知识库服务 - 向量化知识库成功')
        return {
          success: true,
          data: response.data
        }
      } else {
        console.error('❌ 知识库服务 - 向量化知识库失败:', response.message)
        return {
          success: false,
          error: response.message || '向量化知识库失败'
        }
      }
    } catch (error) {
      console.error('❌ 知识库服务 - 向量化知识库异常:', error)
      return {
        success: false,
        error: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 获取嵌入模型列表
   * @returns {Promise} 嵌入模型列表
   */
  async getEmbeddingModels() {
    try {
      console.log('📚 知识库服务 - 获取嵌入模型列表')

      const response = await apiClient.post(`${API_PREFIX}/knowledge/embedding-models`)

      if (response.status === 100) {
        console.log('✅ 知识库服务 - 获取嵌入模型列表成功')
        return {
          success: true,
          data: response.data || []
        }
      } else {
        console.error('❌ 知识库服务 - 获取嵌入模型列表失败:', response.message)
        return {
          success: false,
          error: response.message || '获取嵌入模型列表失败'
        }
      }
    } catch (error) {
      console.error('❌ 知识库服务 - 获取嵌入模型列表异常:', error)
      return {
        success: false,
        error: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 向量检索
   * @param {number} knowledgeBaseId - 知识id
   * @param {Object} searchParams - 检索参数
   * @returns {Promise} 检索结果
   */
  async vectorSearch(knowledgeBaseId, searchParams) {
    try {
      console.log('🔍 知识库服务 - 向量检索:', knowledgeBaseId, searchParams)

      const 请求数据 = {
        查询文本: searchParams.查询文本 || '',
        最大数量: searchParams.最大数量 || 10,
        检索方式: searchParams.检索方式 || 'postgresql',
        相似度阈值: searchParams.相似度阈值 !== undefined ? searchParams.相似度阈值 : 0.5
      }

      const response = await apiClient.post(`${API_PREFIX}/knowledge/${knowledgeBaseId}/search`, 请求数据)

      if (response.status === 100) {
        console.log('✅ 知识库服务 - 向量检索成功')
        return {
          success: true,
          data: response.data
        }
      } else {
        console.error('❌ 知识库服务 - 向量检索失败:', response.message)
        return {
          success: false,
          error: response.message || '向量检索失败'
        }
      }
    } catch (error) {
      console.error('❌ 知识库服务 - 向量检索异常:', error)
      return {
        success: false,
        error: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 获取向量化状态
   * @param {number} knowledgeBaseId - 知识id
   * @returns {Promise} 向量化状态
   */
  async getVectorStatus(knowledgeBaseId) {
    try {
      console.log('📚 知识库服务 - 获取向量化状态:', knowledgeBaseId)

      const 请求数据 = {
        知识id: knowledgeBaseId
      }

      const response = await apiClient.post(`${API_PREFIX}/knowledge/vector_status`, 请求数据)

      if (response.status === 100) {
        console.log('✅ 知识库服务 - 获取向量化状态成功')
        return {
          success: true,
          data: response.data
        }
      } else {
        console.error('❌ 知识库服务 - 获取向量化状态失败:', response.message)
        return {
          success: false,
          error: response.message || '获取向量化状态失败'
        }
      }
    } catch (error) {
      console.error('❌ 知识库服务 - 获取向量化状态异常:', error)
      return {
        success: false,
        error: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 获取检索配置
   * @param {number} knowledgeBaseId - 知识id
   * @returns {Promise} 检索配置
   */
  async getRetrievalConfig(knowledgeBaseId) {
    try {
      console.log('📚 知识库服务 - 获取检索配置:', knowledgeBaseId)

      const 请求数据 = {
        知识id: knowledgeBaseId
      }

      const response = await apiClient.post(`${API_PREFIX}/knowledge/retrieval_config`, 请求数据)

      if (response.status === 100) {
        console.log('✅ 知识库服务 - 获取检索配置成功')
        return {
          success: true,
          data: response.data
        }
      } else {
        console.error('❌ 知识库服务 - 获取检索配置失败:', response.message)
        return {
          success: false,
          error: response.message || '获取检索配置失败'
        }
      }
    } catch (error) {
      console.error('❌ 知识库服务 - 获取检索配置异常:', error)
      return {
        success: false,
        error: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 更新检索配置
   * @param {number} knowledgeBaseId - 知识id
   * @param {Object} configData - 检索配置数据
   * @returns {Promise} 更新结果
   */
  async updateRetrievalConfig(knowledgeBaseId, configData) {
    try {
      console.log('📚 知识库服务 - 更新检索配置:', knowledgeBaseId, configData)

      const 请求数据 = {
        知识id: knowledgeBaseId,
        ...configData
      }

      const response = await apiClient.post(`${API_PREFIX}/knowledge/retrieval_config/update`, 请求数据)

      if (response.status === 100) {
        console.log('✅ 知识库服务 - 更新检索配置成功')
        return {
          success: true,
          data: response.data
        }
      } else {
        console.error('❌ 知识库服务 - 更新检索配置失败:', response.message)
        return {
          success: false,
          error: response.message || '更新检索配置失败'
        }
      }
    } catch (error) {
      console.error('❌ 知识库服务 - 更新检索配置异常:', error)
      return {
        success: false,
        error: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 测试检索（旧版本，保持向后兼容）
   * @param {number} knowledgeBaseId - 知识id
   * @param {string} queryText - 查询文本
   * @param {Object} retrievalParams - 检索参数
   * @returns {Promise} 检索结果
   */
  async testRetrieval(knowledgeBaseId, queryText, retrievalParams = {}) {
    try {
      console.log('📚 知识库服务 - 测试检索（旧版本）:', knowledgeBaseId, queryText, retrievalParams)

      const 请求数据 = {
        查询文本: queryText,
        嵌入模型id: retrievalParams.嵌入模型id || null,
        检索参数: {
          检索策略: retrievalParams.检索策略 || 'vector',
          相似度阈值: retrievalParams.相似度阈值 !== undefined ? retrievalParams.相似度阈值 : 0.5,
          最大检索数量: retrievalParams.最大检索数量 || 10,
          分块大小: retrievalParams.分块大小 || 1000,
          分块重叠: retrievalParams.分块重叠 || 200,
          分块策略: retrievalParams.分块策略 || 'recursive'
        }
      }

      console.log('📤 发送检索测试请求（旧版本）:', 请求数据)

      const response = await apiClient.post(`${API_PREFIX}/knowledge/${knowledgeBaseId}/test_retrieval`, 请求数据)

      if (response.status === 100) {
        console.log('✅ 知识库服务 - 测试检索成功（旧版本）')
        return {
          success: true,
          data: response.data
        }
      } else {
        console.error('❌ 知识库服务 - 测试检索失败（旧版本）:', response.message)
        return {
          success: false,
          error: response.message || '测试检索失败'
        }
      }
    } catch (error) {
      console.error('❌ 知识库服务 - 测试检索异常（旧版本）:', error)
      return {
        success: false,
        error: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 知识库检索测试（新版本） - 支持多知识库和完整参数配置
   * @param {Array} knowledgeBaseIds - 知识id列表
   * @param {string} testQuery - 测试查询文本
   * @param {Object} retrievalConfig - 检索配置参数
   * @param {Object} queryOptimizationConfig - 查询优化配置参数（可选）
   * @param {string} testMode - 测试模式（standard/debug）
   * @returns {Promise} 检索结果
   */
  async testRetrievalNew(knowledgeBaseIds, testQuery, retrievalConfig, queryOptimizationConfig = null, testMode = 'standard') {
    try {
      console.log('📚 知识库服务 - 知识库检索测试（新版本）:', {
        knowledgeBaseIds,
        testQuery,
        retrievalConfig,
        queryOptimizationConfig,
        testMode
      })

      // 验证必需参数
      if (!Array.isArray(knowledgeBaseIds) || knowledgeBaseIds.length === 0) {
        throw new Error('知识id列表不能为空')
      }

      if (!testQuery || testQuery.trim().length === 0) {
        throw new Error('测试查询文本不能为空')
      }

      if (!retrievalConfig) {
        throw new Error('检索配置参数不能为空')
      }

      // 验证检索配置必需字段
      const requiredFields = ['检索策略', '嵌入模型', '相似度阈值', '最大检索数量']
      for (const field of requiredFields) {
        if (!(field in retrievalConfig)) {
          throw new Error(`检索配置缺少必需字段: ${field}`)
        }
      }

      const 请求数据 = {
        知识库列表: knowledgeBaseIds,
        测试查询: testQuery.trim(),
        检索配置: {
          检索策略: retrievalConfig.检索策略 || 'vector',
          嵌入模型: retrievalConfig.嵌入模型,
          相似度阈值: parseFloat(retrievalConfig.相似度阈值),
          最大检索数量: parseInt(retrievalConfig.最大检索数量),
          分块大小: parseInt(retrievalConfig.分块大小 || 1000),
          分块重叠: parseInt(retrievalConfig.分块重叠 || 200),
          分块策略: retrievalConfig.分块策略 || 'recursive'
        },
        查询优化配置: queryOptimizationConfig,
        测试模式: testMode
      }

      console.log('📤 发送知识库检索测试请求（新版本）:', 请求数据)

      const response = await apiClient.post(`${API_PREFIX}/knowledge/test_retrieval`, 请求数据)

      if (response.status === 100) {
        console.log('✅ 知识库服务 - 知识库检索测试成功（新版本）')
        return {
          success: true,
          data: response.data
        }
      } else {
        console.error('❌ 知识库服务 - 知识库检索测试失败（新版本）:', response.message)
        return {
          success: false,
          error: response.message || '知识库检索测试失败'
        }
      }
    } catch (error) {
      console.error('❌ 知识库服务 - 知识库检索测试异常（新版本）:', error)
      return {
        success: false,
        error: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 获取向量模型列表
   * @returns {Promise} 向量模型列表
   */
  async getVectorModels() {
    try {
      console.log('📚 知识库服务 - 获取向量模型列表')

      // 后端接口是GET方法，不需要请求体
      const response = await apiClient.get(`${API_PREFIX}/model-providers/embedding-models`)

      if (response.status === 100) {
        console.log('✅ 知识库服务 - 获取向量模型列表成功')
        return {
          success: true,
          data: response.data
        }
      } else {
        console.error('❌ 知识库服务 - 获取向量模型列表失败:', response.message)
        return {
          success: false,
          error: response.message || '获取向量模型列表失败'
        }
      }
    } catch (error) {
      console.error('❌ 知识库服务 - 获取向量模型列表异常:', error)
      return {
        success: false,
        error: error.message || '网络请求失败'
      }
    }
  }

  // 清空向量数据功能已移除 - 可通过重新向量化替代，避免误操作

  // ==================== 文档管理功能 ====================

  /**
   * 获取知识库文档列表
   * @param {number} knowledgeBaseId - 知识id
   * @param {Object} params - 查询参数
   * @returns {Promise} 文档列表响应
   */
  async getDocumentList(knowledgeBaseId, params = {}) {
    try {
      console.log('📄 知识库服务 - 获取文档列表:', knowledgeBaseId, params)

      const 请求数据 = {
        页码: params.页码 || 1,
        每页数量: params.每页数量 || 20,
        搜索关键字: params.搜索关键字 || '',
        文件类型: params.文件类型 || '',
        处理状态: params.处理状态 || ''
      }

      const response = await apiClient.post(`${API_PREFIX}/knowledge/${knowledgeBaseId}/documents/list`, 请求数据)

      if (response.status === 100) {
        console.log('✅ 知识库服务 - 获取文档列表成功')
        return {
          success: true,
          data: response.data
        }
      } else {
        console.error('❌ 知识库服务 - 获取文档列表失败:', response.message)
        return {
          success: false,
          error: response.message || '获取文档列表失败'
        }
      }
    } catch (error) {
      console.error('❌ 知识库服务 - 获取文档列表异常:', error)
      return {
        success: false,
        error: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 上传文档到知识库
   * @param {number} knowledgeBaseId - 知识id
   * @param {Array} files - 文件列表
   * @param {Object} options - 上传选项
   * @returns {Promise} 上传结果
   */
  async uploadDocuments(knowledgeBaseId, files, options = {}) {
    try {
      console.log('📄 知识库服务 - 上传文档:', knowledgeBaseId, files.length, options)

      const formData = new FormData()

      // 添加文件
      files.forEach(file => {
        formData.append('files', file)
      })

      // 添加选项
      formData.append('auto_process', options.auto_process || 'true')
      formData.append('chunk_strategy', options.chunk_strategy || 'recursive')
      formData.append('chunk_size', options.chunk_size || 2000)
      formData.append('chunk_overlap', options.chunk_overlap || 400)

      // 添加嵌入模型配置
      if (options.embedding_model) {
        formData.append('embedding_model', options.embedding_model)
      }

      const response = await apiClient.post(
        `${API_PREFIX}/knowledge/${knowledgeBaseId}/documents/upload`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          timeout: 1800000, // 30分钟超时 (大文件处理需要更长时间)
          onUploadProgress: (progressEvent) => {
            // 调用进度回调
            if (options.onProgress) {
              options.onProgress({
                loaded: progressEvent.loaded,
                total: progressEvent.total,
                rate: progressEvent.rate
              })
            }
          }
        }
      )

      if (response.status === 100) {
        console.log('✅ 知识库服务 - 上传文档成功')
        return {
          success: true,
          data: response.data
        }
      } else {
        console.error('❌ 知识库服务 - 上传文档失败:', response.message)
        return {
          success: false,
          error: response.message || '上传文档失败'
        }
      }
    } catch (error) {
      console.error('❌ 知识库服务 - 上传文档异常:', error)
      return {
        success: false,
        error: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 删除文档
   * @param {string} documentId - 文档ID
   * @returns {Promise} 删除结果
   */
  async deleteDocument(documentId) {
    try {
      console.log('📄 知识库服务 - 删除文档:', documentId)

      const response = await apiClient.post(`${API_PREFIX}/knowledge/documents/${documentId}/delete`)

      if (response.status === 100) {
        console.log('✅ 知识库服务 - 删除文档成功')
        return {
          success: true,
          data: response.data
        }
      } else {
        console.error('❌ 知识库服务 - 删除文档失败:', response.message)
        return {
          success: false,
          error: response.message || '删除文档失败'
        }
      }
    } catch (error) {
      console.error('❌ 知识库服务 - 删除文档异常:', error)
      return {
        success: false,
        error: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 批量删除文档
   * @param {Array} documentIds - 文档ID列表
   * @returns {Promise} 批量删除结果
   */
  async batchDeleteDocuments(documentIds) {
    try {
      console.log('📄 知识库服务 - 批量删除文档:', documentIds)

      const results = []
      let successCount = 0
      let failCount = 0

      for (const id of documentIds) {
        try {
          const result = await this.deleteDocument(id)
          results.push({ 文档ID: id, ...result })
          if (result.success) {
            successCount++
          } else {
            failCount++
          }
        } catch (error) {
          results.push({ 文档ID: id, success: false, error: error.message })
          failCount++
        }
      }

      console.log(`✅ 知识库服务 - 批量删除文档完成: 成功 ${successCount}, 失败 ${failCount}`)

      return {
        success: true,
        data: {
          成功数量: successCount,
          失败数量: failCount,
          总数量: documentIds.length,
          详细结果: results
        }
      }
    } catch (error) {
      console.error('❌ 知识库服务 - 批量删除文档异常:', error)
      return {
        success: false,
        error: error.message || '批量删除文档失败'
      }
    }
  }

  /**
   * 批量删除知识库
   * @param {Array} knowledgeBaseIds - 知识id列表
   * @returns {Promise} 批量删除结果
   */
  async batchDeleteKnowledgeBases(knowledgeBaseIds) {
    try {
      console.log('📚 知识库服务 - 批量删除知识库:', knowledgeBaseIds)

      const results = []
      let successCount = 0
      let failCount = 0

      for (const id of knowledgeBaseIds) {
        try {
          const result = await this.deleteKnowledgeBase(id)
          results.push({ 知识id: id, ...result })
          if (result.success) {
            successCount++
          } else {
            failCount++
          }
        } catch (error) {
          results.push({ 知识id: id, success: false, error: error.message })
          failCount++
        }
      }

      console.log(`✅ 知识库服务 - 批量删除完成: 成功 ${successCount}, 失败 ${failCount}`)

      return {
        success: true,
        data: {
          成功数量: successCount,
          失败数量: failCount,
          总数量: knowledgeBaseIds.length,
          详细结果: results
        }
      }
    } catch (error) {
      console.error('❌ 知识库服务 - 批量删除异常:', error)
      return {
        success: false,
        error: error.message || '批量删除失败'
      }
    }
  }

  /**
   * 获取知识库统计信息
   * @returns {Promise} 统计信息
   */
  async getKnowledgeBaseStats() {
    try {
      console.log('📚 知识库服务 - 获取统计信息')

      // 获取知识库列表来计算统计信息
      const listResult = await this.getKnowledgeBaseList({ 每页数量: 1000 })

      if (listResult.success) {
        const 知识库列表 = listResult.data.知识库列表 || []

        const 统计信息 = {
          总数量: 知识库列表.length,
          活跃数量: 知识库列表.filter(kb => kb.状态 === 'active').length,
          处理中数量: 知识库列表.filter(kb => kb.状态 === 'processing').length,
          错误数量: 知识库列表.filter(kb => kb.状态 === 'error').length,
          总文档数: 知识库列表.reduce((sum, kb) => sum + (kb.文档数量 || 0), 0),
          总向量数: 知识库列表.reduce((sum, kb) => sum + (kb.向量数量 || 0), 0)
        }

        console.log('✅ 知识库服务 - 获取统计信息成功')
        return {
          success: true,
          data: 统计信息
        }
      } else {
        return listResult
      }
    } catch (error) {
      console.error('❌ 知识库服务 - 获取统计信息异常:', error)
      return {
        success: false,
        error: error.message || '获取统计信息失败'
      }
    }
  }
}

// 创建服务实例
const knowledgeBaseService = new KnowledgeBaseService()

export default knowledgeBaseService

// 导出服务类用于依赖注入
export { KnowledgeBaseService }

