/**
 * LangGraph服务
 * 处理LangGraph相关的API调用
 */

import apiClient from './apiClient'

class LangGraphService {
  constructor() {
    this.baseURL = '/api/v1/langgraph'
  }

  /**
   * 流式对话
   * @param {Object} data - 对话数据
   * @param {string} data.message - 用户消息
   * @param {number} data.agent_id - 智能体id
   * @param {string} [data.session_id] - 会话ID
   * @param {string} [data.thread_id] - 线程ID
   * @param {string} [data.agent_type] - 智能体类型
   * @param {boolean} [data.stream] - 是否流式响应
   */
  async streamChat(data) {
    try {
      const response = await apiClient.post(`${this.baseURL}/chat/stream`, data)
      return response.data
    } catch (error) {
      console.error('流式对话失败:', error)
      throw error
    }
  }

  /**
   * 流式对话（SSE方式）
   * @param {Object} data - 对话数据
   * @param {Function} onMessage - 消息回调
   * @param {Function} onError - 错误回调
   * @param {Function} onComplete - 完成回调
   */
  async streamChatSSE(data, onMessage, onError, onComplete) {
    try {
      const url = `${apiClient.defaults.baseURL}${this.baseURL}/chat/stream`
      const token = localStorage.getItem('token')
      
      const eventSource = new EventSource(`${url}?${new URLSearchParams({
        ...data,
        token
      })}`)

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          onMessage && onMessage(data)
          
          if (data.type === 'end' || data.type === 'complete') {
            eventSource.close()
            onComplete && onComplete(data)
          }
        } catch (error) {
          console.error('解析SSE消息失败:', error)
          onError && onError(error)
        }
      }

      eventSource.onerror = (error) => {
        console.error('SSE连接错误:', error)
        eventSource.close()
        onError && onError(error)
      }

      return eventSource
    } catch (error) {
      console.error('创建SSE连接失败:', error)
      onError && onError(error)
      throw error
    }
  }

  /**
   * 获取工具调用监控
   * @param {Object} data - 查询参数
   * @param {string} data.thread_id - 线程ID
   * @param {number} [data.limit] - 限制数量
   */
  async getToolMonitor(data) {
    try {
      const response = await apiClient.post(`${this.baseURL}/tools/monitor`, data)
      return response.data
    } catch (error) {
      console.error('获取工具监控失败:', error)
      throw error
    }
  }

  /**
   * 查询状态
   * @param {Object} data - 查询参数
   * @param {string} data.thread_id - 线程ID
   * @param {boolean} [data.include_history] - 是否包含历史
   */
  async queryState(data) {
    try {
      const response = await apiClient.post(`${this.baseURL}/state/query`, data)
      return response.data
    } catch (error) {
      console.error('查询状态失败:', error)
      throw error
    }
  }

  /**
   * 获取智能体统计
   */
  async getStats() {
    try {
      const response = await apiClient.get(`${this.baseURL}/agents/stats`)
      return response.data
    } catch (error) {
      console.error('获取智能体统计失败:', error)
      throw error
    }
  }

  /**
   * 清理缓存
   */
  async clearCache() {
    try {
      const response = await apiClient.delete(`${this.baseURL}/cache/clear`)
      return response.data
    } catch (error) {
      console.error('清理缓存失败:', error)
      throw error
    }
  }

  /**
   * 获取对话历史
   * @param {string} threadId - 线程ID
   * @param {number} [limit] - 限制数量
   */
  async getChatHistory(threadId, limit = 50) {
    try {
      const response = await apiClient.get(`${this.baseURL}/chat/history`, {
        params: { thread_id: threadId, limit }
      })
      return response.data
    } catch (error) {
      console.error('获取对话历史失败:', error)
      throw error
    }
  }

  /**
   * 获取线程列表
   * @param {number} [userId] - 用户id
   * @param {number} [agentId] - 智能体id
   * @param {number} [limit] - 限制数量
   */
  async getThreads(userId, agentId, limit = 20) {
    try {
      const response = await apiClient.get(`${this.baseURL}/threads`, {
        params: { user_id: userId, agent_id: agentId, limit }
      })
      return response.data
    } catch (error) {
      console.error('获取线程列表失败:', error)
      throw error
    }
  }

  /**
   * 删除线程
   * @param {string} threadId - 线程ID
   */
  async deleteThread(threadId) {
    try {
      const response = await apiClient.delete(`${this.baseURL}/threads/${threadId}`)
      return response.data
    } catch (error) {
      console.error('删除线程失败:', error)
      throw error
    }
  }

  /**
   * 获取性能指标
   * @param {string} [timeRange] - 时间范围
   */
  async getPerformanceMetrics(timeRange = '24h') {
    try {
      const response = await apiClient.get(`${this.baseURL}/metrics/performance`, {
        params: { time_range: timeRange }
      })
      return response.data
    } catch (error) {
      console.error('获取性能指标失败:', error)
      throw error
    }
  }

  /**
   * 获取工具使用统计
   * @param {string} [timeRange] - 时间范围
   */
  async getToolUsageStats(timeRange = '24h') {
    try {
      const response = await apiClient.get(`${this.baseURL}/metrics/tools`, {
        params: { time_range: timeRange }
      })
      return response.data
    } catch (error) {
      console.error('获取工具使用统计失败:', error)
      throw error
    }
  }

  /**
   * 导出对话数据
   * @param {string} threadId - 线程ID
   * @param {string} [format] - 导出格式
   */
  async exportChatData(threadId, format = 'json') {
    try {
      const response = await apiClient.get(`${this.baseURL}/export/chat`, {
        params: { thread_id: threadId, format },
        responseType: 'blob'
      })
      return response.data
    } catch (error) {
      console.error('导出对话数据失败:', error)
      throw error
    }
  }

  /**
   * 重置智能体状态
   * @param {number} agentId - 智能体id
   */
  async resetAgentState(agentId) {
    try {
      const response = await apiClient.post(`${this.baseURL}/agents/${agentId}/reset`)
      return response.data
    } catch (error) {
      console.error('重置智能体状态失败:', error)
      throw error
    }
  }

  /**
   * 获取智能体配置
   * @param {number} agentId - 智能体id
   */
  async getAgentConfig(agentId) {
    try {
      const response = await apiClient.get(`${this.baseURL}/agents/${agentId}/config`)
      return response.data
    } catch (error) {
      console.error('获取智能体配置失败:', error)
      throw error
    }
  }

  /**
   * 更新智能体配置
   * @param {number} agentId - 智能体id
   * @param {Object} config - 配置数据
   */
  async updateAgentConfig(agentId, config) {
    try {
      const response = await apiClient.put(`${this.baseURL}/agents/${agentId}/config`, config)
      return response.data
    } catch (error) {
      console.error('更新智能体配置失败:', error)
      throw error
    }
  }

  /**
   * 测试智能体连接
   * @param {number} agentId - 智能体id
   */
  async testAgentConnection(agentId) {
    try {
      const response = await apiClient.post(`${this.baseURL}/agents/${agentId}/test`)
      return response.data
    } catch (error) {
      console.error('测试智能体连接失败:', error)
      throw error
    }
  }

  /**
   * 获取系统健康状态
   */
  async getSystemHealth() {
    try {
      const response = await apiClient.get(`${this.baseURL}/system/health`)
      return response.data
    } catch (error) {
      console.error('获取系统健康状态失败:', error)
      throw error
    }
  }
}

// 创建并导出服务实例
export const langGraphService = new LangGraphService()
export default langGraphService
