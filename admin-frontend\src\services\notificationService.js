import apiClient from './apiClient';

const API_PREFIX = '/admin/announcements';

/**
 * 通知服务类
 */
class NotificationService {
  /**
   * 获取通告列表 (分页、筛选、排序)
   * @param {object} params - 查询参数，例如 { page, size, 标题, 类型, 状态, 创建时间开始, 创建时间结束, 排序字段, 排序顺序 }
   * @returns {Promise}
   */
  async getNotificationList(params) {
    try {
      const response = await apiClient.post(`${API_PREFIX}/list`, params);
      return response.data;
    } catch (error) {
      console.error('获取通告列表失败:', error);
      throw error;
    }
  }

  /**
   * 创建新通告
   * @param {object} data - 通告数据，例如 { 类型, 标题, 内容, 已发布, 重要性, 开始时间, 结束时间, 排序 }
   * @returns {Promise}
   */
  async createNotification(data) {
    try {
      const response = await apiClient.post(API_PREFIX, data);
      return response.data;
    } catch (error) {
      console.error('创建通告失败:', error);
      throw error;
    }
  }

  /**
   * 更新现有通告
   * @param {number|string} id - 通告ID
   * @param {object} data - 需要更新的通告数据
   * @returns {Promise}
   */
  async updateNotification(id, data) {
    try {
      const response = await apiClient.put(`${API_PREFIX}/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('更新通告失败:', error);
      throw error;
    }
  }

  /**
   * 删除指定ID的通告
   * @param {number|string} id - 通告ID
   * @returns {Promise}
   */
  async deleteNotification(id) {
    try {
      const response = await apiClient.delete(`${API_PREFIX}/${id}`);
      return response.data;
    } catch (error) {
      console.error('删除通告失败:', error);
      throw error;
    }
  }
}

// 创建并导出服务实例
const notificationService = new NotificationService();
export default notificationService;