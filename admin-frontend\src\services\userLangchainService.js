/**
 * 用户端 - LangChain智能体服务
 * 提供用户使用智能体的相关功能
 */

import apiClient from './apiClient'
import * as LangChainUtils from '../utils/langchainUtils'

class UserLangChainService {
  // ==================== 智能体使用 ====================
  
  /**
   * 获取用户可用智能体列表
   * @param {Object} 查询参数 - 分页和过滤参数
   * @returns {Promise} API响应
   */
  async 获取用户可用智能体列表(查询参数 = {}) {
    try {
      const 默认参数 = {
        页码: 1,
        每页数量: 20,
        智能体类型: null,
        搜索关键词: null
      }
      
      const 最终参数 = { ...默认参数, ...查询参数 }
      const response = await apiClient.post('/user/langchain/agents/available', 最终参数)
      return response.data
    } catch (error) {
      console.error('获取可用智能体列表失败:', error)
      throw error
    }
  }

  /**
   * 智能体对话
   * @param {string} 智能体id - 智能体id
   * @param {Object} 消息数据 - 消息内容和配置
   * @returns {Promise} API响应
   */
  async 智能体对话(智能体id, 消息数据) {
    try {
      const 请求数据 = { 智能体id, ...消息数据 }
      const response = await apiClient.post('/user/langchain/agents/chat', 请求数据)
      return response
    } catch (error) {
      console.error('智能体对话失败:', error)
      throw error
    }
  }

  /**
   * 获取对话历史
   * @param {Object} 查询参数 - 查询条件
   * @returns {Promise} API响应
   */
  async 获取对话历史(查询参数 = {}) {
    try {
      const 默认参数 = {
        页码: 1,
        每页数量: 50,
        智能体id: null,
        会话id: null,
        开始时间: null,
        结束时间: null
      }
      
      const 最终参数 = { ...默认参数, ...查询参数 }
      const response = await apiClient.post('/user/langchain/chat/history', 最终参数)
      return response.data
    } catch (error) {
      console.error('获取对话历史失败:', error)
      throw error
    }
  }

  /**
   * 清空智能体记忆
   * @param {string} 智能体id - 智能体id
   * @param {string} 会话id - 可选的会话ID
   * @returns {Promise} API响应
   */
  async 清空智能体记忆(智能体id, 会话id = null) {
    try {
      const 请求数据 = { 智能体id }
      if (会话id) {
        请求数据.会话id = 会话id
      }
      const response = await apiClient.post('/user/langchain/agents/clear-memory', 请求数据)
      return response.data
    } catch (error) {
      console.error('清空智能体记忆失败:', error)
      throw error
    }
  }

  /**
   * 获取我的智能体使用统计
   * @returns {Promise} API响应
   */
  async 获取我的智能体使用统计() {
    try {
      const response = await apiClient.post('/user/langchain/statistics/my-usage')
      return response.data
    } catch (error) {
      console.error('获取使用统计失败:', error)
      throw error
    }
  }

  /**
   * 获取智能体详情（用户视图）
   * @param {string} 智能体id - 智能体id
   * @returns {Promise} API响应
   */
  async 获取智能体详情(智能体id) {
    try {
      const response = await apiClient.post(`/user/langchain/agents/${智能体id}/detail`)
      return response.data
    } catch (error) {
      console.error('获取智能体详情失败:', error)
      throw error
    }
  }

  // ==================== 会话管理 ====================

  /**
   * 创建新会话
   * @param {string} 智能体id - 智能体id
   * @param {Object} 会话配置 - 会话配置参数
   * @returns {Promise} API响应
   */
  async 创建会话(智能体id, 会话配置 = {}) {
    try {
      const response = await apiClient.post(`/user/langchain/agents/${智能体id}/sessions/create`, 会话配置)
      return response.data
    } catch (error) {
      console.error('创建会话失败:', error)
      throw error
    }
  }

  /**
   * 获取会话列表
   * @param {string} 智能体id - 智能体id
   * @returns {Promise} API响应
   */
  async 获取会话列表(智能体id) {
    try {
      const response = await apiClient.post(`/user/langchain/agents/${智能体id}/sessions`)
      return response.data
    } catch (error) {
      console.error('获取会话列表失败:', error)
      throw error
    }
  }

  /**
   * 删除会话
   * @param {string} 智能体id - 智能体id
   * @param {string} 会话id - 会话ID
   * @returns {Promise} API响应
   */
  async 删除会话(智能体id, 会话id) {
    try {
      const response = await apiClient.post(`/user/langchain/agents/${智能体id}/sessions/${会话id}/delete`)
      return response.data
    } catch (error) {
      console.error('删除会话失败:', error)
      throw error
    }
  }

  // ==================== 收藏和评价 ====================

  /**
   * 收藏智能体
   * @param {string} 智能体id - 智能体id
   * @returns {Promise} API响应
   */
  async 收藏智能体(智能体id) {
    try {
      const response = await apiClient.post(`/user/langchain/agents/${智能体id}/favorite`)
      return response.data
    } catch (error) {
      console.error('收藏智能体失败:', error)
      throw error
    }
  }

  /**
   * 取消收藏智能体
   * @param {string} 智能体id - 智能体id
   * @returns {Promise} API响应
   */
  async 取消收藏智能体(智能体id) {
    try {
      const response = await apiClient.post(`/user/langchain/agents/${智能体id}/unfavorite`)
      return response.data
    } catch (error) {
      console.error('取消收藏智能体失败:', error)
      throw error
    }
  }

  /**
   * 评价智能体
   * @param {string} 智能体id - 智能体id
   * @param {Object} 评价数据 - 评分和评论
   * @returns {Promise} API响应
   */
  async 评价智能体(智能体id, 评价数据) {
    try {
      const response = await apiClient.post(`/user/langchain/agents/${智能体id}/rate`, 评价数据)
      return response.data
    } catch (error) {
      console.error('评价智能体失败:', error)
      throw error
    }
  }

  /**
   * 获取我的收藏列表
   * @returns {Promise} API响应
   */
  async 获取我的收藏列表() {
    try {
      const response = await apiClient.post('/user/langchain/favorites')
      return response.data
    } catch (error) {
      console.error('获取收藏列表失败:', error)
      throw error
    }
  }

  // ==================== 导出和分享 ====================

  /**
   * 导出对话记录
   * @param {Object} 导出参数 - 导出配置
   * @returns {Promise} API响应
   */
  async 导出对话记录(导出参数) {
    try {
      const response = await apiClient.post('/user/langchain/chat/export', 导出参数, {
        responseType: 'blob'
      })
      return response
    } catch (error) {
      console.error('导出对话记录失败:', error)
      throw error
    }
  }

  /**
   * 分享对话
   * @param {string} 对话id - 对话记录ID
   * @param {Object} 分享配置 - 分享设置
   * @returns {Promise} API响应
   */
  async 分享对话(对话id, 分享配置) {
    try {
      const response = await apiClient.post(`/user/langchain/chat/${对话id}/share`, 分享配置)
      return response.data
    } catch (error) {
      console.error('分享对话失败:', error)
      throw error
    }
  }

  // ==================== 工具方法 ====================
  // 注意：工具方法已迁移到 langchainUtils.js 统一管理，避免重复代码
  // 可通过 LangChainUtils.格式化智能体类型() 等方式调用




}

// 创建并导出服务实例
const userLangchainService = new UserLangChainService()
export default userLangchainService
