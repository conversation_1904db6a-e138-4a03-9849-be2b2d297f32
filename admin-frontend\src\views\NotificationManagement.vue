<template>
  <div class="notification-management">
    <!-- 页面标题卡片 -->
    <a-card title="通告管理" :bordered="false" class="header-card">
      <!-- 搜索筛选区域 -->
      <a-form
        layout="inline"
        :model="searchFormState"
        @finish="handleSearch"
        class="search-form"
      >
        <a-form-item label="标题" name="title">
          <a-input 
            v-model:value="searchFormState.title" 
            placeholder="请输入通告标题" 
            allow-clear
            style="width: 200px;"
          />
        </a-form-item>
        
        <a-form-item label="类型" name="type">
          <a-select 
            v-model:value="searchFormState.type" 
            placeholder="请选择通告类型" 
            style="width: 120px" 
            allow-clear
          >
            <a-select-option value="通知">通知</a-select-option>
            <a-select-option value="公告">公告</a-select-option>
            <a-select-option value="警告">警告</a-select-option>
            <a-select-option value="系统维护">系统维护</a-select-option>
            <a-select-option value="版本更新">版本更新</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="状态" name="status">
          <a-select 
            v-model:value="searchFormState.status" 
            placeholder="请选择状态" 
            style="width: 120px" 
            allow-clear
          >
            <a-select-option :value="1">已发布</a-select-option>
            <a-select-option :value="0">草稿</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="重要性" name="importance">
          <a-select 
            v-model:value="searchFormState.importance" 
            placeholder="请选择重要性" 
            style="width: 120px" 
            allow-clear
          >
            <a-select-option :value="1">普通</a-select-option>
            <a-select-option :value="2">重要</a-select-option>
            <a-select-option :value="3">紧急</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="创建时间" name="createTimeRange">
          <a-range-picker 
            v-model:value="searchFormState.createTimeRange" 
            style="width: 300px;"
            :placeholder="['开始时间', '结束时间']"
          />
        </a-form-item>
        
        <a-form-item>
          <a-space>
            <a-button type="primary" html-type="submit" :loading="loading">
              <template #icon><SearchOutlined /></template>
              查询
            </a-button>
            <a-button @click="resetSearchForm">
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>

      <!-- 操作按钮区域 -->
      <div class="action-buttons">
        <a-space>
          <router-link to="/announcements/create">
            <a-button type="primary">
              <template #icon><PlusOutlined /></template>
              新增通告
            </a-button>
          </router-link>
          <a-button @click="refreshData" :loading="loading">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>

      <!-- 通告数据表格 -->
      <a-table
        :columns="columns"
        :row-key="record => record.id"
        :data-source="dataSource"
        :pagination="pagination"
        :loading="loading"
        @change="handleTableChange"
        bordered
        size="middle"
        :scroll="{ x: 1500 }"
        class="notification-table"
      >
        <!-- 自定义渲染列 -->
        <template #bodyCell="{ column, record }">
          <!-- 操作列 -->
          <template v-if="column.key === '操作'">
            <a-space>
              <router-link :to="`/announcements/edit/${record.id}`">
                <a-button type="link" size="small">
                  <template #icon><EditOutlined /></template>
                  编辑
                </a-button>
              </router-link>
              
              <a-popconfirm
                title="确定要删除此通告吗?"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record.id)"
                placement="topRight"
              >
                <a-button type="link" danger size="small">
                  <template #icon><DeleteOutlined /></template>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
          
          <!-- 状态列 -->
          <template v-else-if="column.key === '状态'">
            <a-tag :color="record.已发布 === 1 ? 'green' : 'orange'">
              {{ record.已发布 === 1 ? '已发布' : '草稿' }}
            </a-tag>
          </template>
          
          <!-- 重要性列 -->
          <template v-else-if="column.key === '重要性'">
            <a-tag :color="getImportanceColor(record.重要性)">
              {{ getImportanceText(record.重要性) }}
            </a-tag>
          </template>
          
          <!-- 内容摘要列 -->
          <template v-else-if="column.key === '内容'">
            <div class="content-preview">
              <a-tooltip :title="getContentPreview(record.内容)" placement="topLeft">
                <div v-html="getContentPreview(record.内容)" class="content-text"></div>
              </a-tooltip>
            </div>
          </template>
          
          <!-- 类型列 -->
          <template v-else-if="column.key === '类型'">
            <a-tag :color="getTypeColor(record.类型)">
              {{ record.类型 || '未分类' }}
            </a-tag>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑通告弹窗 -->
    <a-modal
      :title="modalTitle"
      v-model:open="modalVisible"
      :confirm-loading="modalConfirmLoading"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      destroyOnClose
      width="900px"
      :maskClosable="false"
    >
      <a-form
        ref="modalFormRef"
        :model="modalFormState"
        layout="vertical"
        name="notificationModalForm"
        :rules="formRules"
      >
        <!-- 基本信息行 -->
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              name="标题"
              label="通告标题"
              :rules="[{ required: true, message: '请输入通告标题!' }]"
            >
              <a-input 
                v-model:value="modalFormState.标题" 
                placeholder="请输入通告标题" 
                show-count
                :maxlength="100"
              />
            </a-form-item>
          </a-col>
          
          <a-col :span="12">
            <a-form-item
              name="类型" 
              label="通告类型" 
              :rules="[{ required: true, message: '请选择通告类型!' }]"
            >
              <a-select 
                v-model:value="modalFormState.类型" 
                placeholder="请选择通告类型"
              >
                <a-select-option value="通知">通知</a-select-option>
                <a-select-option value="公告">公告</a-select-option>
                <a-select-option value="警告">警告</a-select-option>
                <a-select-option value="系统维护">系统维护</a-select-option>
                <a-select-option value="版本更新">版本更新</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 内容编辑区域 -->
        <a-form-item
          name="内容"
          label="通告内容"
          :rules="[{ required: true, message: '请输入通告内容!' }]"
        >
          <a-alert 
            message="内容安全提示" 
            description="内容将以HTML形式展示，请注意内容安全，避免XSS攻击。建议使用纯文本或简单的HTML标签。" 
            type="warning" 
            show-icon 
            style="margin-bottom: 12px;" 
          />
          <a-textarea 
            v-model:value="modalFormState.内容" 
            placeholder="请输入通告内容，支持基本HTML标签" 
            :rows="8" 
            show-count
            :maxlength="2000"
          />
        </a-form-item>

        <!-- 配置选项行 -->
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item name="重要性" label="重要性级别">
              <a-radio-group v-model:value="modalFormState.重要性">
                <a-radio :value="1">普通</a-radio>
                <a-radio :value="2">重要</a-radio>
                <a-radio :value="3">紧急</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          
          <a-col :span="8">
            <a-form-item 
              name="排序" 
              label="显示排序" 
              :rules="[{ type: 'number', message: '请输入数字'}]"
            >
              <a-input-number 
                v-model:value="modalFormState.排序" 
                placeholder="数字越大越靠前" 
                style="width: 100%;"
                :min="0"
                :max="9999"
              />
            </a-form-item>
          </a-col>
          
          <a-col :span="8">
            <a-form-item name="状态" label="发布状态">
              <a-radio-group v-model:value="modalFormState.状态">
                <a-radio :value="1">立即发布</a-radio>
                <a-radio :value="0">保存草稿</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 时间设置行 -->
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item name="开始时间" label="生效开始时间">
              <a-date-picker 
                v-model:value="modalFormState.开始时间" 
                show-time 
                placeholder="选择开始时间（可选）" 
                style="width: 100%;" 
                format="YYYY-MM-DD HH:mm:ss"
              />
            </a-form-item>
          </a-col>
          
          <a-col :span="12">
            <a-form-item name="结束时间" label="生效结束时间">
              <a-date-picker 
                v-model:value="modalFormState.结束时间" 
                show-time 
                placeholder="选择结束时间（可选）" 
                style="width: 100%;" 
                format="YYYY-MM-DD HH:mm:ss"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
/**
 * 通告管理页面
 * 功能：通告的增删改查、状态管理、批量操作等
 * 
 * 主要特性：
 * 1. 支持多条件搜索和筛选
 * 2. 支持通告的新增、编辑、删除操作
 * 3. 支持通告状态管理（发布/草稿）
 * 4. 支持重要性级别设置
 * 5. 支持时间范围设置
 * 6. 响应式表格设计，适配不同屏幕尺寸
 */

import { onMounted, reactive, ref, computed } from 'vue';
import { RouterLink } from 'vue-router';
import { 
  PlusOutlined, 
  SearchOutlined, 
  EditOutlined, 
  DeleteOutlined,
  ReloadOutlined 
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import superAdminService from '../services/superAdminService';
import { useSuperAdminRequest } from '../composables/useApiRequest';
import { formatDate } from '../utils/dateUtils';
import { useUserStore } from '../store/index.js';

// ==================== 响应式数据定义 ====================

// 使用统一的API响应处理钩子
const { loading, 执行API请求 } = useSuperAdminRequest();

// 表格数据源
const dataSource = ref([]);

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50', '100'],
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条 / 总共 ${total} 条`,
});

// 搜索表单状态
const searchFormState = reactive({
  title: '',
  status: undefined,
  type: undefined,
  importance: undefined,
  createTimeRange: [],
});

// 弹窗相关状态
const modalVisible = ref(false);
const modalConfirmLoading = ref(false);
const modalFormRef = ref();
const modalTitle = ref('新增通告');
const editingNotification = ref(null);

// 弹窗表单初始状态
const initialModalFormState = () => ({
  id: undefined,
  标题: '',
  内容: '',
  排序: 0,
  状态: 1, // 默认发布状态
  类型: '',
  重要性: 1, // 默认普通重要性
  开始时间: null,
  结束时间: null,
});

// 弹窗表单状态
const modalFormState = reactive(initialModalFormState());

// ==================== 表格列定义 ====================

const columns = [
  { 
    title: 'ID', 
    dataIndex: 'id', 
    key: 'id', 
    width: 80, 
    sorter: true,
    fixed: 'left'
  },
  { 
    title: '标题', 
    dataIndex: '标题', 
    key: '标题', 
    width: 200,
    sorter: true,
    ellipsis: true
  },
  { 
    title: '类型', 
    dataIndex: '类型', 
    key: '类型', 
    width: 100,
    sorter: true
  },
  { 
    title: '内容摘要', 
    dataIndex: '内容', 
    key: '内容', 
    width: 250, 
    ellipsis: true
  },
  { 
    title: '重要性', 
    dataIndex: '重要性', 
    key: '重要性', 
    width: 100, 
    sorter: true
  },
  { 
    title: '排序', 
    dataIndex: '操作标识', 
    key: '操作标识', 
    width: 80, 
    sorter: true
  },
  { 
    title: '状态', 
    dataIndex: '已发布', 
    key: '状态', 
    width: 100, 
    filters: [
      { text: '已发布', value: 1 }, 
      { text: '草稿', value: 0 }
    ]
  },
  { 
    title: '开始时间', 
    dataIndex: '开始时间', 
    key: '开始时间', 
    width: 150,
    sorter: true,
    customRender: ({ text }) => text ? formatDate(text) : '-'
  },
  { 
    title: '结束时间', 
    dataIndex: '结束时间', 
    key: '结束时间', 
    width: 150,
    sorter: true,
    customRender: ({ text }) => text ? formatDate(text) : '-'
  },
  { 
    title: '创建时间', 
    dataIndex: '创建时间', 
    key: '创建时间', 
    width: 150,
    sorter: true,
    customRender: ({ text }) => formatDate(text)
  },
  { 
    title: '更新时间', 
    dataIndex: '更新时间', 
    key: '更新时间', 
    width: 150,
    sorter: true,
    customRender: ({ text }) => formatDate(text)
  },
  { 
    title: '操作', 
    key: '操作', 
    width: 150, 
    fixed: 'right'
  },
];

// ==================== 表单验证规则 ====================

const formRules = {
  标题: [
    { required: true, message: '请输入通告标题!' },
    { min: 2, max: 100, message: '标题长度应在2-100个字符之间!' }
  ],
  类型: [
    { required: true, message: '请选择通告类型!' }
  ],
  内容: [
    { required: true, message: '请输入通告内容!' },
    { min: 5, max: 2000, message: '内容长度应在5-2000个字符之间!' }
  ],
  排序: [
    { type: 'number', min: 0, max: 9999, message: '排序值应在0-9999之间!' }
  ]
};

// ==================== 计算属性和工具函数 ====================

/**
 * 获取重要性对应的颜色
 * @param {number} importance - 重要性级别
 * @returns {string} 颜色值
 */
const getImportanceColor = (importance) => {
  const colorMap = {
    1: 'default',  // 普通
    2: 'orange',   // 重要
    3: 'red'       // 紧急
  };
  return colorMap[importance] || 'default';
};

/**
 * 获取重要性对应的文本
 * @param {number} importance - 重要性级别
 * @returns {string} 重要性文本
 */
const getImportanceText = (importance) => {
  const textMap = {
    1: '普通',
    2: '重要', 
    3: '紧急'
  };
  return textMap[importance] || '未知';
};

/**
 * 获取类型对应的颜色
 * @param {string} type - 通告类型
 * @returns {string} 颜色值
 */
const getTypeColor = (type) => {
  const colorMap = {
    '通知': 'blue',
    '公告': 'green',
    '警告': 'orange',
    '系统维护': 'red',
    '版本更新': 'purple'
  };
  return colorMap[type] || 'default';
};

/**
 * 获取内容预览文本
 * @param {string|Array} content - 通告内容
 * @returns {string} 预览文本
 */
const getContentPreview = (content) => {
  if (!content) return '暂无内容';
  
  let text = '';
  if (typeof content === 'string') {
    // 移除HTML标签，获取纯文本
    text = content.replace(/<[^>]*>/g, '');
  } else if (Array.isArray(content)) {
    // 如果是数组格式，提取文本内容
    text = content.map(item => item.内容 || '').join(' ');
  }
  
  // 限制预览长度
  return text.length > 100 ? text.substring(0, 100) + '...' : text;
};

// ==================== API调用函数 ====================

/**
 * 获取通告列表数据
 * @param {Object} params - 查询参数
 */
const fetchNotifications = async (params = {}) => {
  try {
    const queryParams = {
      页码: params.page || pagination.current,
      页面大小: params.pageSize || pagination.pageSize,
      类型筛选: searchFormState.type || null,
      状态筛选: searchFormState.status || null
    };

    console.log('发送通告列表请求参数:', queryParams);
    
    // 检查认证状态
    const userStore = useUserStore();
    console.log('用户认证状态:', {
      isAuthenticated: userStore.isAuthenticated,
      token: userStore.token ? '存在' : '不存在'
    });
    
    if (!userStore.isAuthenticated) {
      console.error('用户未认证，无法获取通告列表');
      message.error('用户未认证，请重新登录');
      return;
    }
    
    // 使用统一的API调用，自动处理错误和加载状态
    console.log('开始调用通告列表API...');
    
    try {
      // 直接调用API，不使用执行API请求包装器，以便更好地调试
      const response = await superAdminService.getAnnouncementList(queryParams);
      console.log('通告列表API原始响应:', response);
      
      // 检查响应格式：后端返回 {status: 100, message: "...", data: {列表: [...], 总数: 10}}
      if (response && response.status === 100 && response.data) {
        console.log('API调用成功，数据格式正确');
        const result = response.data; // 使用response.data而不是response
        
        // 成功获取数据，更新表格数据源和分页信息
        dataSource.value = result.通告列表?.map(item => ({
          ...item,
          key: item.id
        })) || [];

        const 分页信息 = result.分页信息 || {};
        pagination.total = 分页信息.总记录数 || 0;
        pagination.current = 分页信息.当前页码 || queryParams.页码;
        pagination.pageSize = 分页信息.页面大小 || queryParams.页面大小;
        
        console.log('数据更新完成:', {
          dataSource: dataSource.value.length,
          total: pagination.total,
          current: pagination.current
        });
      } else {
        console.error('API返回数据格式不正确:', response);
        message.error('获取通告列表失败：数据格式错误');
        dataSource.value = [];
        pagination.total = 0;
      }
    } catch (error) {
      console.error('API调用失败:', error);
      message.error('获取通告列表失败：' + (error.message || '未知错误'));
      dataSource.value = [];
      pagination.total = 0;
    }
  } catch (error) {
    console.error('获取通告列表失败:', error);
    message.error('获取通告列表失败，请稍后重试');
  }
};

/**
 * 删除通告
 * @param {number} notificationId - 通告ID
 */
const handleDelete = async (notificationId) => {
  try {
    const result = await 执行API请求(
      () => superAdminService.deleteAnnouncement(notificationId), 
      '删除成功'
    );
    
    if (result) {
      // 删除成功后刷新列表
      await fetchNotifications();
    }
  } catch (error) {
    console.error('删除通告失败:', error);
    message.error('删除通告失败，请稍后重试');
  }
};

// ==================== 事件处理函数 ====================

/**
 * 表格变化处理（分页、排序、筛选）
 * @param {Object} pag - 分页信息
 * @param {Object} filters - 筛选信息
 * @param {Object} sorter - 排序信息
 */
const handleTableChange = (pag, filters, sorter) => {
  fetchNotifications({
    page: pag.current,
    pageSize: pag.pageSize,
    sortField: sorter.field,
    sortOrder: sorter.order,
    filters: filters,
  });
};

/**
 * 搜索处理
 */
const handleSearch = () => {
  pagination.current = 1;
  fetchNotifications();
};

/**
 * 重置搜索表单
 */
const resetSearchForm = () => {
  Object.assign(searchFormState, {
    title: '',
    status: undefined,
    type: undefined,
    importance: undefined,
    createTimeRange: [],
  });
  handleSearch();
};

/**
 * 刷新数据
 */
const refreshData = () => {
  fetchNotifications();
};

/**
 * 显示新增弹窗
 */
const showAddModal = () => {
  editingNotification.value = null;
  Object.assign(modalFormState, initialModalFormState());
  modalTitle.value = '新增通告';
  modalVisible.value = true;
};

/**
 * 弹窗确认处理
 */
const handleModalOk = async () => {
  try {
    // 表单验证
    await modalFormRef.value.validate();
    modalConfirmLoading.value = true;
    
    // 准备发送到后端的数据
    const preparedData = {
      标题: modalFormState.标题,
      类型: modalFormState.类型,
      已发布: modalFormState.状态 === 1,
      重要性: modalFormState.重要性,
      排序: modalFormState.排序 || 0,
      开始时间: modalFormState.开始时间 ? modalFormState.开始时间.format('YYYY-MM-DD HH:mm:ss') : null,
      结束时间: modalFormState.结束时间 ? modalFormState.结束时间.format('YYYY-MM-DD HH:mm:ss') : null,
    };

    // 处理内容字段
    let parsedContent = [];
    if (modalFormState.内容 && typeof modalFormState.内容 === 'string') {
      try {
        // 尝试解析为JSON数组
        parsedContent = JSON.parse(modalFormState.内容);
        if (!Array.isArray(parsedContent)) {
          // 如果不是数组，包装成数组格式
          parsedContent = [{ 类型: '文本', 内容: modalFormState.内容, 操作类型: 0 }];
        }
      } catch (error) {
        // JSON解析失败，作为纯文本处理
        parsedContent = [{ 类型: '文本', 内容: modalFormState.内容, 操作类型: 0 }];
      }
    } else if (Array.isArray(modalFormState.内容)) {
      parsedContent = modalFormState.内容;
    }
    preparedData.内容 = parsedContent;

    // 调用API
    let result;
    const actionText = editingNotification.value ? '通告编辑成功！' : '通告新增成功！';
    
    if (editingNotification.value && editingNotification.value.id) {
      // 编辑通告
      result = await 执行API请求(
        () => superAdminService.updateAnnouncement(editingNotification.value.id, preparedData), 
        actionText
      );
    } else {
      // 新增通告
      result = await 执行API请求(
        () => superAdminService.createAnnouncement(preparedData), 
        actionText
      );
    }

    if (result) {
      // 成功后关闭弹窗并刷新列表
      modalVisible.value = false;
      await fetchNotifications();
    }
  } catch (errorInfo) {
    if (errorInfo.errorFields) {
      message.error('请检查表单输入项！');
    } else {
      console.error('操作通告错误:', errorInfo);
      message.error('操作失败，请稍后重试');
    }
  } finally {
    modalConfirmLoading.value = false;
  }
};

/**
 * 弹窗取消处理
 */
const handleModalCancel = () => {
  modalVisible.value = false;
};

// ==================== 生命周期钩子 ====================

/**
 * 组件挂载时获取初始数据
 */
onMounted(() => {
  fetchNotifications();
});

</script>

<style scoped>
/**
 * 通告管理页面样式
 * 采用现代化设计风格，注重用户体验
 */

.notification-management {
  padding: 0;
}

/* 头部卡片样式 */
.header-card {
  margin-bottom: 0;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 搜索表单样式 */
.search-form {
  margin-bottom: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.search-form .ant-form-item {
  margin-bottom: 16px;
}

/* 操作按钮区域样式 */
.action-buttons {
  margin-bottom: 16px;
  padding: 0 16px;
}

/* 表格样式优化 */
.notification-table {
  border-radius: 6px;
}

.notification-table .ant-table-thead > tr > th {
  background: #f5f5f5;
  font-weight: 600;
}

/* 内容预览样式 */
.content-preview {
  max-width: 250px;
}

.content-text {
  max-height: 60px;
  overflow: hidden;
  line-height: 1.4;
  word-break: break-word;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-form {
    padding: 12px;
  }
  
  .search-form .ant-form-item {
    margin-bottom: 12px;
  }
  
  .action-buttons {
    padding: 0 12px;
  }
}

/* 弹窗样式优化 */
.ant-modal-body {
  padding: 24px;
}

.ant-form-vertical .ant-form-item-label {
  padding: 0 0 4px;
  font-weight: 500;
}

/* 标签颜色优化 */
.ant-tag {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
}
</style>