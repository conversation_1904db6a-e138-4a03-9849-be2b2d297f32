<template>
  <div class="rag-config">
    <div class="section-header">
      <h2><DatabaseOutlined /> RAG配置</h2>
      <p>配置知识库检索增强生成</p>
    </div>
    
    <a-form layout="vertical" class="config-form">
      <!-- RAG开关 -->
      <a-card title="RAG设置" class="config-card">
        <a-form-item label="启用rag">
          <a-switch
            v-model:checked="localForm.启用rag"
            checked-children="开启"
            un-checked-children="关闭"
            @change="handleRAGToggle"
          />
          <span class="switch-desc">
            {{ localForm.启用rag ? '已启用知识库检索增强' : '使用纯模型对话' }}
          </span>
        </a-form-item>

        <!-- RAG配置内容 -->
        <div v-if="localForm.启用rag" class="rag-settings">
          <!-- 知识库选择 -->
          <a-form-item 
            label="知识库选择" 
            required
            :validate-status="errors.知识库列表 ? 'error' : ''"
            :help="errors.知识库列表"
          >
            <a-select
              v-model:value="localForm.知识库列表"
              mode="multiple"
              placeholder="选择知识库"
              :loading="loading.知识库列表"
              :options="knowledgeBaseOptions"
              show-search
              :filter-option="filterKnowledgeBase"
              @change="handleFormChange"
            >
              <template #option="{ label, value, ...kb }">
                <div class="kb-option">
                  <div class="kb-name">{{ label }}</div>
                  <div class="kb-info">
                    <a-space size="small">
                      <a-tag size="small" color="blue">{{ kb.文档数量 || 0 }} 文档</a-tag>
                      <a-tag size="small" :color="kb.状态 === '正常' ? 'green' : 'orange'">
                        {{ kb.状态 }}
                      </a-tag>
                    </a-space>
                  </div>
                </div>
              </template>
            </a-select>
          </a-form-item>

          <!-- 已选知识库信息 -->
          <div v-if="selectedKnowledgeBases.length > 0" class="selected-kb-info">
            <div class="info-title">已选择的知识库：</div>
            <div class="kb-list">
              <div v-for="kb in selectedKnowledgeBases" :key="kb.value" class="kb-item">
                <a-card size="small">
                  <div class="kb-item-content">
                    <div class="kb-item-name">{{ kb.label }}</div>
                    <div class="kb-item-stats">
                      <a-space size="small">
                        <span>{{ kb.文档数量 || 0 }} 文档</span>
                        <a-tag size="small" :color="kb.状态 === '正常' ? 'green' : 'orange'">
                          {{ kb.状态 }}
                        </a-tag>
                      </a-space>
                    </div>
                  </div>
                </a-card>
              </div>
            </div>
          </div>

          <!-- 检索策略 -->
          <a-form-item label="检索策略">
            <a-radio-group v-model:value="localForm.检索策略" @change="handleFormChange">
              <a-radio value="similarity">语义相似度</a-radio>
              <a-radio value="keyword">关键词匹配</a-radio>
              <a-radio value="hybrid">混合检索</a-radio>
            </a-radio-group>
            <div class="strategy-desc">
              {{ getStrategyDesc(localForm.检索策略) }}
            </div>
          </a-form-item>

          <!-- 嵌入模型 -->
          <a-form-item label="嵌入模型">
            <a-select
              v-model:value="localForm.嵌入模型id"
              placeholder="选择嵌入模型"
              :options="embeddingModelOptions"
              allow-clear
              @change="handleFormChange"
            />
          </a-form-item>

          <!-- 检索参数 -->
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="相似度阈值">
                <a-slider
                  v-model:value="localForm.相似度阈值"
                  :min="0"
                  :max="1"
                  :step="0.1"
                  :marks="{ 0: '0', 0.5: '0.5', 1: '1' }"
                  @change="handleFormChange"
                />
                <div class="param-desc">
                  阈值越高，检索结果越精确
                </div>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="最大检索数量">
                <a-input-number
                  v-model:value="localForm.最大检索数量"
                  :min="1"
                  :max="20"
                  @change="handleFormChange"
                />
                <div class="param-desc">
                  建议3-10个结果
                </div>
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 查询优化 -->
          <a-collapse ghost>
            <a-collapse-panel key="query-optimization" header="查询优化设置">
              <a-form-item label="启用查询优化">
                <a-switch 
                  v-model:checked="localForm.查询优化配置.启用查询优化"
                  @change="handleFormChange"
                />
                <span class="switch-desc">
                  自动优化用户查询以提高检索效果
                </span>
              </a-form-item>

              <div v-if="localForm.查询优化配置.启用查询优化">
                <a-form-item label="优化策略">
                  <a-select
                    v-model:value="localForm.查询优化配置.查询优化策略"
                    @change="handleFormChange"
                  >
                    <a-select-option value="rewrite">查询重写</a-select-option>
                    <a-select-option value="expand">查询扩展</a-select-option>
                    <a-select-option value="decompose">查询分解</a-select-option>
                  </a-select>
                </a-form-item>

                <a-form-item label="优化模型">
                  <a-select
                    v-model:value="localForm.查询优化配置.查询优化模型id"
                    placeholder="选择查询优化模型"
                    :options="modelOptions"
                    @change="handleFormChange"
                  />
                </a-form-item>
              </div>
            </a-collapse-panel>
          </a-collapse>
        </div>
      </a-card>

      <!-- RAG测试 -->
      <a-card v-if="localForm.启用rag" title="检索测试" class="config-card">
        <RAGTestPanel
          :knowledge-base-ids="localForm.知识库列表"
          :retrieval-config="{
            检索策略: localForm.检索策略,
            嵌入模型: 选中的嵌入模型名称,
            嵌入模型id: localForm.嵌入模型id,
            相似度阈值: localForm.相似度阈值,
            最大检索数量: localForm.最大检索数量,
            启用查询优化: localForm.查询优化配置?.启用查询优化,
            查询优化模型id: localForm.查询优化配置?.查询优化模型id,
            查询优化提示词: localForm.查询优化配置?.查询优化提示词
          }"
          @test-result="handleTestResult"
        />
      </a-card>
    </a-form>
  </div>
</template>

<script setup>
import { DatabaseOutlined } from '@ant-design/icons-vue'
import { computed, watch } from 'vue'
import RAGTestPanel from '../test/RAGTestPanel.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  errors: {
    type: Object,
    default: () => ({})
  },
  knowledgeBaseOptions: {
    type: Array,
    default: () => []
  },
  embeddingModelOptions: {
    type: Array,
    default: () => []
  },
  modelOptions: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'validate', 'change'])

// 本地表单数据
const localForm = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 已选择的知识库
const selectedKnowledgeBases = computed(() => {
  if (!localForm.value.知识库列表?.length) return []

  return props.knowledgeBaseOptions.filter(kb =>
    localForm.value.知识库列表.includes(kb.value)
  )
})

// 选中的嵌入模型名称
const 选中的嵌入模型名称 = computed(() => {
  if (!localForm.value.嵌入模型id) return null

  const 选中的模型 = props.embeddingModelOptions.find(model =>
    model.value === localForm.value.嵌入模型id
  )

  return 选中的模型?.模型名称 || 选中的模型?.label || null
})



// 获取策略描述
const getStrategyDesc = (strategy) => {
  const descriptions = {
    'similarity': '基于语义相似度进行检索，适合概念性查询',
    'keyword': '基于关键词匹配进行检索，适合精确查询',
    'hybrid': '结合语义和关键词检索，平衡精确性和召回率'
  }
  return descriptions[strategy] || ''
}

// 过滤知识库
const filterKnowledgeBase = (input, option) => {
  const searchText = input.toLowerCase()
  return option.label.toLowerCase().includes(searchText)
}



// 处理RAG开关
const handleRAGToggle = (enabled) => {
  if (!enabled) {
    // 关闭RAG时清空相关配置
    const newFormData = { ...localForm.value }
    newFormData.知识库列表 = []
    newFormData.嵌入模型id = null
    localForm.value = newFormData
  }
  handleFormChange()
}

// 处理表单变化
const handleFormChange = () => {
  emit('change', localForm.value)
}

// 处理测试结果
const handleTestResult = (result) => {
  console.log('RAG测试结果:', result)
}

// 监听知识库列表变化，清除验证错误
watch(() => localForm.value.知识库列表, () => {
  if (props.errors.知识库列表) {
    emit('validate', { 知识库列表: null })
  }
})

// 监听启用rag状态变化
watch(() => localForm.value.启用rag, (newValue, oldValue) => {
  console.log('🔍 RAGConfig - 启用rag状态变化:', {
    旧值: oldValue,
    新值: newValue,
    类型: typeof newValue
  })
}, { immediate: true })



// 初始化查询优化配置
if (!localForm.value.查询优化配置) {
  localForm.value.查询优化配置 = {
    启用查询优化: false,
    查询优化策略: 'rewrite',
    查询优化模型id: null,
    查询优化提示词: ''
  }
}
</script>

<style scoped>
.rag-config {
  padding: 16px;
}

.section-header {
  margin-bottom: 24px;
}

.section-header h2 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-header p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.config-form {
  max-width: 800px;
}

.config-card {
  margin-bottom: 16px;
}

.switch-desc {
  margin-left: 12px;
  color: #8c8c8c;
  font-size: 12px;
}

.rag-settings {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.kb-option {
  padding: 4px 0;
}

.kb-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.kb-info {
  font-size: 12px;
}

.selected-kb-info {
  margin: 16px 0;
}

.info-title {
  font-weight: 500;
  color: #262626;
  margin-bottom: 8px;
}

.kb-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 8px;
}

.kb-item-content {
  padding: 4px;
}

.kb-item-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.kb-item-stats {
  font-size: 12px;
  color: #8c8c8c;
}

.strategy-desc {
  margin-top: 4px;
  color: #8c8c8c;
  font-size: 12px;
}

.param-desc {
  margin-top: 4px;
  color: #8c8c8c;
  font-size: 12px;
}

:deep(.ant-collapse-ghost .ant-collapse-item) {
  border-bottom: none;
}

:deep(.ant-collapse-ghost .ant-collapse-content) {
  background: transparent;
}
</style>
