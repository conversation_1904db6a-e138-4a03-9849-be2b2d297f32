<template>
  <div class="rag-test-panel">
    <div class="test-header">
      <h3>RAG检索测试</h3>
      <p>测试知识库检索效果</p>
    </div>

    <a-form layout="vertical" class="test-form">
      <!-- 测试查询 -->
      <a-form-item label="测试查询">
        <a-textarea
          v-model:value="testQuery"
          placeholder="输入要测试的查询内容"
          :rows="3"
          :maxlength="500"
          show-count
        />
      </a-form-item>

      <!-- 当前测试参数 -->
      <a-collapse ghost>
        <a-collapse-panel key="params" header="当前测试参数">
          <a-descriptions size="small" :column="2">
            <a-descriptions-item label="检索数量">
              {{ props.retrievalConfig?.最大检索数量 || 5 }}
            </a-descriptions-item>
            <a-descriptions-item label="相似度阈值">
              {{ props.retrievalConfig?.相似度阈值 || 0.7 }}
            </a-descriptions-item>
            <a-descriptions-item label="检索策略">
              {{ getStrategyText(props.retrievalConfig?.检索策略) }}
            </a-descriptions-item>
            <a-descriptions-item label="嵌入模型">
              {{ props.retrievalConfig?.嵌入模型 || '默认模型' }}
            </a-descriptions-item>
          </a-descriptions>
          <a-alert
            type="info"
            message="参数说明"
            description="测试参数来自上方RAG配置，如需修改请在上方配置区域调整"
            show-icon
            style="margin-top: 12px"
          />
        </a-collapse-panel>
      </a-collapse>

      <!-- 测试按钮 -->
      <a-form-item>
        <a-space>
          <a-button 
            type="primary" 
            @click="runTest"
            :loading="testing"
            :disabled="!testQuery.trim() || knowledgeBaseIds.length === 0"
          >
            <SearchOutlined /> 开始测试
          </a-button>
          <a-button @click="clearResults">
            <ClearOutlined /> 清空结果
          </a-button>
        </a-space>
      </a-form-item>
    </a-form>

    <!-- 测试结果 -->
    <div v-if="testResults.length > 0" class="test-results">
      <div class="results-header">
        <h4>检索结果 ({{ testResults.length }} 条)</h4>
        <a-space>
          <span class="result-stats">
            平均相似度: {{ averageScore.toFixed(3) }}
          </span>
          <a-button size="small" @click="exportResults">
            <DownloadOutlined /> 导出结果
          </a-button>
        </a-space>
      </div>

      <!-- 三阶段检索对比展示 -->
      <div v-if="queryOptimizationInfo" class="three-stage-comparison">
        <!-- 对比分析总览 -->
        <a-card size="small" title="三阶段检索对比分析" class="comparison-overview">
          <div class="comparison-summary">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-statistic
                  title="第一阶段最高相似度"
                  :value="queryOptimizationInfo.对比分析?.第一阶段最高相似度 || 0"
                  :precision="4"
                  :value-style="{ color: '#1890ff' }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="第二阶段最高相似度"
                  :value="queryOptimizationInfo.对比分析?.第二阶段最高相似度 || 0"
                  :precision="4"
                  :value-style="{ color: '#52c41a' }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="第三阶段最高相似度"
                  :value="queryOptimizationInfo.对比分析?.第三阶段最高相似度 || 0"
                  :precision="4"
                  :value-style="{ color: '#fa8c16' }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="总体提升"
                  :value="queryOptimizationInfo.对比分析?.三阶段对比?.第一到第三阶段总提升 || 0"
                  :precision="4"
                  :value-style="{ color: totalImprovementStyle.color }"
                  :prefix="totalImprovementStyle.prefix"
                />
              </a-col>
            </a-row>

            <!-- 最终选择结果 -->
            <div class="final-choice" style="margin-top: 16px;">
              <a-alert
                :type="queryOptimizationInfo.对比分析?.优化效果?.类型 || 'info'"
                :message="`最终选择：${queryOptimizationInfo.对比分析?.最终选择 || '未知'}`"
                :description="queryOptimizationInfo.对比分析?.优化效果?.描述 || ''"
                show-icon
              />
            </div>
          </div>
        </a-card>

        <!-- 三阶段详细对比 -->
        <a-row :gutter="16" style="margin-top: 16px;">
          <!-- 第一阶段检索 -->
          <a-col :span="8">
            <a-card
              size="small"
              title="第一阶段：原始查询检索"
              class="stage-card first-stage"
              :class="{ 'selected-stage': queryOptimizationInfo.对比分析?.最终选择 === '第一阶段' }"
            >
              <div class="stage-query">
                <strong>查询内容：</strong>
                <div class="query-text original">{{ queryOptimizationInfo.第一阶段检索?.查询 || '' }}</div>
              </div>
              <a-descriptions size="small" :column="1" style="margin-top: 12px;">
                <a-descriptions-item label="结果数量">
                  <a-tag color="blue">{{ queryOptimizationInfo.第一阶段检索?.结果数量 || 0 }}</a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="最高相似度">
                  <a-tag color="cyan">{{ (queryOptimizationInfo.第一阶段检索?.最高相似度 || 0).toFixed(4) }}</a-tag>
                </a-descriptions-item>
              </a-descriptions>
            </a-card>
          </a-col>

          <!-- 第二阶段检索 -->
          <a-col :span="8">
            <a-card
              size="small"
              title="第二阶段：第一次优化查询检索"
              class="stage-card second-stage"
              :class="{ 'selected-stage': queryOptimizationInfo.对比分析?.最终选择 === '第二阶段' }"
            >
              <div class="stage-query">
                <strong>查询内容：</strong>
                <div class="query-text optimized">{{ queryOptimizationInfo.第二阶段检索?.查询 || '' }}</div>
              </div>
              <a-descriptions size="small" :column="1" style="margin-top: 12px;">
                <a-descriptions-item label="结果数量">
                  <a-tag color="green">{{ queryOptimizationInfo.第二阶段检索?.结果数量 || 0 }}</a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="最高相似度">
                  <a-tag color="lime">{{ (queryOptimizationInfo.第二阶段检索?.最高相似度 || 0).toFixed(4) }}</a-tag>
                </a-descriptions-item>
              </a-descriptions>
            </a-card>
          </a-col>

          <!-- 第三阶段检索 -->
          <a-col :span="8">
            <a-card
              size="small"
              title="第三阶段：第二次优化查询检索"
              class="stage-card third-stage"
              :class="{ 'selected-stage': queryOptimizationInfo.对比分析?.最终选择 === '第三阶段' }"
            >
              <div class="stage-query">
                <strong>查询内容：</strong>
                <div class="query-text optimized-final">{{ queryOptimizationInfo.第三阶段检索?.查询 || '' }}</div>
              </div>
              <a-descriptions size="small" :column="1" style="margin-top: 12px;">
                <a-descriptions-item label="结果数量">
                  <a-tag color="orange">{{ queryOptimizationInfo.第三阶段检索?.结果数量 || 0 }}</a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="最高相似度">
                  <a-tag color="gold">{{ (queryOptimizationInfo.第三阶段检索?.最高相似度 || 0).toFixed(4) }}</a-tag>
                </a-descriptions-item>
              </a-descriptions>
            </a-card>
          </a-col>
        </a-row>

        <!-- 查询优化详情 -->
        <a-card size="small" title="查询优化详情" class="optimization-details" style="margin-top: 16px;">
          <a-row :gutter="16">
            <!-- 第一次优化 -->
            <a-col :span="12">
              <h4 style="margin-bottom: 12px;">第一次优化</h4>
              <a-descriptions size="small" :column="1">
                <a-descriptions-item label="优化策略">
                  <a-tag color="purple">{{ queryOptimizationInfo.第一次查询优化?.优化策略 || '' }}</a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="优化模型">
                  <a-tag color="geekblue">{{ queryOptimizationInfo.第一次查询优化?.优化模型 || '' }}</a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="优化耗时">
                  <a-tag color="orange">{{ queryOptimizationInfo.第一次查询优化?.优化耗时 || 0 }}ms</a-tag>
                </a-descriptions-item>
              </a-descriptions>
            </a-col>

            <!-- 第二次优化 -->
            <a-col :span="12">
              <h4 style="margin-bottom: 12px;">第二次优化</h4>
              <a-descriptions size="small" :column="1">
                <a-descriptions-item label="优化策略">
                  <a-tag color="purple">{{ queryOptimizationInfo.第二次查询优化?.优化策略 || '' }}</a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="优化模型">
                  <a-tag color="geekblue">{{ queryOptimizationInfo.第二次查询优化?.优化模型 || '' }}</a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="优化耗时">
                  <a-tag color="orange">{{ queryOptimizationInfo.第二次查询优化?.优化耗时 || 0 }}ms</a-tag>
                </a-descriptions-item>
              </a-descriptions>
            </a-col>
          </a-row>

          <!-- 三阶段对比分析 -->
          <a-divider style="margin: 16px 0;" />
          <h4 style="margin-bottom: 12px;">三阶段对比分析</h4>
          <a-descriptions size="small" :column="3">
            <a-descriptions-item label="第一到第二阶段提升">
              <a-tag :color="getImprovementColor(queryOptimizationInfo.对比分析?.三阶段对比?.第一到第二阶段提升)">
                {{ formatSimilarity(queryOptimizationInfo.对比分析?.三阶段对比?.第一到第二阶段提升) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="第二到第三阶段提升">
              <a-tag :color="getImprovementColor(queryOptimizationInfo.对比分析?.三阶段对比?.第二到第三阶段提升)">
                {{ formatSimilarity(queryOptimizationInfo.对比分析?.三阶段对比?.第二到第三阶段提升) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="第一到第三阶段总提升">
              <a-tag :color="getImprovementColor(queryOptimizationInfo.对比分析?.三阶段对比?.第一到第三阶段总提升)">
                {{ formatSimilarity(queryOptimizationInfo.对比分析?.三阶段对比?.第一到第三阶段总提升) }}
              </a-tag>
            </a-descriptions-item>
          </a-descriptions>
        </a-card>
      </div>

      <div class="results-list">
        <div 
          v-for="(result, index) in testResults" 
          :key="index"
          class="result-item"
        >
          <div class="result-header">
            <div class="result-index">#{{ index + 1 }}</div>
            <div class="result-score">
              <a-progress 
                :percent="Math.round(result.相似度分数 * 100)" 
                size="small"
                :stroke-color="getScoreColor(result.相似度分数)"
              />
              <span class="score-text">{{ result.相似度分数.toFixed(3) }}</span>
            </div>
          </div>

          <div class="result-content">
            <div class="result-text">{{ result.分块内容 }}</div>
            <div class="result-meta">
              <a-space size="small">
                <a-tag size="small" color="blue">
                  知识库ID: {{ result.知识库ID }}
                </a-tag>
                <a-tag size="small" color="green">
                  {{ result.文档名称 }}
                </a-tag>
                <span class="meta-text">
                  文档ID: {{ result.文档记录ID }}
                </span>
              </a-space>
            </div>
          </div>

          <!-- 展开详情 -->
          <a-collapse ghost>
            <a-collapse-panel key="detail" header="查看详情">
              <a-descriptions size="small" :column="2">
                <a-descriptions-item label="文档UUID">
                  {{ result.文档uuid || '未知' }}
                </a-descriptions-item>
                <a-descriptions-item label="分块大小">
                  {{ result.分块大小 || '未知' }}
                </a-descriptions-item>
                <a-descriptions-item label="分块序号">
                  {{ result.分块序号 !== undefined ? result.分块序号 : '未知' }}
                </a-descriptions-item>
                <a-descriptions-item label="相似度分数">
                  {{ result.相似度分数 ? result.相似度分数.toFixed(4) : '未知' }}
                </a-descriptions-item>
                <a-descriptions-item label="元数据" :span="2">
                  <pre style="font-size: 12px; margin: 0;">{{ JSON.stringify(result.元数据, null, 2) }}</pre>
                </a-descriptions-item>
              </a-descriptions>
            </a-collapse-panel>
          </a-collapse>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="hasTestedOnce" class="empty-results">
      <a-empty description="未找到相关结果">
        <template #image>
          <SearchOutlined style="font-size: 48px; color: #d9d9d9;" />
        </template>
      </a-empty>
    </div>

    <!-- 测试历史 -->
    <div v-if="testHistory.length > 0" class="test-history">
      <a-collapse ghost>
        <a-collapse-panel key="history" header="测试历史">
          <div class="history-list">
            <div 
              v-for="(history, index) in testHistory" 
              :key="index"
              class="history-item"
              @click="loadHistoryTest(history)"
            >
              <div class="history-query">{{ history.查询内容 }}</div>
              <div class="history-meta">
                <a-space size="small">
                  <span>{{ history.结果数量 }} 条结果</span>
                  <span>{{ history.测试时间 }}</span>
                </a-space>
              </div>
            </div>
          </div>
        </a-collapse-panel>
      </a-collapse>
    </div>
  </div>
</template>

<script setup>
import knowledgeBaseService from '@/services/knowledgeBaseService'
import { ClearOutlined, DownloadOutlined, SearchOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { computed, ref } from 'vue'

// Props
const props = defineProps({
  knowledgeBaseIds: {
    type: Array,
    default: () => []
  },
  retrievalConfig: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['test-result'])

// 测试状态
const testing = ref(false)
const hasTestedOnce = ref(false)
const testQuery = ref('')

// 测试结果
const testResults = ref([])
const testHistory = ref([])
const queryOptimizationInfo = ref(null)

// 计算属性
const averageScore = computed(() => {
  if (testResults.value.length === 0) return 0
  const sum = testResults.value.reduce((acc, result) => acc + result.相似度分数, 0)
  return sum / testResults.value.length
})

// 获取相似度提升的颜色
const getImprovementColor = (value) => {
  return (value || 0) > 0 ? 'green' : 'red'
}

// 格式化相似度数值
const formatSimilarity = (value) => {
  return (value || 0).toFixed(4)
}

// 获取总体提升的样式配置
const totalImprovementStyle = computed(() => {
  const value = queryOptimizationInfo.value?.对比分析?.三阶段对比?.第一到第三阶段总提升 || 0
  return {
    color: value > 0 ? '#52c41a' : '#ff4d4f',
    prefix: value > 0 ? '+' : ''
  }
})

// 获取检索策略显示文本
const getStrategyText = (strategy) => {
  const strategyMap = {
    'similarity': '语义相似度',
    'keyword': '关键词匹配',
    'hybrid': '混合检索',
    'vector': '向量检索',
    'mixed': '混合检索'
  }
  return strategyMap[strategy] || strategy || '默认策略'
}

// 获取分数颜色
const getScoreColor = (score) => {
  if (score >= 0.8) return '#52c41a'
  if (score >= 0.6) return '#faad14'
  return '#ff4d4f'
}

// 运行测试
const runTest = async () => {
  if (!testQuery.value.trim()) {
    message.warning('请输入测试查询')
    return
  }

  if (props.knowledgeBaseIds.length === 0) {
    message.warning('请先选择知识库')
    return
  }

  testing.value = true
  hasTestedOnce.value = true

  try {
    // 验证必需参数
    if (!props.knowledgeBaseIds || props.knowledgeBaseIds.length === 0) {
      message.warning('请先选择知识库')
      return
    }

    if (!testQuery.value || testQuery.value.trim().length === 0) {
      message.warning('请输入测试查询内容')
      return
    }

    // 构建检索配置参数（直接使用上方RAG配置的参数）
    const 检索配置参数 = {
      检索策略: props.retrievalConfig?.检索策略 || 'similarity',
      嵌入模型: props.retrievalConfig?.嵌入模型 || props.retrievalConfig?.嵌入模型名称 || 'text-embedding-v4',
      相似度阈值: props.retrievalConfig?.相似度阈值 !== undefined ? props.retrievalConfig.相似度阈值 : 0.7,
      最大检索数量: props.retrievalConfig?.最大检索数量 || 5
    }

    // 构建查询优化配置（如果启用）
    let 查询优化配置 = null
    if (props.retrievalConfig?.启用查询优化) {
      查询优化配置 = {
        启用: true,
        优化策略: "rewrite", // 默认使用重写策略
        优化模型id: props.retrievalConfig.查询优化模型id,
        提示词模板: props.retrievalConfig.查询优化提示词 || ""
      }
    }

    console.log('📊 RAG测试面板 - 使用实时参数进行检索测试:', {
      知识库列表: props.knowledgeBaseIds,
      检索配置参数,
      查询优化配置
    })

    // 使用新版本接口进行多知识库检索测试
    const response = await knowledgeBaseService.testRetrievalNew(
      props.knowledgeBaseIds, // 知识id列表
      testQuery.value.trim(), // 测试查询
      检索配置参数, // 检索配置
      查询优化配置, // 查询优化配置
      'standard' // 测试模式
    )

    if (response.success) {
      testResults.value = response.data.检索结果 || []
      const 总结果数量 = response.data.总结果数量 || testResults.value.length

      // 保存查询优化信息（如果有）
      queryOptimizationInfo.value = response.data.查询优化信息 || null

      // 添加到测试历史
      testHistory.value.unshift({
        查询内容: testQuery.value,
        结果数量: 总结果数量,
        测试时间: new Date().toLocaleString('zh-CN'),
        参数: { ...检索配置参数 },
        查询优化配置,
        查询优化信息: queryOptimizationInfo.value,
        结果: testResults.value,
        知识库详情: response.data.知识库检索详情 || []
      })

      // 限制历史记录数量
      if (testHistory.value.length > 10) {
        testHistory.value = testHistory.value.slice(0, 10)
      }

      console.log('✅ RAG测试面板 - 检索测试成功（新版本）:', response.data)
    } else {
      console.warn('❌ RAG测试面板 - 检索测试失败:', response.error)
      testResults.value = []
    }

    emit('test-result', {
      query: testQuery.value,
      results: testResults.value,
      params: 检索配置参数
    })

    message.success(`检索完成，找到 ${testResults.value.length} 条相关结果`)
  } catch (error) {
    console.error('RAG测试失败:', error)
    message.error('测试失败，请重试')
  } finally {
    testing.value = false
  }
}

// 清空结果
const clearResults = () => {
  testResults.value = []
  hasTestedOnce.value = false
}

// 导出结果
const exportResults = () => {
  const data = {
    查询: testQuery.value,
    参数: props.retrievalConfig,
    结果: testResults.value,
    导出时间: new Date().toISOString()
  }
  
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `rag_test_results_${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)
  
  message.success('结果已导出')
}

// 加载历史测试
const loadHistoryTest = (history) => {
  testQuery.value = history.查询内容
  testResults.value = [...history.结果]
  queryOptimizationInfo.value = history.查询优化信息 || null
  hasTestedOnce.value = true
  message.info('已加载历史测试（参数将使用当前RAG配置）')
}
</script>

<style scoped>
.rag-test-panel {
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
}

.test-header {
  margin-bottom: 16px;
}

.test-header h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.test-header p {
  margin: 0;
  color: #8c8c8c;
  font-size: 12px;
}

.test-form {
  margin-bottom: 24px;
}

.test-results {
  margin-top: 24px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.results-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.result-stats {
  font-size: 12px;
  color: #8c8c8c;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.result-item {
  background: white;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.result-index {
  font-weight: 600;
  color: #1890ff;
  font-size: 14px;
}

.result-score {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
}

.score-text {
  font-size: 12px;
  font-weight: 500;
  color: #262626;
}

.result-content {
  margin-bottom: 12px;
}

.result-text {
  color: #262626;
  line-height: 1.6;
  margin-bottom: 8px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
}

.result-meta {
  font-size: 12px;
}

.meta-text {
  color: #8c8c8c;
}

.empty-results {
  text-align: center;
  padding: 40px 20px;
}

.test-history {
  margin-top: 24px;
}

.history-list {
  max-height: 200px;
  overflow-y: auto;
}

.history-item {
  padding: 8px 12px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.history-item:hover {
  border-color: #1890ff;
  background: #f6ffed;
}

.history-query {
  font-size: 13px;
  color: #262626;
  margin-bottom: 4px;
  font-weight: 500;
}

.history-meta {
  font-size: 11px;
  color: #8c8c8c;
}

:deep(.ant-collapse-ghost .ant-collapse-item) {
  border-bottom: none;
}

:deep(.ant-collapse-ghost .ant-collapse-content) {
  background: transparent;
}

:deep(.ant-progress-inner) {
  background: #f5f5f5;
}

/* 查询优化信息样式 */
.query-optimization-info {
  margin-bottom: 16px;
}

.optimization-card {
  border: 1px solid #e8f4fd;
  background: #f6fbff;
}

.query-text {
  padding: 8px 12px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-word;
}

.query-text.original {
  background: #fff2e8;
  border: 1px solid #ffbb96;
  color: #d4380d;
}

.query-text.optimized {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  color: #389e0d;
}

.query-text.optimized-final {
  background: #fff7e6;
  border: 1px solid #ffd591;
  color: #d46b08;
}

.optimization-effect {
  margin-top: 12px;
}

.optimization-effect h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

/* 三阶段检索对比样式 */
.three-stage-comparison {
  margin-bottom: 24px;
}

.comparison-overview {
  border: 2px solid #f0f0f0;
  border-radius: 8px;
}

.comparison-summary {
  padding: 8px 0;
}

.stage-card {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.stage-card.first-stage {
  border-left: 4px solid #1890ff;
}

.stage-card.second-stage {
  border-left: 4px solid #52c41a;
}

.stage-card.third-stage {
  border-left: 4px solid #fa8c16;
}

.stage-card.selected-stage {
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
  border-color: #1890ff;
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
}

.stage-card.second-stage.selected-stage {
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.15);
  border-color: #52c41a;
  background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
}

.stage-card.third-stage.selected-stage {
  box-shadow: 0 4px 12px rgba(250, 140, 22, 0.15);
  border-color: #fa8c16;
  background: linear-gradient(135deg, #fff7e6 0%, #fef7e6 100%);
}

.stage-query {
  margin-bottom: 12px;
}

.stage-query strong {
  color: #262626;
  font-size: 13px;
}

.optimization-details {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.final-choice {
  text-align: center;
}
</style>
