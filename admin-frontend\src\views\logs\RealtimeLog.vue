<template>
  <div class="realtime-log">
    <!-- 页面头部 -->
    <div class="log-header">
      <h1>
        <FileTextOutlined />
        实时日志监控
      </h1>
      <div class="header-actions">
        <a-space>
          <a-button @click="refreshConnection">
            <template #icon>
              <ReloadOutlined />
            </template>
            重新连接
          </a-button>
          <a-button @click="exportLogs">
            <template #icon>
              <DownloadOutlined />
            </template>
            导出日志
          </a-button>
          <a-switch 
            v-model:checked="autoRefresh"
            checked-children="自动刷新"
            un-checked-children="手动刷新"
          />
        </a-space>
      </div>
    </div>

    <!-- 统计卡片 -->
    <a-row :gutter="16" class="stats-row">
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="总日志数"
            :value="logs.length"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix>
              <FileTextOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="错误日志"
            :value="errorCount"
            :value-style="{ color: '#ff4d4f' }"
          >
            <template #prefix>
              <ExclamationCircleOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="警告日志"
            :value="warnCount"
            :value-style="{ color: '#faad14' }"
          >
            <template #prefix>
              <WarningOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="连接状态"
            :value="connectionStatus"
            :value-style="{ color: isReceiving ? '#52c41a' : '#8c8c8c' }"
          >
            <template #prefix>
              <WifiOutlined v-if="isReceiving" />
              <DisconnectOutlined v-else />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>

    <!-- 控制面板 -->
    <a-card class="control-panel">
      <div class="control-row">
        <div class="control-left">
          <a-space>
            <a-button 
              :type="isReceiving ? 'danger' : 'primary'"
              :loading="connecting"
              @click="toggleRealtimeLog"
              size="large"
            >
              <template #icon>
                <PlayCircleOutlined v-if="!isReceiving" />
                <PauseCircleOutlined v-else />
              </template>
              {{ isReceiving ? '暂停接收' : '开始接收' }}
            </a-button>
            
            <a-button @click="clearLogs" size="large">
              <template #icon>
                <ClearOutlined />
              </template>
              清空日志
            </a-button>

            <a-button @click="scrollToBottom" size="large">
              <template #icon>
                <VerticalAlignBottomOutlined />
              </template>
              滚动到底部
            </a-button>
          </a-space>
        </div>
        
        <div class="control-right">
          <a-space>
            <a-switch 
              v-model:checked="autoScroll"
              checked-children="自动滚动"
              un-checked-children="手动滚动"
            />
            <a-select 
              v-model:value="logLevel"
              placeholder="选择日志级别"
              style="width: 120px"
              allow-clear
            >
              <a-select-option value="error">ERROR</a-select-option>
              <a-select-option value="warn">WARN</a-select-option>
              <a-select-option value="info">INFO</a-select-option>
              <a-select-option value="debug">DEBUG</a-select-option>
            </a-select>
            <a-input-search
              v-model:value="filterKeyword"
              placeholder="过滤日志内容..."
              style="width: 300px"
              allow-clear
              @search="applyFilter"
            />
          </a-space>
        </div>
      </div>
    </a-card>
    
    <!-- 日志显示区域 -->
    <a-card class="log-container">
      <template #title>
        <div class="log-title">
          <span>实时日志流</span>
          <a-tag :color="isReceiving ? 'green' : 'default'">
            {{ isReceiving ? '正在接收' : '已暂停' }}
          </a-tag>
        </div>
      </template>
      
      <template #extra>
        <a-space>
          <span class="log-count">显示: {{ filteredLogs.length }} / {{ logs.length }}</span>
          <a-button size="small" @click="toggleFullscreen">
            <template #icon>
              <FullscreenOutlined v-if="!isFullscreen" />
              <FullscreenExitOutlined v-else />
            </template>
          </a-button>
        </a-space>
      </template>
      
      <a-spin :spinning="initialLoading" tip="正在连接日志服务...">
        <div 
          ref="logContainerRef"
          class="log-display-container"
          :class="{ 
            'auto-scroll': autoScroll,
            'fullscreen': isFullscreen
          }"
        >
          <div v-if="filteredLogs.length === 0 && !isReceiving && !initialLoading" class="empty-logs">
            <a-empty description="暂无日志显示，请点击开始接收" />
          </div>
          <div 
            v-for="(log, index) in filteredLogs" 
            :key="index"
            :class="getLogLevelClass(log)"
            @click="showLogDetail(log, index)"
          >
            <span class="log-line-number">{{ index + 1 }}</span>
            <span class="log-timestamp">[{{ formatTimestamp(log.timestamp) }}]</span>
            <span class="log-level">[{{ log.level || 'INFO' }}]</span>
            <span class="log-module" v-if="log.module">[{{ log.module }}]</span>
            <span class="log-message" v-html="highlightKeyword(log.message, filterKeyword)"></span>
            <span class="log-source" v-if="log.source">{{ log.source }}</span>
          </div>
        </div>
      </a-spin>
    </a-card>

    <!-- 日志详情弹窗 -->
    <a-modal
      v-model:open="logDetailVisible"
      title="日志详情"
      width="800px"
      :footer="null"
    >
      <div v-if="selectedLog" class="log-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="行号">{{ selectedLogIndex + 1 }}</a-descriptions-item>
          <a-descriptions-item label="时间">{{ selectedLog.timestamp }}</a-descriptions-item>
          <a-descriptions-item label="级别">
            <a-tag :color="getLevelColor(selectedLog.level)">{{ selectedLog.level.toUpperCase() }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="模块">{{ selectedLog.module || '-' }}</a-descriptions-item>
          <a-descriptions-item label="来源" :span="2">{{ selectedLog.source || '-' }}</a-descriptions-item>
        </a-descriptions>
        
        <div class="log-content-detail">
          <h4>日志内容</h4>
          <pre>{{ selectedLog.message }}</pre>
        </div>
        
        <div v-if="selectedLog.stack" class="log-stack-detail">
          <h4>堆栈信息</h4>
          <pre>{{ selectedLog.stack }}</pre>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import {computed, nextTick, onMounted, onUnmounted, ref, watch} from 'vue'
import {
  ClearOutlined,
  DisconnectOutlined,
  DownloadOutlined,
  ExclamationCircleOutlined,
  FileTextOutlined,
  FullscreenExitOutlined,
  FullscreenOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined,
  ReloadOutlined,
  VerticalAlignBottomOutlined,
  WarningOutlined,
  WifiOutlined
} from '@ant-design/icons-vue'
import {message, Modal} from 'ant-design-vue'
import dayjs from 'dayjs'

// 响应式数据
const logs = ref([])
const filterKeyword = ref('')
const logLevel = ref(null)
const isReceiving = ref(false)
const autoScroll = ref(true)
const autoRefresh = ref(false)
const connecting = ref(false)
const initialLoading = ref(false)
const isFullscreen = ref(false)

// 日志详情弹窗
const logDetailVisible = ref(false)
const selectedLog = ref(null)
const selectedLogIndex = ref(0)

// WebSocket 相关
const websocket = ref(null)
const logContainerRef = ref(null)

// 定时器
let pollingTimer = null
let refreshTimer = null

// 常量
const WEBSOCKET_URL = import.meta.env.MODE === 'development' ? 'ws://localhost:8000/ws/logs' : `ws://${window.location.host}/ws/logs`
const POLLING_URL = '/admin/logs/realtime'
const MAX_LOGS = 1000

// 计算属性
const filteredLogs = computed(() => {
  let result = logs.value
  
  // 按级别过滤
  if (logLevel.value) {
    result = result.filter(log => log.level === logLevel.value)
  }
  
  // 按关键词过滤
  if (filterKeyword.value) {
    result = result.filter(log => 
      log.message.toLowerCase().includes(filterKeyword.value.toLowerCase())
    )
  }
  
  return result
})

// 统计计算属性
const errorCount = computed(() => {
  return logs.value.filter(log => log.level === 'error').length
})

const warnCount = computed(() => {
  return logs.value.filter(log => log.level === 'warn').length
})

const connectionStatus = computed(() => {
  return isReceiving.value ? '已连接' : '未连接'
})

// WebSocket 连接
const connectWebSocket = () => {
  try {
    websocket.value = new WebSocket(WEBSOCKET_URL)
    
    websocket.value.onopen = () => {
      console.log('WebSocket 连接已建立')
      connecting.value = false
      initialLoading.value = false
      message.success('实时日志连接成功')
    }
    
    websocket.value.onmessage = (event) => {
      try {
        const logData = JSON.parse(event.data)
        addLog(logData)
      } catch (error) {
        console.error('解析日志数据失败:', error)
      }
    }
    
    websocket.value.onerror = (error) => {
      console.error('WebSocket 错误:', error)
      connecting.value = false
      initialLoading.value = false
      message.error('WebSocket 连接失败，将使用轮询模式')
      startPolling()
    }
    
    websocket.value.onclose = () => {
      console.log('WebSocket 连接已关闭')
      connecting.value = false
      if (isReceiving.value) {
        message.warning('连接已断开，尝试重新连接...')
        setTimeout(() => {
          if (isReceiving.value) {
            connectWebSocket()
          }
        }, 3000)
      }
    }
  } catch (error) {
    console.error('创建 WebSocket 连接失败:', error)
    connecting.value = false
    initialLoading.value = false
    startPolling()
  }
}

// 轮询模式
const startPolling = () => {
  if (pollingTimer) {
    clearInterval(pollingTimer)
  }
  
  pollingTimer = setInterval(async () => {
    try {
      // 模拟获取新日志
      const mockLogs = generateMockLogs(Math.floor(Math.random() * 3) + 1)
      mockLogs.forEach(log => addLog(log))
    } catch (error) {
      console.error('轮询获取日志失败:', error)
    }
  }, 2000)
}

const stopPolling = () => {
  if (pollingTimer) {
    clearInterval(pollingTimer)
    pollingTimer = null
  }
}

// 添加日志
const addLog = (logData) => {
  logs.value.push({
    timestamp: logData.timestamp || new Date().toISOString(),
    level: logData.level || 'info',
    message: logData.message || '',
    source: logData.source || '',
    module: logData.module || '',
    stack: logData.stack || ''
  })
  
  // 限制日志数量
  if (logs.value.length > MAX_LOGS) {
    logs.value.shift()
  }
  
  // 自动滚动到底部
  if (autoScroll.value) {
    nextTick(() => {
      scrollToBottom()
    })
  }
}

// 生成模拟日志
const generateMockLogs = (count = 1) => {
  const levels = ['info', 'warn', 'error', 'debug']
  const modules = ['auth', 'database', 'cache', 'email', 'api', 'scheduler']
  const messages = [
    '用户登录成功，用户id: 12345',
    '数据库连接池已建立，连接数: 10',
    '处理API请求: GET /api/users',
    '发送邮件通知给用户: <EMAIL>',
    '缓存更新完成，键: user_session_12345',
    '系统性能监控：CPU使用率 45%',
    '错误：数据库连接超时，重试中...',
    '警告：内存使用率过高 85%',
    '调试：变量值检查 - userId: 12345',
    '任务调度器启动成功',
    '文件上传完成: document.pdf',
    '权限验证失败，用户无访问权限'
  ]
  
  const mockLogs = []
  for (let i = 0; i < count; i++) {
    const level = levels[Math.floor(Math.random() * levels.length)]
    mockLogs.push({
      timestamp: new Date().toISOString(),
      level: level,
      message: messages[Math.floor(Math.random() * messages.length)],
      source: `service-${Math.floor(Math.random() * 3) + 1}`,
      module: modules[Math.floor(Math.random() * modules.length)],
      stack: level === 'error' ? 'Error stack trace would be here...' : ''
    })
  }
  return mockLogs
}

// 切换接收状态
const toggleRealtimeLog = () => {
  if (isReceiving.value) {
    stopReceiving()
  } else {
    startReceiving()
  }
}

// 开始接收
const startReceiving = () => {
  connecting.value = true
  initialLoading.value = true
  isReceiving.value = true
  
  // 尝试 WebSocket 连接
  connectWebSocket()
}

// 停止接收
const stopReceiving = () => {
  isReceiving.value = false
  connecting.value = false
  
  if (websocket.value) {
    websocket.value.close()
    websocket.value = null
  }
  
  stopPolling()
  message.info('已停止接收实时日志')
}

// 重新连接
const refreshConnection = () => {
  if (isReceiving.value) {
    stopReceiving()
    setTimeout(() => {
      startReceiving()
    }, 1000)
  } else {
    message.info('请先开始接收日志')
  }
}

// 导出日志
const exportLogs = () => {
  try {
    const logData = logs.value.map(log => {
      return `[${formatTimestamp(log.timestamp)}] [${log.level.toUpperCase()}] [${log.module || 'SYSTEM'}] ${log.message}`
    }).join('\n')
    
    const blob = new Blob([logData], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `realtime-logs-${dayjs().format('YYYY-MM-DD-HH-mm-ss')}.txt`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    message.success('日志导出成功')
  } catch (error) {
    console.error('导出日志失败:', error)
    message.error('导出日志失败')
  }
}

// 清空日志
const clearLogs = () => {
  Modal.confirm({
    title: '确认清空',
    content: '确定要清空所有日志吗？此操作不可恢复。',
    onOk() {
      logs.value = []
      message.success('日志已清空')
    }
  })
}

// 滚动到底部
const scrollToBottom = () => {
  if (logContainerRef.value) {
    logContainerRef.value.scrollTop = logContainerRef.value.scrollHeight
  }
}

// 切换全屏
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
}

// 显示日志详情
const showLogDetail = (log, index) => {
  selectedLog.value = log
  selectedLogIndex.value = index
  logDetailVisible.value = true
}

// 应用过滤器
const applyFilter = () => {
  // 过滤逻辑已在计算属性中实现
}

// 格式化时间戳
const formatTimestamp = (timestamp) => {
  return dayjs(timestamp).format('HH:mm:ss.SSS')
}

const formatTime = (timestamp) => {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss')
}

// 获取日志级别样式类
const getLogLevelClass = (log) => {
  return {
    'log-entry': true,
    'log-info': log.level === 'info',
    'log-warn': log.level === 'warn',
    'log-error': log.level === 'error',
    'log-debug': log.level === 'debug'
  }
}

// 获取级别颜色
const getLevelColor = (level) => {
  const colors = {
    error: 'red',
    warn: 'orange',
    info: 'blue',
    debug: 'green'
  }
  return colors[level] || 'default'
}

// 高亮关键词
const highlightKeyword = (text, keyword) => {
  if (!keyword) return text
  const regex = new RegExp(`(${keyword})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

// 设置自动刷新
const setupAutoRefresh = () => {
  if (autoRefresh.value && !refreshTimer) {
    refreshTimer = setInterval(() => {
      if (!isReceiving.value) {
        // 生成一些模拟日志
        const mockLogs = generateMockLogs(Math.floor(Math.random() * 2) + 1)
        mockLogs.forEach(log => addLog(log))
      }
    }, 5000)
  } else if (!autoRefresh.value && refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 监听自动刷新变化
watch(autoRefresh, () => {
  setupAutoRefresh()
})

// 组件挂载
onMounted(() => {
  // 初始化时生成一些模拟日志
  const initialLogs = generateMockLogs(20)
  initialLogs.forEach(log => addLog(log))
  
  // 设置自动刷新
  setupAutoRefresh()
})

// 组件卸载
onUnmounted(() => {
  stopReceiving()
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})

</script>

<style scoped>
.realtime-log {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

/* 页面头部 */
.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.log-header h1 {
  margin: 0;
  color: #1890ff;
  font-size: 24px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-actions .ant-space {
  gap: 12px !important;
}

/* 统计卡片 */
.stats-row {
  margin-bottom: 24px;
}

.stats-row .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stats-row .ant-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* 控制面板 */
.control-panel {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.control-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.control-left,
.control-right {
  display: flex;
  align-items: center;
}

.control-left .ant-space,
.control-right .ant-space {
  gap: 12px !important;
}

/* 日志容器 */
.log-container {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.log-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.log-count {
  font-size: 12px;
  color: #8c8c8c;
}

/* 日志显示区域 */
.log-display-container {
  height: 500px;
  background: #1e1e1e;
  color: #d4d4d4;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  padding: 16px;
  overflow-y: auto;
  border-radius: 6px;
}

.log-display-container.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  height: 100vh;
  border-radius: 0;
}

.log-display-container.auto-scroll {
  scroll-behavior: smooth;
}

/* 空状态 */
.empty-logs {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #8c8c8c;
}

/* 日志条目 */
.log-entry {
  display: flex;
  align-items: flex-start;
  margin-bottom: 2px;
  padding: 4px 0;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-radius: 3px;
}

.log-entry:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.log-line-number {
  width: 50px;
  color: #858585;
  text-align: right;
  margin-right: 12px;
  user-select: none;
  flex-shrink: 0;
  font-size: 11px;
}

.log-timestamp {
  color: #858585;
  margin-right: 8px;
  flex-shrink: 0;
  font-size: 11px;
}

.log-level {
  margin-right: 8px;
  font-weight: bold;
  padding: 1px 4px;
  border-radius: 2px;
  font-size: 10px;
  flex-shrink: 0;
  min-width: 45px;
  text-align: center;
}

.log-module {
  color: #9c9c9c;
  margin-right: 8px;
  font-size: 11px;
  flex-shrink: 0;
}

.log-message {
  flex: 1;
  word-break: break-all;
  line-height: 1.4;
}

.log-source {
  color: #666;
  margin-left: 8px;
  font-size: 10px;
  flex-shrink: 0;
}

/* 日志级别样式 */
.log-info .log-level {
  background: #1890ff;
  color: white;
}

.log-info .log-message {
  color: #d4d4d4;
}

.log-warn .log-level {
  background: #faad14;
  color: white;
}

.log-warn .log-message {
  color: #ffa726;
}

.log-error .log-level {
  background: #ff4d4f;
  color: white;
}

.log-error .log-message {
  color: #ff6b6b;
}

.log-debug .log-level {
  background: #52c41a;
  color: white;
}

.log-debug .log-message {
  color: #66bb6a;
}

/* 级别标签样式 */
.level-error {
  background: #ff4d4f !important;
  color: white !important;
}

.level-warn {
  background: #faad14 !important;
  color: white !important;
}

.level-info {
  background: #1890ff !important;
  color: white !important;
}

.level-debug {
  background: #52c41a !important;
  color: white !important;
}

/* 日志详情弹窗 */
.log-detail .ant-descriptions {
  margin-bottom: 16px;
}

.log-content-detail,
.log-stack-detail {
  margin-top: 16px;
}

.log-content-detail h4,
.log-stack-detail h4 {
  margin-bottom: 8px;
  color: #262626;
  font-weight: 600;
}

.log-content-detail pre,
.log-stack-detail pre {
  background: #1e1e1e;
  color: #d4d4d4;
  padding: 12px;
  border-radius: 4px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 300px;
  overflow-y: auto;
  margin: 0;
}

/* 高亮搜索关键词 */
:deep(mark) {
  background-color: #fff3cd !important;
  color: #856404 !important;
  padding: 1px 2px !important;
  border-radius: 2px !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .realtime-log {
    padding: 12px;
  }
  
  .log-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .log-header h1 {
    text-align: center;
    font-size: 20px;
  }
  
  .control-row {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .control-left,
  .control-right {
    justify-content: center;
  }
  
  .log-display-container {
    height: 400px;
    font-size: 12px;
    padding: 12px;
  }
  
  .log-line-number {
    width: 35px;
    margin-right: 8px;
  }
  
  .log-level {
    min-width: 35px;
    font-size: 9px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.log-entry {
  animation: fadeIn 0.3s ease;
}

.stats-row .ant-card {
  animation: fadeIn 0.5s ease;
}

/* 自定义滚动条 */
.log-display-container::-webkit-scrollbar {
  width: 8px;
}

.log-display-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.log-display-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

.log-display-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 加载状态 */
.ant-spin-nested-loading {
  height: 100%;
}

.ant-spin-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 标签样式 */
.ant-tag {
  border-radius: 4px;
  font-size: 11px;
}

/* 按钮样式 */
.ant-btn-lg {
  height: 40px;
  padding: 0 20px;
  font-size: 14px;
}

/* 统计数字样式 */
.ant-statistic-content {
  font-size: 20px;
  font-weight: 600;
}

.ant-statistic-title {
  font-size: 12px;
  color: #8c8c8c;
}
</style>