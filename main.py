# 标准库模块
import logging
import os
import pathlib
import socket
import sys
import time
from contextlib import asynccontextmanager
from typing import Any, Dict

import psutil  # 添加psutil导入用于获取系统资源信息

# 第三方库模块
import uvicorn
from fastapi import Depends, FastAPI, HTTPException, Request, WebSocket, status
from fastapi import __version__ as fastapi_version
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.routing import APIRoute  # 从正确的模块导入APIRoute
from fastapi.staticfiles import StaticFiles

# 从config导入应用配置
from config import (  # 添加数据库配置导入
    APP_NAME,
    APP_VERSION,
    DOCS_URL,
    FASTAPI_DESCRIPTION,
    FASTAPI_TITLE,
    FASTAPI_VERSION,
    OPENAPI_URL,
    REDOC_URL,
    数据库配置,
)

# 修正：连接池实例在启动初始化中使用
# IP限速器实例在中间件中使用，这里不需要直接导入
from 中间件.IP限流 import IP限流中间件
from 中间件.请求日志 import 日志中间件

# 延迟导入路由，避免循环导入问题
from 日志 import 处理日志WebSocket连接, 系统日志器, 错误日志器
from 日志.实时日志服务 import 获取日志类型和级别
from 核心.启动初始化 import 初始化应用服务
from 核心.资源清理 import 清理应用资源
# Coze相关路由已移除
from 路由.产品 import 产品路由
from 路由.仪表板路由 import 仪表板路由
from 路由.商品路由 import 商品路由
from 路由.团队权限管理 import 团队权限管理路由
from 路由.团队管理 import 团队管理路由
from 路由.团队邀请管理 import 团队邀请管理路由

# 删除重复实例化：异步连接池实例 = 异步数据库连接池() # 创建实例
# 现在直接使用导入的 异步连接池实例
# 直接导入所有路由模块 - 支持IDE重构
from 路由.基础路由 import 基础路由
from 路由.外部服务 import 外部服务路由
from 路由.客户邀请管理 import 客户邀请管理路由
from 路由.工作台路由 import 工作台路由
from 路由.店铺管理 import 店铺管理路由
from 路由.微信对接看板路由 import 微信对接看板路由
from 路由.用户_微信小店达人路由 import 微信小店达人路由
from 路由.用户_微信路由 import 微信路由
from 路由.用户_微信自动化路由 import 微信自动化路由

from 路由.样品信息路由 import 样品路由
from 路由.用户_LangChain智能体路由 import 用户LangChain智能体路由
from 路由.用户_专用智能体接口路由 import 用户专用智能体接口路由
from 路由.用户_团队业务指标 import 用户团队业务指标路由
from 路由.用户_团队数据看板 import 用户团队数据看板路由
from 路由.用户_旧 import 用户路由
from 路由.用户_账号相关 import 用户账号路由
from 路由.用户_达人邀约路由 import 用户达人邀约路由
from 路由.用户通知路由 import 用户通知路由
from 路由.管理_API接口 import SuperAdminAPI路由
from 路由.管理_LangChain智能体路由 import 管理LangChain智能体路由
from 路由.管理_LangChain模型路由 import 管理LangChain模型路由

from 路由.管理_仪表板路由 import 管理仪表板路由
from 路由.管理_激活码管理 import 激活码管理路由
from 路由.管理_用户管理路由 import 用户管理路由
from 路由.管理_系统监控 import 系统监控路由
from 路由.管理_通告管理 import 通告管理路由
from 路由.管理_公司管理 import 公司管理路由
from 路由.系统 import 系统路由
from 路由.系统更新 import 系统更新路由
from 路由.线索路由 import 线索路由
from 路由.订单 import 订单路由
from 路由.达人 import 达人路由

# 获取当前文件所在目录的绝对路径
BASE_DIR = os.path.dirname(os.path.abspath(__file__))

# 需要保留标准logging模块用于设置外部库的日志级别

# 禁用watchfiles和相关模块的DEBUG日志
logging.getLogger("watchfiles").setLevel(logging.INFO)
logging.getLogger("watchgod").setLevel(logging.INFO)
logging.getLogger("uvicorn.watchgod").setLevel(logging.INFO)
logging.getLogger("uvicorn.reload").setLevel(logging.INFO)


# -----------------------------
# 应用生命周期管理
# -----------------------------
@asynccontextmanager
async def 生命周期(app_instance: FastAPI):
    """应用程序生命周期管理"""
    系统日志器.info("服务正在启动...")
    启动时间 = time.time()
    try:
        # 系统启动前环境检查
        from 核心.启动初始化 import 系统启动前检查

        检查结果 = await 系统启动前检查()
        if not 检查结果:
            系统日志器.warning(
                "⚠️ 系统启动前检查发现问题，但继续启动（部分功能可能受限）"
            )

        # 在初始化服务之前注册路由
        注册所有路由()

        # 调用新的初始化服务函数，传递必要的参数
        await 初始化应用服务(
            app_instance, BASE_DIR, APP_NAME, APP_VERSION
        )  # ++ MODIFIED

        # 原有的 yield 语句保持不变，代表应用正常运行阶段
        yield
    except Exception as e:
        错误日志器.critical(f"生命周期启动事件处理异常: {e}", exc_info=True)
        raise
    finally:
        # 计算运行时间
        运行时间 = time.time() - 启动时间
        系统日志器.info(f"应用运行时间: {运行时间:.2f} 秒")  # 记录运行时间

        # 调用新的资源清理函数
        await 清理应用资源(APP_NAME, 运行时间)  # ++ MODIFIED (APP_NAME 从 config 导入)


# -----------------------------
# 创建 FastAPI 应用实例
# -----------------------------
app = FastAPI(
    title=FASTAPI_TITLE,  # ++ 使用配置
    version=FASTAPI_VERSION,  # ++ 使用配置
    lifespan=生命周期,
    description=FASTAPI_DESCRIPTION,  # ++ 使用配置
    openapi_url=OPENAPI_URL,  # ++ 使用配置
    docs_url=DOCS_URL,  # ++ 使用配置
    redoc_url=REDOC_URL,  # ++ 使用配置
)

# 配置跨域
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:5173",  # Vite 开发服务器
        "http://localhost:4173",  # Vite 预览服务器
        "http://localhost:3000",  # 可能的其他前端开发端口
        "http://localhost:8080",  # 管理后台前端开发端口
        "http://127.0.0.1:5173",
        "http://127.0.0.1:4173",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:8080",  # 管理后台前端开发端口
        "https://invite.limob.cn",  # 生产环境域名 - 修复域名匹配
        "https://crm.limob.cn",  # 备用生产环境域名
        # 注意：移除通配符"*"以兼容credentials=True
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["Content-Disposition"],  # 允许前端访问下载文件的头信息
    max_age=600,  # 预检请求缓存10分钟
)


# -----------------------------
# 中间件注册
# -----------------------------
app.middleware("http")(IP限流中间件)
app.middleware("http")(日志中间件)


# -----------------------------
# 异常处理器
# -----------------------------
@app.exception_handler(HTTPException)
async def 全局异常处理器(_: Request, exc: HTTPException):
    """处理HTTP异常"""
    # 如果detail已经是字典格式并包含status字段，则直接使用
    if isinstance(exc.detail, dict) and "status" in exc.detail:
        # 确保包含所有必需字段
        detail_dict: Dict[str, Any] = dict(exc.detail)  # 明确类型注解以支持混合类型
        if "message" not in detail_dict and "msg" in detail_dict:
            detail_dict["message"] = detail_dict.get("msg", "")
            detail_dict.pop("msg", None)
        if "data" not in detail_dict:
            detail_dict["data"] = None
        return JSONResponse(status_code=exc.status_code, content=detail_dict)

    # 否则构造标准响应格式
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "status": exc.status_code,
            "message": str(exc.detail)
            if not isinstance(exc.detail, dict)
            else exc.detail.get("message", "未知错误"),
            "data": None,
        },
    )


@app.exception_handler(Exception)
async def 全局未知异常处理器(_: Request, exc: Exception):
    """处理未知异常"""
    # 使用日志记录异常，而不是print
    错误日志器.error(f"全局未处理异常: {exc}", exc_info=True)

    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "status": status.HTTP_500_INTERNAL_SERVER_ERROR,
            "message": f"服务器内部错误: {str(exc)}",
            "data": None,
        },
    )


@app.exception_handler(RequestValidationError)
async def 自定义验证异常处理器(request: Request, exc: RequestValidationError):
    """处理请求验证错误，提供友好错误信息"""
    error_messages = []

    for error in exc.errors():
        # 提取字段路径和错误类型
        field = ".".join(str(loc) for loc in error["loc"] if loc != "body")
        error_type = error["type"]

        # 生成友好提示
        if (
            error_type == "value_error.missing" or error_type == "missing"
        ):  # 统一处理 missing 类型
            message = "该字段是必填项"
        elif error_type == "type_error.integer":
            message = "需要一个有效的整数"
        elif error_type == "type_error.float":
            message = "需要一个有效的数字"
        elif error_type == "value_error.email":
            message = "需要一个有效的邮箱地址"
        else:
            message = f"无效的值 ({error_type})"

        error_messages.append({"field": field, "msg": message})

    client_ip = request.client.host if request.client else "未知IP"
    错误日志器.warning(f"请求参数验证失败: {error_messages} - 来自 IP: {client_ip}")
    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content={
            "status": 400,
            "message": "请求参数错误",
            "data": {"errors": error_messages},
        },
    )


# -----------------------------
# 路由注册
# -----------------------------
def 注册所有路由():
    """
    直接注册所有应用路由，支持IDE重构功能。
    这种方式更清晰、更可控，支持IDE的重命名重构。
    """
    成功计数 = 0
    跳过计数 = 0
    失败详情 = []

    # 路由配置: (路由对象, [tags], URL前缀)
    # 前缀是可选的
    路由配置 = [
        (基础路由, ["基础"]),
        (用户路由, ["用户接口"]),
        (用户账号路由, ["用户账号"], "/user"),
        (用户通知路由, ["用户通知"], "/user"),
        (用户达人邀约路由, ["用户达人邀约"], "/invite"),
        (系统路由, ["系统接口"]),
        (订单路由, ["订单接口"], "/order"),
        (微信路由, ["微信"], "/wechat"),
        (微信对接看板路由, ["微信对接看板"], "/wechat/board"),
        # 微信达人管理路由，优化后移除冗余接口
        (微信小店达人路由, ["微信达人管理"], "/wechat-kol"),
        # 微信自动化路由
        (微信自动化路由, ["微信自动化"], "/wechat/automation"),
        # AI相关路由已移除（Coze相关）
        # LangChain智能体系统
        (管理LangChain智能体路由, ["管理-LangChain智能体"], "/admin/langchain"),
        (管理LangChain模型路由, ["管理-LangChain模型"], "/admin/langchain"),

        (用户LangChain智能体路由, ["用户-LangChain智能体"], "/user/langchain"),
        (用户专用智能体接口路由, ["用户-专用智能体接口"], "/user/specialized-agents"),
        # 管理相关路由
        (SuperAdminAPI路由, ["SuperAdmin接口"], "/admin"),
        (用户管理路由, ["管理员-用户管理"], "/admin/users"),
        (系统监控路由, ["管理员-系统监控"]),
        (管理仪表板路由, ["管理员-仪表板"], "/admin/dashboard"),
        (激活码管理路由, ["管理员-激活码管理"]),
        (系统更新路由, ["系统更新"], "/system"),
        (团队管理路由, ["团队管理"], "/team"),
        (团队权限管理路由, ["团队权限管理"], "/team/permissions"),
        (团队邀请管理路由, ["团队邀请管理"], "/team/invite"),
        (用户团队数据看板路由, ["用户团队数据看板"], "/team/dashboard"),
        (用户团队业务指标路由, ["用户团队业务指标"], "/team/metrics"),
        (客户邀请管理路由, ["客户邀请管理"], "/customer/invite"),
        # 业务功能路由
        (产品路由, ["产品管理"], "/product"),
        (店铺管理路由, ["店铺管理"], "/store"),
        (样品路由, ["样品信息"], "/samples"),
        (达人路由, ["达人公海"], "/kol"),
        (线索路由, ["线索管理"], "/leads"),
        (外部服务路由, ["外部服务"]),
        (仪表板路由, ["仪表板"], "/dashboard"),
        (工作台路由, ["工作台"], "/workspace"),

        (商品路由, ["商品管理"], "/items"),
        (通告管理路由, ["通告管理"], "/admin/announcements"),
    (公司管理路由, ["管理员-公司管理"], "/admin"),
    ]

    for item in 路由配置:
        路由对象, tags, *可选前缀 = item
        prefix = 可选前缀[0] if 可选前缀 else ""
        try:
            app.include_router(路由对象, prefix=prefix, tags=tags)
            成功计数 += 1
        except Exception as e:
            # 提供更详细的错误信息
            路由名称 = getattr(路由对象, "__name__", str(路由对象))
            错误信息 = f"  - 路由: {路由名称}, 错误: {e}"
            系统日志器.warning(f"⚠️  路由加载失败:{错误信息}")
            失败详情.append(错误信息)
            跳过计数 += 1

    系统日志器.info(f"路由注册完成: 成功 {成功计数} 个, 跳过 {跳过计数} 个")
    if 失败详情:
        系统日志器.warning("以下路由未能成功加载:")
        for detail in 失败详情:
            系统日志器.warning(detail)


# 挂载静态文件目录 - 模板目录已废弃，管理前端使用Vue SPA
# app.mount("/limob/css", StaticFiles(directory=os.path.join(BASE_DIR, "模板/css")), name="css")  # 已废弃
# app.mount("/limob/js", StaticFiles(directory=os.path.join(BASE_DIR, "模板/js")), name="js")    # 已废弃

# 确保静态目录存在
静态目录 = os.path.join(BASE_DIR, "静态")
pathlib.Path(静态目录).mkdir(parents=True, exist_ok=True)
app.mount("/static", StaticFiles(directory=静态目录), name="static")


# 实时日志WebSocket路由
@app.websocket("/limob/admin/ws/logs")
async def 日志WebSocket路由(
    websocket: WebSocket, 日志类型_级别=Depends(获取日志类型和级别)
):
    日志类型, 日志级别 = 日志类型_级别
    await 处理日志WebSocket连接(websocket, 日志类型, 日志级别)


# -----------------------------
# 这些装饰器已被移除以避免 FastAPI deprecation 警告
# 所有启动和关闭逻辑现在通过 生命周期() 函数处理
# -----------------------------


# -----------------------------
# 应用启动配置
# -----------------------------
def 检查端口占用(端口: int) -> bool:
    """检查指定端口是否被占用"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            result = sock.connect_ex(("127.0.0.1", 端口))
            return result == 0
    except Exception:
        return False


def 打印启动信息():
    """打印应用启动信息"""
    # 准备显示内容
    应用标题 = f"🚀 {APP_NAME}"
    版本信息 = f"v{APP_VERSION}"
    系统信息 = f"Python {sys.version.split()[0]}  │  FastAPI {fastapi_version}"

    # 获取启动时间
    启动时间 = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())

    # 获取路由数量
    api_路由数 = len([route for route in app.routes if isinstance(route, APIRoute)])

    # 获取系统资源信息
    进程 = psutil.Process(os.getpid())
    内存使用 = 进程.memory_info().rss / 1024 / 1024  # MB
    CPU核心 = psutil.cpu_count(logical=True)

    # 显示环境和系统信息
    环境信息 = f"🌍 环境: {'开发环境' if os.getenv('ENVIRONMENT', '').lower() == 'development' else '生产环境'}"
    资源信息 = f"🖥️ 系统资源: CPU {CPU核心}核 | 内存占用 {内存使用:.1f}MB"
    数据库信息 = f"🗃️ 数据库: {数据库配置.get('host', '未配置')} ({数据库配置.get('database', '未配置')})"
    连接池信息 = "🔄 连接池: 最大连接数 15"

    # 网络信息
    本地访问 = "🌐 本地访问: http://127.0.0.1:8000"
    # 尝试获取本机IP地址
    局域网访问 = ""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        局域网访问 = f"🔗 局域网访问: http://{ip}:8000"
        s.close()
    except (OSError, socket.error):
        pass

    API文档 = f"📖 API文档: http://127.0.0.1:8000{DOCS_URL}"
    工作目录 = f"📁 工作目录: {os.getcwd()}"

    # 应用统计信息
    应用统计 = (
        f"📊 路由统计: API {api_路由数} 个 | 模块 {len(app.routes) - api_路由数} 个"
    )
    启动时间信息 = f"⏱️ 启动时间: {启动时间}"
    版权信息 = f"© {time.strftime('%Y')} 灵能科技 | 保留所有权利"

    # 计算所需的最大宽度
    内容列表 = [
        应用标题,
        版本信息,
        系统信息,
        环境信息,
        资源信息,
        数据库信息,
        连接池信息,
        本地访问,
        API文档,
        工作目录,
        应用统计,
        启动时间信息,
        版权信息,
    ]
    if 局域网访问:
        内容列表.append(局域网访问)

    # 计算实际文本长度，去除ANSI颜色代码并考虑中文字符宽度
    def 计算实际文本长度(文本):
        # 去除ANSI颜色代码
        文本 = 文本.replace("\033[1m", "").replace("\033[0m", "")
        文本 = (
            文本.replace("\033[32m", "").replace("\033[34m", "").replace("\033[33m", "")
        )

        # 计算中文字符占用的宽度（中文字符通常占用2个英文字符的宽度）
        宽度 = 0
        for 字符 in 文本:
            if "\u4e00" <= 字符 <= "\u9fff":  # 中文字符范围
                宽度 += 2
            else:
                宽度 += 1
        return 宽度

    最大内容宽度 = max(计算实际文本长度(line) for line in 内容列表)
    # 设置最小宽度为64，最大宽度为96，避免过窄或过宽
    横幅宽度 = max(64, min(96, 最大内容宽度 + 4))  # +4 为左右边距
    内容宽度 = 横幅宽度 - 2  # 去掉左右边框的宽度

    # 如果工作目录太长，进行截断
    if 计算实际文本长度(工作目录) > 内容宽度:
        截断长度 = 内容宽度 - 15  # 保留 "📁 工作目录: ..." 的空间
        工作目录 = f"📁 工作目录: ...{os.getcwd()[-截断长度:]}"

    # 生成自适应横幅
    顶部边框 = "╔" + "═" * (横幅宽度 - 2) + "╗"
    标题分隔线 = "╟" + "─" * (横幅宽度 - 2) + "╢"
    分隔线 = "╠" + "═" * (横幅宽度 - 2) + "╣"
    底部边框 = "╚" + "═" * (横幅宽度 - 2) + "╝"

    # 内容行生成函数
    def 生成内容行(内容, 居中=False, 加粗=False, 颜色=None):
        原始内容 = 内容
        内容文本 = 内容

        # 应用格式
        if 加粗:
            内容文本 = f"\033[1m{内容文本}\033[0m"  # ANSI转义序列加粗
        if 颜色 == "绿色":
            内容文本 = f"\033[32m{内容文本}\033[0m"  # 绿色
        elif 颜色 == "蓝色":
            内容文本 = f"\033[34m{内容文本}\033[0m"  # 蓝色
        elif 颜色 == "黄色":
            内容文本 = f"\033[33m{内容文本}\033[0m"  # 黄色

        # 计算填充，考虑中文宽度
        内容长度 = 计算实际文本长度(原始内容)
        if 居中:
            左填充 = (内容宽度 - 内容长度) // 2
            右填充 = 内容宽度 - 内容长度 - 左填充
            return f"║{' ' * 左填充}{内容文本}{' ' * 右填充}║"
        else:
            return f"║  {内容文本}{' ' * (内容宽度 - 内容长度 - 2)}║"

    # 构建横幅
    横幅 = f"""
{顶部边框}
{生成内容行(应用标题, 居中=True, 加粗=True, 颜色="绿色")}
{生成内容行(版本信息, 居中=True)}
{分隔线}
{生成内容行(系统信息, 居中=True)}
{生成内容行(启动时间信息, 居中=True, 颜色="蓝色")}
{标题分隔线}
{生成内容行(环境信息)}
{生成内容行(资源信息)}
{生成内容行(数据库信息)}
{生成内容行(连接池信息)}
{生成内容行(应用统计)}
{标题分隔线}
{生成内容行(本地访问)}"""

    # 添加局域网访问信息（如果有）
    if 局域网访问:
        横幅 += f"\n{生成内容行(局域网访问)}"

    横幅 += f"""
{生成内容行(API文档)}
{生成内容行(工作目录)}
{标题分隔线}
{生成内容行(版权信息, 居中=True, 颜色="黄色")}
{底部边框}
"""
    print(横幅)
    系统日志器.info("启动信息已显示")


def 获取服务器配置() -> dict:
    """获取服务器启动配置"""
    return {
        "app": "main:app",
        "host": "0.0.0.0",
        "port": 8000,
        "reload": True,
        "reload_excludes": [
            "logs/*",
            "日志/文件/*",
            "*.log",
            "__pycache__/*",
            "*.pyc",
            "静态/*",
            "数据/缓存/*",
            "数据/临时文件/*",
        ],
        "reload_dirs": ["路由", "服务", "数据", "工具", "中间件", "核心"],
        "log_level": "info",
    }


# -----------------------------
# 应用入口
# -----------------------------
if __name__ == "__main__":
    try:
        # 避免重复显示，只在主入口显示一次启动信息
        已显示启动信息 = False

        if not 已显示启动信息:
            打印启动信息()
            已显示启动信息 = True

        # 检查端口占用
        if 检查端口占用(8000):
            错误日志器.warning("警告: 端口 8000 已被占用，服务可能无法启动!")

        # 启动服务器
        uvicorn.run(**获取服务器配置())

    except Exception as e:
        错误日志器.critical(f"应用程序启动错误: {e}", exc_info=True)
        sys.exit(1)
