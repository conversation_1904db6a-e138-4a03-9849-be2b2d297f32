#!/usr/bin/env python3
"""
MySQL语法检测工具 - 仅用于检测，不自动修复
"""

import os
import re
from typing import List, Dict

def find_mysql_issues(directory: str = '.') -> List[Dict]:
    """查找MySQL语法问题"""
    issues = []
    
    # SQL关键词
    sql_keywords = [
        'SELECT', 'INSERT', 'UPDATE', 'DELETE', 'WHERE', 'FROM', 'JOIN',
        'LEFT JOIN', 'RIGHT JOIN', 'INNER JOIN', 'ORDER BY', 'GROUP BY',
        'HAVING', 'LIMIT', 'OFFSET', 'VALUES', 'SET'
    ]
    
    def is_sql_line(line: str) -> bool:
        """判断是否是SQL相关的行"""
        line_upper = line.upper()
        return any(keyword in line_upper for keyword in sql_keywords) or \
               'SQL' in line_upper or 'sql' in line or \
               '查询' in line or '插入' in line or '更新' in line or '删除' in line

    def is_string_format(line: str) -> bool:
        """判断%s是否用于字符串格式化而非SQL占位符"""
        # 常见的字符串格式化模式
        string_format_patterns = [
            r'\.strftime\([\'"][^\'\"]*%s',  # strftime('%Y-%m-%d %H:%M:%S')
            r'f[\'"][^\'\"]*%s',             # f"string with %s"
            r'[\'"][^\'\"]*%s[^\'\"]*[\'"]', # "string with %s"
            r'%\([^)]+\)s',                  # %(name)s 格式
            r'\.format\(',                   # .format() 方法
            r'%[sd]',                        # %s, %d 等格式化符号
        ]

        return any(re.search(pattern, line) for pattern in string_format_patterns)
    
    for root, dirs, files in os.walk(directory):
        # 跳过虚拟环境和缓存目录
        if any(skip in root for skip in ['__pycache__', '.venv', '.git', 'mysql_migration_backup']):
            continue
            
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    lines = content.split('\n')
                    for line_num, line in enumerate(lines, 1):
                        # 检查%s占位符（MySQL语法，需要转换为PostgreSQL的$1, $2...）
                        if '%s' in line and is_sql_line(line) and not is_string_format(line):
                            issues.append({
                                'file': file_path,
                                'line': line_num,
                                'type': 'placeholder',
                                'content': line.strip(),
                                'issue': 'MySQL %s placeholder (需要转换为PostgreSQL $1, $2...)'
                            })
                        
                        # 检查反引号（但排除正则表达式）
                        if '`' in line and is_sql_line(line) and 'r"' not in line and "r'" not in line:
                            issues.append({
                                'file': file_path,
                                'line': line_num,
                                'type': 'backtick',
                                'content': line.strip(),
                                'issue': 'MySQL backticks'
                            })
                        
                        # 检查参数传递方式（list vs tuple）
                        if re.search(r'执行查询.*\[.*\]', line) or re.search(r'execute.*\[.*\]', line):
                            issues.append({
                                'file': file_path,
                                'line': line_num,
                                'type': 'parameter',
                                'content': line.strip(),
                                'issue': 'Using list instead of tuple for parameters'
                            })
                            
                except Exception as e:
                    print(f"Error reading {file_path}: {e}")
    
    return issues

def main():
    print("MySQL语法检测工具")
    print("=" * 50)

    # 检测问题
    print("\n检测MySQL语法问题...")
    issues = find_mysql_issues('.')
    
    if not issues:
        print("没有发现MySQL语法问题！")
        return
    
    # 过滤项目文件（排除第三方库）
    project_issues = [issue for issue in issues if not any(skip in issue['file'] for skip in ['.venv', '__pycache__'])]
    
    if not project_issues:
        print("项目文件中没有发现MySQL语法问题！")
        return
    
    # 按类型统计
    placeholder_files = set()
    backtick_files = set()
    parameter_files = set()
    
    for issue in project_issues:
        if issue['type'] == 'placeholder':
            placeholder_files.add(issue['file'])
        elif issue['type'] == 'backtick':
            backtick_files.add(issue['file'])
        elif issue['type'] == 'parameter':
            parameter_files.add(issue['file'])
    
    print(f"\n发现 {len(project_issues)} 个问题，涉及 {len(set(issue['file'] for issue in project_issues))} 个文件")
    print(f"[高优先级] %s占位符问题: {len(placeholder_files)} 个文件")
    print(f"[中优先级] 反引号问题: {len(backtick_files)} 个文件")
    print(f"[低优先级] 参数传递问题: {len(parameter_files)} 个文件")

    # 显示详细问题信息
    def show_issues_by_type(issues, issue_type, title):
        type_issues = [i for i in issues if i['type'] == issue_type]
        if type_issues:
            print(f"\n{title}:")
            files_dict = {}
            for issue in type_issues:
                file_path = issue['file']
                if file_path not in files_dict:
                    files_dict[file_path] = []
                files_dict[file_path].append(issue)

            for file_path in sorted(files_dict.keys()):
                print(f"\n  文件: {file_path}")
                for issue in files_dict[file_path][:5]:  # 每个文件最多显示5个问题
                    print(f"    行 {issue['line']:3d}: {issue['content']}")
                if len(files_dict[file_path]) > 5:
                    print(f"    ... 还有 {len(files_dict[file_path]) - 5} 个问题")

    # 显示各类问题的详细信息
    show_issues_by_type(project_issues, 'placeholder', "[高优先级] %s占位符问题")
    show_issues_by_type(project_issues, 'backtick', "[中优先级] 反引号问题")
    show_issues_by_type(project_issues, 'parameter', "[低优先级] 参数传递问题")

    print("\n修复建议：")
    print("1. 优先修复%s占位符问题，这些会导致SQL语法错误")
    print("2. 将%s替换为PostgreSQL的$1, $2, $3...格式")
    print("3. 确保参数使用tuple而不是list传递")

if __name__ == "__main__":
    main()
