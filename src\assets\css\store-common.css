/* 店铺管理模块统一样式 - 与达人管理模块保持一致 */

/* ==================== 基础页面样式 ==================== */
.store-page {
  background: #ffffff;
  min-height: 100vh;
}

.store-page-header {
  margin-bottom: 24px;
  padding: 0 4px;
}

.store-page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1d1d1d;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.store-title-icon {
  color: #1890ff;
  font-size: 28px;
}

.store-page-description {
  font-size: 14px;
  color: #666;
  margin: 0;
  line-height: 1.5;
}

/* ==================== 卡片样式 ==================== */
.store-page :deep(.ant-card) {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e8e8e8;
  margin-bottom: 16px;
}

.store-page :deep(.ant-card-head) {
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
}

.store-page :deep(.ant-card-head-title) {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.store-page :deep(.ant-card:hover) {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* ==================== 表格样式 ==================== */
.store-page :deep(.ant-table) {
  border-radius: 8px;
}

.store-page :deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
  font-weight: 500;
  color: #262626;
}

.store-page :deep(.ant-table-tbody > tr:hover > td) {
  background: #f5f5f5;
}

.store-page :deep(.ant-table-pagination) {
  margin-top: 16px;
  text-align: right;
}

/* 表格滚动条样式 */
.store-page :deep(.ant-table-body::-webkit-scrollbar) {
  height: 8px;
}

.store-page :deep(.ant-table-body::-webkit-scrollbar-track) {
  background: #f1f1f1;
  border-radius: 4px;
}

.store-page :deep(.ant-table-body::-webkit-scrollbar-thumb) {
  background: #c1c1c1;
  border-radius: 4px;
}

.store-page :deep(.ant-table-body::-webkit-scrollbar-thumb:hover) {
  background: #a8a8a8;
}

/* ==================== 按钮样式 ==================== */
.store-page :deep(.ant-btn-primary) {
  background: #1890ff;
  border-color: #1890ff;
  border-radius: 6px;
  font-weight: 500;
}

.store-page :deep(.ant-btn-primary:hover) {
  background: #40a9ff;
  border-color: #40a9ff;
}

.store-page :deep(.ant-btn) {
  border-radius: 6px;
  font-weight: 500;
}

.store-page :deep(.ant-btn-link) {
  padding: 0;
  height: auto;
}

/* ==================== 输入框样式 ==================== */
.store-page :deep(.ant-input) {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
}

.store-page :deep(.ant-input:focus) {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.store-page :deep(.ant-input-search) {
  border-radius: 6px;
}

.store-page :deep(.ant-input-search .ant-input) {
  border-radius: 6px 0 0 6px;
}

.store-page :deep(.ant-input-search .ant-btn) {
  border-radius: 0 6px 6px 0;
}

/* ==================== 选择器样式 ==================== */
.store-page :deep(.ant-select) {
  border-radius: 6px;
}

.store-page :deep(.ant-select-selector) {
  border-radius: 6px !important;
  border: 1px solid #d9d9d9 !important;
}

.store-page :deep(.ant-select-focused .ant-select-selector) {
  border-color: #40a9ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

/* ==================== 标签样式 ==================== */
.store-page :deep(.ant-tag) {
  border-radius: 4px;
  font-size: 12px;
}

/* ==================== 分页样式 ==================== */
.store-page :deep(.ant-pagination) {
  margin-top: 16px;
  text-align: right;
}

/* ==================== 统计样式 ==================== */
.store-page :deep(.ant-statistic-title) {
  font-size: 14px;
  color: #8c8c8c;
}

.store-page :deep(.ant-statistic-content) {
  font-size: 20px;
  font-weight: 600;
}

/* ==================== 表单样式 ==================== */
.store-page :deep(.ant-form-item) {
  margin-bottom: 16px;
}

.store-page :deep(.ant-form-item-label) {
  font-weight: 500;
}

/* ==================== Tab样式 ==================== */
.store-page :deep(.ant-tabs) {
  background: #ffffff;
}

.store-page :deep(.ant-tabs-tab) {
  font-weight: 500;
  color: #666;
}

.store-page :deep(.ant-tabs-tab-active) {
  color: #1890ff;
}

.store-page :deep(.ant-tabs-ink-bar) {
  background: #1890ff;
}

/* ==================== 步骤条样式 ==================== */
.store-page :deep(.ant-steps) {
  background: #fafafa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
}

.store-page :deep(.ant-steps-item-title) {
  font-size: 14px;
  font-weight: 500;
}

.store-page :deep(.ant-steps-item-description) {
  font-size: 12px;
  color: #666;
}

/* ==================== 模态框样式 ==================== */
.store-page :deep(.ant-modal-body) {
  max-height: 70vh;
  overflow-y: auto;
}

/* ==================== 抽屉样式 ==================== */
.store-page :deep(.ant-drawer-header) {
  border-bottom: 1px solid #f0f0f0;
}

.store-page :deep(.ant-drawer-body) {
  padding: 24px;
}

/* ==================== 搜索筛选区域样式 ==================== */
.store-search-filters {
  margin-bottom: 16px;
}

.store-search-filters :deep(.ant-card) {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 768px) {
  .store-page-header {
    padding: 0 2px;
  }
  
  .store-page-title {
    font-size: 20px;
  }
  
  .store-title-icon {
    font-size: 24px;
  }
  
  .store-page :deep(.ant-card) {
    margin-bottom: 12px;
  }
  
  .store-page :deep(.ant-table-pagination) {
    text-align: center;
  }
}

@media (max-width: 480px) {
  .store-page-title {
    font-size: 18px;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .store-page :deep(.ant-btn) {
    font-size: 12px;
    padding: 4px 8px;
  }
}
