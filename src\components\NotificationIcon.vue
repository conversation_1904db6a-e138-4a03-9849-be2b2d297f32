<template>
  <div class="notification-icon-wrapper">
    <a-badge :count="unreadCount" :overflow-count="99" :size="'default'">
      <a-button 
        type="text" 
        shape="circle" 
        size="middle"
        @click="handleClick"
        class="notification-button"
      >
        <BellOutlined class="notification-icon" />
      </a-button>
    </a-badge>
    
    <!-- 通知下拉面板 -->
    <a-dropdown 
      v-model:open="dropdownVisible" 
      :trigger="['click']"
      placement="bottomRight"
      overlay-class-name="notification-dropdown"
    >
      <div></div>
      <template #overlay>
        <div class="notification-dropdown-content">
          <!-- 头部 -->
          <div class="dropdown-header">
            <div class="header-title">
              <BellOutlined />
              通知中心
            </div>
            <div class="header-actions">
              <a-button 
                type="link" 
                size="small"
                @click="handleViewAll"
              >
                查看全部
              </a-button>
            </div>
          </div>
          
          <!-- 通知列表 -->
          <div class="dropdown-body">
            <a-spin :spinning="loading">
              <div v-if="notifications.length === 0" class="empty-notifications">
                <a-empty 
                  description="暂无通知" 
                  :image="Empty.PRESENTED_IMAGE_SIMPLE"
                />
              </div>
              
              <div v-else class="notification-list">
                <div
                  v-for="notification in notifications"
                  :key="notification.id"
                  class="notification-item"
                  :class="{ 'unread': !notification.是否已读 }"
                  @click="handleNotificationClick(notification)"
                >
                  <div class="item-icon">
                    <component 
                      :is="getNotificationIcon(notification.通知类型, notification.重要性)"
                      :style="{ color: getImportanceColor(notification.重要性) }"
                    />
                  </div>
                  
                  <div class="item-content">
                    <div class="item-title">{{ notification.标题 }}</div>
                    <div class="item-summary">{{ notification.摘要 }}</div>
                    <div class="item-time">{{ notification.时间显示 }}</div>
                  </div>
                  
                  <div v-if="!notification.是否已读" class="item-dot"></div>
                </div>
                
                <!-- 查看更多按钮 -->
                <div class="view-more">
                  <a-button 
                    type="link" 
                    block
                    @click="handleViewAll"
                  >
                    查看更多通知
                  </a-button>
                </div>
              </div>
            </a-spin>
          </div>
        </div>
      </template>
    </a-dropdown>
    
    <!-- 通知详情弹框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="通知详情"
      :footer="null"
      width="600px"
      :destroy-on-close="true"
    >
      <div v-if="selectedNotification" class="notification-detail">
        <div class="detail-header">
          <h2 class="detail-title">{{ selectedNotification.标题 }}</h2>
          <div class="detail-meta">
            <a-tag 
              :color="getImportanceColor(selectedNotification.重要性)"
            >
              {{ formatImportanceLevel(selectedNotification.重要性) }}
            </a-tag>
            <a-tag>
              {{ formatNotificationType(selectedNotification.通知类型) }}
            </a-tag>
            <span class="detail-time">
              {{ selectedNotification.创建时间 }}
            </span>
          </div>
        </div>
        
        <a-divider />
        
        <div class="detail-content">
          <div
            v-if="Array.isArray(selectedNotification.内容)"
            v-for="(item, index) in selectedNotification.内容"
            :key="index"
            class="content-item"
          >
            <div v-if="item.类型 === '文本'" class="text-content">
              {{ item.内容 }}
            </div>
            <div v-else-if="item.类型 === '链接'" class="link-content">
              <a :href="item.内容" target="_blank" rel="noopener">{{ item.内容 }}</a>
            </div>
          </div>
          
          <!-- 如果内容不是数组格式，直接显示 -->
          <div v-else class="simple-content">
            {{ selectedNotification.内容 }}
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="detail-actions">
          <a-space>
            <!-- 
              移除"标记已读"按钮，优化用户体验：
              - 点击通知查看详情时已自动标记为已读
              - 避免用户需要额外点击"标记已读"按钮的步骤
              - 符合直觉：查看即已读
            -->
            <a-button 
              v-if="selectedNotification.业务类型"
              type="primary"
              @click="handleNavigateToBusiness"
            >
              查看相关内容
            </a-button>
            <a-button @click="detailModalVisible = false">
              关闭
            </a-button>
          </a-space>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { Empty, message } from 'ant-design-vue'
import {
  BellOutlined,
  InfoCircleOutlined,
  ExclamationCircleOutlined,
  WarningOutlined
} from '@ant-design/icons-vue'
import notificationService from '../services/notificationService'

// 路由
const router = useRouter()

// 响应式数据
const unreadCount = ref(0)
const notifications = ref([])
const loading = ref(false)
const dropdownVisible = ref(false)
const detailModalVisible = ref(false)
const selectedNotification = ref(null)

// 轮询定时器
let pollingTimer = null

// 获取最新通知列表（用于下拉面板）
const fetchRecentNotifications = async () => {
  try {
    loading.value = true
    
    const response = await notificationService.getNotificationList({
      页码: 1,
      每页数量: 5, // 只显示最新5条
      排序字段: '创建时间',
      排序顺序: 'DESC'
    })
    
    if (response.status === 100 && response.data) {
      notifications.value = response.data.列表
    }
  } catch (error) {
    console.error('获取最新通知失败:', error)
  } finally {
    loading.value = false
  }
}

// 获取未读数量
const fetchUnreadCount = async () => {
  try {
    const response = await notificationService.getUnreadCount()
    if (response.status === 100 && response.data) {
      unreadCount.value = response.data.未读数量
    }
  } catch (error) {
    console.error('获取未读数量失败:', error)
  }
}

// 处理图标点击
const handleClick = () => {
  dropdownVisible.value = !dropdownVisible.value
  if (dropdownVisible.value) {
    fetchRecentNotifications()
  }
}

// 处理通知点击 - 显示详情弹框
const handleNotificationClick = async (notification) => {
  try {
    console.log('点击通知，准备显示详情:', notification)
    
    // 关闭下拉面板
    dropdownVisible.value = false
    
    // 获取通知详情
    await fetchNotificationDetail(notification)
    
  } catch (error) {
    console.error('处理通知点击失败:', error)
    // 显示友好的错误提示
    if (error.message) {
      message.error(`获取通知详情失败: ${error.message}`)
    } else {
      message.error('获取通知详情失败，请稍后重试')
    }
  }
}

// 获取通知详情
const fetchNotificationDetail = async (notification) => {
  try {
    console.log('开始获取通知详情:', notification.id)
    
    const response = await notificationService.getNotificationDetail(notification.id)
    
    if (response.status === 100 && response.data) {
      selectedNotification.value = response.data
      detailModalVisible.value = true
      console.log('通知详情获取成功:', response.data)
      
      // 如果通知未读，自动标记为已读（点击查看即视为已读，提升用户体验）
      if (!notification.是否已读) {
        await markAsReadSilently(notification.id)
      }
    } else {
      throw new Error(response.message || '获取通知详情失败')
    }
  } catch (error) {
    console.error('获取通知详情失败:', error)
    throw error
  }
}

// 静默标记已读（不显示提示）
const markAsReadSilently = async (notificationId) => {
  try {
    await notificationService.markAsRead(notificationId)
    // markAsRead方法内部会自动触发立即更新，无需手动调用
    
    // 更新本地数据
    const index = notifications.value.findIndex(n => n.id === notificationId)
    if (index !== -1) {
      notifications.value[index].是否已读 = true
    }
  } catch (error) {
    console.error('标记已读失败:', error)
  }
}

// 导航到业务相关页面
const handleNavigateToBusiness = () => {
  if (selectedNotification.value.业务类型) {
    detailModalVisible.value = false
    notificationService.navigateToBusinessPage(
      selectedNotification.value.业务类型,
      selectedNotification.value.业务关联id,
      router
    )
  }
}

// 查看全部通知
const handleViewAll = () => {
  dropdownVisible.value = false
  router.push('/notifications')
}

// 格式化通知类型
const formatNotificationType = (type) => {
  return notificationService.formatNotificationType(type)
}

// 格式化重要性级别
const formatImportanceLevel = (level) => {
  return notificationService.formatImportanceLevel(level).text
}

// 格式化重要性颜色
const getImportanceColor = (level) => {
  return notificationService.formatImportanceLevel(level).color
}

// 获取通知图标
const getNotificationIcon = (type, importance) => {
  const iconName = notificationService.getNotificationIcon(type, importance)
  const iconMap = {
    'BellOutlined': BellOutlined,
    'InfoCircleOutlined': InfoCircleOutlined,
    'ExclamationCircleOutlined': ExclamationCircleOutlined,
    'WarningOutlined': WarningOutlined
  }
  return iconMap[iconName] || BellOutlined
}

// 组件挂载
onMounted(() => {
  fetchUnreadCount()

  // 使用新的智能轮询系统
  const cleanup = notificationService.startSmartPolling((count) => {
    unreadCount.value = count
  })
  
  // 保存清理函数
  pollingTimer = cleanup
})

// 组件卸载
onUnmounted(() => {
  if (pollingTimer) {
    // 调用智能轮询的清理函数
    pollingTimer()
    pollingTimer = null
  }
})
</script>

<style scoped>
.notification-icon-wrapper {
  position: relative;
}

.notification-button {
  border: 1px solid transparent !important;
  box-shadow: none !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 32px !important;
  width: 32px !important;
  background: rgba(255, 255, 255, 0.1) !important;
  transition: all 0.2s ease !important;
  padding: 4px !important;
  margin: 0 !important;
  backdrop-filter: blur(2px) !important;
}

.notification-button:hover {
  background: rgba(24, 144, 255, 0.1) !important;
}

.notification-button:focus {
  background: rgba(24, 144, 255, 0.15) !important;
  outline: none !important;
}

.notification-button:active {
  background: rgba(24, 144, 255, 0.2) !important;
}

.notification-icon {
  font-size: 16px !important;
  color: #1890ff !important;
  transition: color 0.2s ease !important;
  line-height: 1 !important;
  font-weight: 500 !important;
}

.notification-button:hover .notification-icon {
  color: #1890ff !important;
}

.notification-button:focus .notification-icon {
  color: #1890ff !important;
}

.notification-button:active .notification-icon {
  color: #0d7cd9 !important;
}

/* 徽章样式优化 */
:deep(.ant-badge-count) {
  background: #ff4d4f !important;
  border: none !important;
  font-size: 11px !important;
  min-width: 16px !important;
  height: 16px !important;
  line-height: 16px !important;
  border-radius: 8px !important;
  font-weight: 400 !important;
  box-shadow: 0 0 0 1px #fff !important;
  transform: scale(1) !important;
}

:deep(.ant-badge) {
  vertical-align: middle !important;
}

:deep(.ant-badge-count-sm) {
  font-size: 11px !important;
  min-width: 16px !important;
  height: 16px !important;
  line-height: 16px !important;
}

/* 下拉面板样式 */
:deep(.notification-dropdown .ant-dropdown-menu) {
  padding: 0;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-height: none;
}

.notification-dropdown-content {
  width: 380px;
  max-height: 500px;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

/* 头部样式 */
.dropdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.header-title {
  font-weight: 600;
  color: #262626;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-actions {
  color: #1890ff;
}

/* 通知列表样式 */
.dropdown-body {
  max-height: 400px;
  overflow-y: auto;
}

.empty-notifications {
  padding: 40px 20px;
  text-align: center;
}

.notification-list {
  padding: 0;
}

.notification-item {
  display: flex;
  padding: 16px 20px;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.notification-item:hover {
  background-color: #f8f9fa;
}

.notification-item.unread {
  background-color: #e6f7ff;
}

.notification-item.unread:hover {
  background-color: #d4edda;
}

.item-icon {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 16px;
}

.item-content {
  flex: 1;
  min-width: 0;
}

.item-title {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-summary {
  font-size: 13px;
  color: #8c8c8c;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 4px;
}

.item-time {
  font-size: 12px;
  color: #bfbfbf;
}

.item-dot {
  width: 8px;
  height: 8px;
  background-color: #ff4d4f;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
}

.view-more {
  padding: 12px 20px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

/* 通知详情弹框样式 */
.notification-detail {
  padding: 4px 0;
}

.detail-header {
  margin-bottom: 0;
}

.detail-title {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.detail-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.detail-time {
  color: #8c8c8c;
  font-size: 14px;
}

.detail-content {
  margin: 20px 0;
  line-height: 1.6;
}

.content-item {
  margin-bottom: 12px;
}

.text-content {
  color: #262626;
  font-size: 14px;
  white-space: pre-line;
}

.link-content a {
  color: #1890ff;
  text-decoration: none;
}

.link-content a:hover {
  text-decoration: underline;
}

.simple-content {
  color: #262626;
  font-size: 14px;
  white-space: pre-line;
}

.detail-actions {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .notification-dropdown-content {
    width: 320px;
  }
  
  .notification-item {
    padding: 12px 16px;
  }
  
  .item-icon {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }
}
</style>
