<template>
  <div class="order-management-module">
    <!-- 操作栏 -->
    <div class="action-bar">
      <div class="action-left">
        <a-button type="primary" @click="showImportModal = true">
          <template #icon>
            <cloud-upload-outlined />
          </template>
          导入商户订单
        </a-button>
        <a-button @click="showImportRecordsModal = true">
          <template #icon>
            <file-text-outlined />
          </template>
          导入记录
        </a-button>
      </div>
      
      <div class="action-right">
        <a-space>
          <!-- 筛选条件 -->
          <span class="filter-label">筛选条件：</span>
          <a-input
            v-model:value="filterForm.商品名称"
            placeholder="商品名称"
            style="width: 150px"
            allowClear
            @pressEnter="handleSearch"
            @change="handleProductNameChange"
          >
            <template #prefix>
              <search-outlined />
            </template>
          </a-input>

          <a-select
            v-model:value="filterForm.订单状态"
            placeholder="订单状态"
            style="width: 120px"
            allowClear
            @change="handleSearch"
            :loading="statusOptionsLoading"
          >
            <a-select-option
              v-for="status in statusOptions"
              :key="status.value"
              :value="status.value"
            >
              {{ status.label }}
            </a-select-option>
          </a-select>

          <a-range-picker
            v-model:value="dateRange"
            :placeholder="['付款开始时间', '付款结束时间']"
            @change="handleDateChange"
            style="width: 240px"
          >
            <template #suffixIcon>
              <calendar-outlined />
            </template>
          </a-range-picker>

          <a-tooltip title="按付款时间筛选订单">
            <info-circle-outlined style="color: #1890ff; cursor: help;" />
          </a-tooltip>

          <a-button @click="handleReset">重置</a-button>
        </a-space>
      </div>
    </div>

    <!-- 订单列表表格 -->
    <div class="table-container">
      <a-table
        :columns="columns"
        :data-source="orderList"
        :loading="loading"
        :pagination="pagination"
        :scroll="{ x: 1500 }"
        row-key="订单id"
        @change="handleTableChange"
      >
        <!-- 订单ID列 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === '订单id'">
            <span class="order-id">{{ record.订单id }}</span>
          </template>
          
          <!-- 支付金额列 -->
          <template v-else-if="column.dataIndex === '支付金额'">
            <span class="amount">¥{{ record.支付金额?.toFixed(2) || '0.00' }}</span>
          </template>
          
          <!-- 佣金率列 -->
          <template v-else-if="column.dataIndex === '佣金率'">
            <span class="rate">{{ (record.佣金率 * 100)?.toFixed(2) || '0.00' }}%</span>
          </template>
          
          <!-- 预估佣金支出列 -->
          <template v-else-if="column.dataIndex === '预估佣金支出'">
            <span class="amount">¥{{ record.预估佣金支出?.toFixed(2) || '0.00' }}</span>
          </template>
          
          <!-- 实际佣金支出列 -->
          <template v-else-if="column.dataIndex === '实际佣金支出'">
            <span class="amount">¥{{ record.实际佣金支出?.toFixed(2) || '0.00' }}</span>
          </template>
          
          <!-- 商品数量列 -->
          <template v-else-if="column.dataIndex === '商品数量'">
            <span class="quantity">{{ formatQuantity(record.商品数量) }}</span>
          </template>

          <!-- 订单状态列 -->
          <template v-else-if="column.dataIndex === '订单状态'">
            <a-tag :color="getStatusColor(record.订单状态)">
              {{ record.订单状态 || '未知' }}
            </a-tag>
          </template>
          
          <!-- 付款时间列 -->
          <template v-else-if="column.dataIndex === '付款时间'">
            <span>{{ formatDateTime(record.付款时间) }}</span>
          </template>
          
          <!-- 收货时间列 -->
          <template v-else-if="column.dataIndex === '收货时间'">
            <span>{{ formatDateTime(record.收货时间) }}</span>
          </template>

          <!-- 操作列 -->
          <template v-else-if="column.key === 'action'">
            <a-button
              type="link"
              size="small"
              @click="handleViewDetail(record)"
            >
              查看详情
            </a-button>
          </template>
        </template>
      </a-table>
    </div>

    <!-- Excel导入弹窗 -->
    <a-modal
      v-model:open="showImportModal"
      title="导入商户订单"
      width="600px"
      :footer="null"
      @cancel="handleImportCancel"
    >
      <div class="import-content">
        <!-- 文件选择界面 -->
        <div v-if="!showUploadProgress" class="file-selection">
          <div class="import-tips">
            <a-alert
              message="导入说明"
              type="info"
              show-icon
              style="margin-bottom: 16px"
            >
              <template #description>
                <ul style="margin: 8px 0; padding-left: 20px;">
                  <li>支持Excel格式文件(.xlsx, .xls)</li>
                  <li>文件大小不能超过35MB</li>
                  <li>重复订单ID将被跳过</li>
                  <li>百分比字段支持"10.00%"格式</li>
                  <li>日期字段支持"-"表示空值</li>
                </ul>
              </template>
            </a-alert>
          </div>

          <div class="upload-area">
            <a-upload-dragger
              v-model:file-list="fileList"
              :before-upload="beforeUpload"
              @remove="handleRemoveFile"
              accept=".xlsx,.xls"
              :multiple="false"
            >
              <p class="ant-upload-drag-icon">
                <inbox-outlined />
              </p>
              <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
              <p class="ant-upload-hint">
                支持Excel格式文件，单个文件大小不超过35MB
              </p>
            </a-upload-dragger>
          </div>

          <div class="import-actions" style="margin-top: 16px; text-align: right;">
            <a-space>
              <a-button @click="handleImportCancel">取消</a-button>
              <a-button
                type="primary"
                :loading="importLoading"
                :disabled="fileList.length === 0"
                @click="handleImport"
              >
                开始导入
              </a-button>
            </a-space>
          </div>
        </div>

        <!-- 上传进度界面 -->
        <div v-if="showUploadProgress" class="upload-progress">
          <div class="progress-header" style="text-align: center; margin-bottom: 24px;">
            <h3 style="margin: 0; color: #1890ff;">{{ uploadStatus.title }}</h3>
            <p style="margin: 8px 0 0 0; color: #666;">{{ uploadStatus.description }}</p>
          </div>

          <div class="progress-content">
            <a-progress
              :percent="Math.round(uploadProgress)"
              :status="uploadStatus.status"
              :stroke-color="uploadStatus.color"
              :stroke-width="8"
              style="margin-bottom: 16px;"
            />

            <div class="progress-info" style="display: flex; justify-content: space-between; align-items: center;">
              <span style="color: #666;">{{ uploadStatus.text }}</span>
              <span style="font-weight: 500; color: #1890ff;">{{ Math.round(uploadProgress) }}%</span>
            </div>

            <!-- 文件信息 -->
            <div v-if="currentFile" class="file-info" style="margin-top: 16px; padding: 12px; background: #f5f5f5; border-radius: 6px;">
              <div style="display: flex; align-items: center;">
                <file-excel-outlined style="color: #52c41a; margin-right: 8px; font-size: 16px;" />
                <span style="font-weight: 500;">{{ currentFile.name }}</span>
                <span style="margin-left: 8px; color: #666;">({{ formatFileSize(currentFile.size) }})</span>
              </div>
            </div>
          </div>

          <!-- 进度界面的操作按钮 -->
          <div class="progress-actions" style="margin-top: 24px; text-align: right;">
            <a-button
              v-if="uploadStatus.status !== 'success'"
              @click="handleCancelUpload"
              :disabled="uploadStatus.status === 'success'"
            >
              取消导入
            </a-button>
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 导入结果弹窗 -->
    <a-modal
      v-model:open="showResultModal"
      title="导入结果"
      width="500px"
      :footer="null"
    >
      <div class="import-result" v-if="importResult">
        <div class="result-summary">
          <a-descriptions :column="2" bordered size="small">
            <a-descriptions-item label="总行数">{{ importResult.总行数 }}</a-descriptions-item>
            <a-descriptions-item label="成功数量">
              <span style="color: #52c41a;">{{ importResult.成功数量 }}</span>
            </a-descriptions-item>
            <a-descriptions-item label="失败数量">
              <span style="color: #ff4d4f;">{{ importResult.失败数量 }}</span>
            </a-descriptions-item>
            <a-descriptions-item label="跳过数量">
              <span style="color: #faad14;">{{ importResult.跳过数量 }}</span>
            </a-descriptions-item>
            <a-descriptions-item label="同步成功">
              <span style="color: #52c41a;">{{ importResult.同步成功数量 || 0 }}</span>
            </a-descriptions-item>
            <a-descriptions-item label="同步失败">
              <span style="color: #ff7875;">{{ importResult.同步失败数量 || 0 }}</span>
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 同步说明 -->
        <div class="sync-info" style="margin-top: 12px;">
          <a-alert
            message="数据同步说明"
            type="info"
            show-icon
            style="font-size: 12px;"
          >
            <template #description>
              <div style="font-size: 12px; line-height: 1.4;">
                • 同步成功：订单中的抖音火山号已匹配到达人信息并建立关联<br>
                • 同步失败：订单中的抖音火山号未找到对应达人信息，跳过关联
              </div>
            </template>
          </a-alert>
        </div>

        <div v-if="importResult.错误详情?.length > 0" class="error-details" style="margin-top: 16px;">
          <h4>错误详情：</h4>
          <div class="error-list">
            <div v-for="(error, index) in importResult.错误详情" :key="index" class="error-item">
              <a-tag color="red">{{ error }}</a-tag>
            </div>
          </div>
        </div>

        <div style="margin-top: 16px; text-align: right;">
          <a-button type="primary" @click="handleResultClose">确定</a-button>
        </div>
      </div>
    </a-modal>

    <!-- 导入记录管理弹窗 -->
    <a-modal
      v-model:open="showImportRecordsModal"
      title="导入记录管理"
      width="1000px"
      :footer="null"
      @cancel="handleImportRecordsCancel"
    >
      <div class="import-records-content">
        <!-- 筛选条件 -->
        <div class="records-filter">
          <a-space>
            <a-select
              v-model:value="recordsFilter.状态筛选"
              placeholder="状态筛选"
              style="width: 120px"
              allowClear
              @change="fetchImportRecords"
            >
              <a-select-option value="进行中">进行中</a-select-option>
              <a-select-option value="已完成">已完成</a-select-option>
              <a-select-option value="失败">失败</a-select-option>
            </a-select>
            <a-button @click="fetchImportRecords" :loading="recordsLoading">
              <template #icon>
                <reload-outlined />
              </template>
              刷新
            </a-button>
          </a-space>
        </div>

        <!-- 导入记录列表 -->
        <div class="records-table" style="margin-top: 16px;">
          <a-table
            :columns="recordsColumns"
            :data-source="importRecords"
            :loading="recordsLoading"
            :pagination="recordsPagination"
            @change="handleRecordsTableChange"
            row-key="任务ID"
            size="middle"
          >
            <!-- 文件名列 -->
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'fileName'">
                <div class="file-name">
                  <file-excel-outlined style="color: #52c41a; margin-right: 8px;" />
                  {{ record.文件名 }}
                </div>
              </template>

              <!-- 状态列 -->
              <template v-else-if="column.key === 'status'">
                <a-tag
                  :color="getImportStatusColor(record.任务状态)"
                  style="margin: 0;"
                >
                  {{ record.任务状态 }}
                </a-tag>
              </template>

              <!-- 进度列 -->
              <template v-else-if="column.key === 'progress'">
                <div class="progress-cell">
                  <a-progress
                    :percent="Math.round(record.进度百分比)"
                    :status="getProgressStatus(record.任务状态)"
                    size="small"
                    :show-info="false"
                  />
                  <span class="progress-text">{{ Math.round(record.进度百分比) }}%</span>
                </div>
              </template>

              <!-- 统计列 -->
              <template v-else-if="column.key === 'stats'">
                <div class="stats-cell">
                  <div class="stat-item">
                    <span class="stat-label">总数:</span>
                    <span class="stat-value">{{ record.总行数 || 0 }}</span>
                  </div>
                  <div class="stat-item success">
                    <span class="stat-label">成功:</span>
                    <span class="stat-value">{{ record.成功数量 || 0 }}</span>
                  </div>
                  <div class="stat-item error" v-if="record.失败数量 > 0">
                    <span class="stat-label">失败:</span>
                    <span class="stat-value">{{ record.失败数量 }}</span>
                  </div>
                  <div class="stat-item skip" v-if="record.跳过数量 > 0">
                    <span class="stat-label">跳过:</span>
                    <span class="stat-value">{{ record.跳过数量 }}</span>
                  </div>
                </div>
              </template>

              <!-- 操作列 -->
              <template v-else-if="column.key === 'actions'">
                <a-space>
                  <a-button
                    type="link"
                    size="small"
                    @click="showRecordDetail(record)"
                  >
                    查看详情
                  </a-button>
                  <a-button
                    v-if="record.任务状态 === '进行中'"
                    type="link"
                    size="small"
                    @click="refreshRecordProgress(record.任务ID)"
                    :loading="refreshingRecords.has(record.任务ID)"
                  >
                    刷新进度
                  </a-button>
                  <a-button
                    v-if="record.任务状态 === '超时' && record.可续传"
                    type="link"
                    size="small"
                    @click="handleContinueImport(record)"
                    :loading="continuingRecords.has(record.id)"
                    style="color: #1890ff;"
                  >
                    继续导入
                  </a-button>
                </a-space>
              </template>
            </template>
          </a-table>
        </div>
      </div>
    </a-modal>

    <!-- 导入记录详情弹窗 -->
    <a-modal
      v-model:open="showRecordDetailModal"
      title="导入记录详情"
      width="600px"
      :footer="null"
      @cancel="handleRecordDetailCancel"
    >
      <div class="record-detail-content" v-if="selectedRecord">
        <a-descriptions :column="2" bordered size="small">
          <a-descriptions-item label="文件名" :span="2">
            <file-excel-outlined style="color: #52c41a; margin-right: 8px;" />
            {{ selectedRecord.文件名 }}
          </a-descriptions-item>
          <a-descriptions-item label="任务状态">
            <a-tag :color="getImportStatusColor(selectedRecord.任务状态)">
              {{ selectedRecord.任务状态 }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="进度">
            {{ Math.round(selectedRecord.进度百分比) }}%
          </a-descriptions-item>
          <a-descriptions-item label="总行数">
            {{ selectedRecord.总行数 || 0 }}
          </a-descriptions-item>
          <a-descriptions-item label="成功数量">
            <span style="color: #52c41a;">{{ selectedRecord.成功数量 || 0 }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="失败数量">
            <span style="color: #ff4d4f;">{{ selectedRecord.失败数量 || 0 }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="跳过数量">
            <span style="color: #faad14;">{{ selectedRecord.跳过数量 || 0 }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="开始时间">
            {{ selectedRecord.开始时间 || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="完成时间">
            {{ selectedRecord.完成时间 || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="错误信息" :span="2" v-if="selectedRecord.错误信息">
            <div class="error-message">
              {{ selectedRecord.错误信息 }}
            </div>
          </a-descriptions-item>
        </a-descriptions>

        <div style="margin-top: 16px; text-align: right;">
          <a-button type="primary" @click="handleRecordDetailCancel">关闭</a-button>
        </div>
      </div>
    </a-modal>

    <!-- 订单详情模态框 -->
    <a-modal
      v-model:open="showOrderDetailModal"
      title="订单详情"
      width="800px"
      :footer="null"
      @cancel="handleOrderDetailCancel"
    >
      <div class="order-detail-content" v-if="orderDetail">
        <a-descriptions :column="2" bordered>
          <!-- 基本信息 -->
          <a-descriptions-item label="订单ID">
            <span class="order-id">{{ orderDetail.订单id }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="订单状态">
            <a-tag :color="getStatusColor(orderDetail.订单状态)">
              {{ orderDetail.订单状态 || '未知' }}
            </a-tag>
          </a-descriptions-item>

          <!-- 商品信息 -->
          <a-descriptions-item label="商品名称" :span="2">
            {{ orderDetail.商品名称 || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="商品规格">
            {{ orderDetail.商品规格 || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="商品数量">
            {{ orderDetail.商品数量 || 0 }}
          </a-descriptions-item>
          <a-descriptions-item label="商品单价">
            <span class="amount">¥{{ orderDetail.商品单价?.toFixed(2) || '0.00' }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="支付金额">
            <span class="amount">¥{{ orderDetail.支付金额?.toFixed(2) || '0.00' }}</span>
          </a-descriptions-item>

          <!-- 佣金信息 -->
          <a-descriptions-item label="佣金率">
            <span class="rate">{{ (orderDetail.佣金率 * 100)?.toFixed(2) || '0.00' }}%</span>
          </a-descriptions-item>
          <a-descriptions-item label="预估佣金">
            <span class="amount">¥{{ orderDetail.预估佣金支出?.toFixed(2) || '0.00' }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="实际佣金">
            <span class="amount">¥{{ orderDetail.实际佣金支出?.toFixed(2) || '0.00' }}</span>
          </a-descriptions-item>

          <!-- 时间信息 -->
          <a-descriptions-item label="下单时间">
            {{ orderDetail.下单时间 || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="付款时间">
            {{ orderDetail.付款时间 || '-' }}
          </a-descriptions-item>

          <!-- 店铺信息 -->
          <a-descriptions-item label="店铺id">
            {{ orderDetail.店铺id || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="店铺名称">
            {{ orderDetail.店铺名称 || '-' }}
          </a-descriptions-item>

          <!-- 达人信息 -->
          <a-descriptions-item label="作者账号">
            {{ orderDetail.作者账号 || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="抖音火山号">
            {{ orderDetail.抖音火山号 || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="关联抖音号" v-if="orderDetail.关联抖音号">
            {{ orderDetail.关联抖音号 }}
          </a-descriptions-item>
          <a-descriptions-item label="关联达人表ID" v-if="orderDetail.关联达人表id">
            {{ orderDetail.关联达人表id }}
          </a-descriptions-item>

          <!-- 其他信息 -->
          <a-descriptions-item label="商品ID">
            {{ orderDetail.商品id || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="推广位">
            {{ orderDetail.推广位 || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="推广位名称" :span="2" v-if="orderDetail.推广位名称">
            {{ orderDetail.推广位名称 }}
          </a-descriptions-item>
        </a-descriptions>

        <div style="margin-top: 16px; text-align: right;">
          <a-button type="primary" @click="handleOrderDetailCancel">关闭</a-button>
        </div>
      </div>

      <div v-else style="text-align: center; padding: 40px;">
        <a-spin size="large" />
        <div style="margin-top: 16px;">加载订单详情中...</div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, onUnmounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  CloudUploadOutlined,
  InboxOutlined,
  FileTextOutlined,
  SearchOutlined,
  CalendarOutlined,
  InfoCircleOutlined,
  ReloadOutlined,
  FileExcelOutlined
} from '@ant-design/icons-vue'
import orderService from '@/services/orderService'

defineOptions({
  name: 'OrderManagementModule'
})

// 响应式数据
const loading = ref(false)
const orderList = ref([])
const showImportModal = ref(false)
const showResultModal = ref(false)
const importLoading = ref(false)
const fileList = ref([])
const importResult = ref(null)
const dateRange = ref([])
const statusOptions = ref([])
const statusOptionsLoading = ref(false)

// 上传进度相关数据
const showUploadProgress = ref(false)
const uploadProgress = ref(0)
const currentFile = ref(null)
const uploadStatus = reactive({
  status: 'active', // active, success, exception
  title: '准备上传',
  description: '正在准备文件上传...',
  text: '初始化中...',
  color: '#1890ff'
})
let progressTimer = null

// 订单详情相关数据
const showOrderDetailModal = ref(false)
const orderDetail = ref(null)
const orderDetailLoading = ref(false)

// 导入记录相关数据
const showImportRecordsModal = ref(false)
const showRecordDetailModal = ref(false)
const recordsLoading = ref(false)
const importRecords = ref([])
const selectedRecord = ref(null)
const refreshingRecords = ref(new Set())
const continuingRecords = ref(new Set())
const progressPollingTimers = ref(new Map())

// 筛选表单
const filterForm = reactive({
  订单状态: undefined,
  商品名称: undefined
})

// 导入记录筛选表单
const recordsFilter = reactive({
  状态筛选: undefined
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 导入记录分页配置
const recordsPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 导入记录表格列配置
const recordsColumns = [
  {
    title: '文件名',
    key: 'fileName',
    width: 200,
    ellipsis: true
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    align: 'center'
  },
  {
    title: '进度',
    key: 'progress',
    width: 150,
    align: 'center'
  },
  {
    title: '统计信息',
    key: 'stats',
    width: 200
  },
  {
    title: '开始时间',
    dataIndex: '开始时间',
    width: 150,
    ellipsis: true
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    align: 'center'
  }
]

// 表格列配置
const columns = [
  {
    title: '订单ID',
    dataIndex: '订单id',
    key: '订单id',
    width: 150,
    fixed: 'left'
  },
  {
    title: '商品名称',
    dataIndex: '商品名称',
    key: '商品名称',
    width: 200,
    ellipsis: true
  },
  {
    title: '数量',
    dataIndex: '商品数量',
    key: '商品数量',
    width: 80,
    align: 'center',
    sorter: (a, b) => (a.商品数量 || 0) - (b.商品数量 || 0)
  },
  {
    title: '作者账号',
    dataIndex: '作者账号',
    key: '作者账号',
    width: 120
  },
  {
    title: '支付金额',
    dataIndex: '支付金额',
    key: '支付金额',
    width: 100,
    align: 'right'
  },
  {
    title: '佣金率',
    dataIndex: '佣金率',
    key: '佣金率',
    width: 80,
    align: 'right'
  },
  {
    title: '预估佣金',
    dataIndex: '预估佣金支出',
    key: '预估佣金支出',
    width: 100,
    align: 'right'
  },
  {
    title: '实际佣金',
    dataIndex: '实际佣金支出',
    key: '实际佣金支出',
    width: 100,
    align: 'right'
  },
  {
    title: '订单状态',
    dataIndex: '订单状态',
    key: '订单状态',
    width: 100
  },
  {
    title: '店铺名称',
    dataIndex: '店铺名称',
    key: '店铺名称',
    width: 150,
    ellipsis: true
  },
  {
    title: '付款时间',
    dataIndex: '付款时间',
    key: '付款时间',
    width: 150
  },
  {
    title: '收货时间',
    dataIndex: '收货时间',
    key: '收货时间',
    width: 150
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    align: 'center',
    fixed: 'right'
  }
]

// 方法定义
/**
 * 获取订单列表
 */
const fetchOrderList = async () => {
  try {
    loading.value = true

    const params = {
      页码: pagination.current,
      每页数量: pagination.pageSize,
      订单状态: filterForm.订单状态,
      商品名称: filterForm.商品名称,
      开始时间: dateRange.value?.[0]?.format('YYYY-MM-DD HH:mm:ss'),
      结束时间: dateRange.value?.[1]?.format('YYYY-MM-DD HH:mm:ss')
    }

    const response = await orderService.getOrderList(params)

    if (response.status === 100) {
      orderList.value = response.data.列表 || []
      pagination.total = response.data.总数 || 0
    } else {
      message.error(response.message || '获取订单列表失败')
    }
  } catch (error) {
    console.error('获取订单列表失败:', error)
    message.error('获取订单列表失败')
  } finally {
    loading.value = false
  }
}

/**
 * 处理表格变化
 */
const handleTableChange = (pag, filters, sorter) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  fetchOrderList()
}

/**
 * 处理搜索
 */
const handleSearch = () => {
  pagination.current = 1
  fetchOrderList()
}

/**
 * 处理重置
 */
const handleReset = () => {
  filterForm.订单状态 = undefined
  filterForm.商品名称 = undefined
  dateRange.value = []
  pagination.current = 1
  fetchOrderList()
}

/**
 * 处理商品名称变化（防抖搜索）
 */
let searchTimeout = null
const handleProductNameChange = () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  searchTimeout = setTimeout(() => {
    handleSearch()
  }, 500) // 500ms防抖
}

/**
 * 处理日期范围变化
 */
const handleDateChange = (dates) => {
  dateRange.value = dates
  handleSearch()
}

/**
 * 获取状态颜色（基于数据库实际状态值）
 */
const getStatusColor = (status) => {
  const colorMap = {
    '订单付款': 'blue',
    '订单收货': 'green',
    '订单退货退款': 'red'
  }
  return colorMap[status] || 'default'
}

/**
 * 格式化日期时间
 */
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return dateTime
}

/**
 * 格式化商品数量
 */
const formatQuantity = (quantity) => {
  if (!quantity || quantity === 0) return '0'

  // 如果数量很大，使用千分位分隔符
  if (quantity >= 1000) {
    return quantity.toLocaleString()
  }

  return quantity.toString()
}

/**
 * 获取订单状态选项
 */
const fetchStatusOptions = async () => {
  try {
    statusOptionsLoading.value = true

    const response = await orderService.getStatusOptions()

    if (response.status === 100) {
      statusOptions.value = response.data || []
    } else {
      console.error('获取订单状态选项失败:', response.message)
    }
  } catch (error) {
    console.error('获取订单状态选项失败:', error)
  } finally {
    statusOptionsLoading.value = false
  }
}

/**
 * 文件上传前检查
 */
const beforeUpload = (file) => {
  const isValidType = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                      file.type === 'application/vnd.ms-excel'
  if (!isValidType) {
    message.error('只能上传Excel格式文件!')
    return false
  }

  const isValidSize = file.size / 1024 / 1024 < 35
  if (!isValidSize) {
    message.error('文件大小不能超过35MB!')
    return false
  }

  return false // 阻止自动上传
}

/**
 * 移除文件
 */
const handleRemoveFile = (file) => {
  // 从文件列表中移除指定文件
  const index = fileList.value.indexOf(file)
  if (index > -1) {
    fileList.value.splice(index, 1)
  }
  return true // 返回true表示允许移除
}

/**
 * 格式化文件大小
 */
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 启动进度模拟
 */
const startProgressSimulation = (file) => {
  // 重置进度状态
  uploadProgress.value = 0
  currentFile.value = file
  showUploadProgress.value = true

  // 设置初始状态
  uploadStatus.status = 'active'
  uploadStatus.title = '正在上传文件'
  uploadStatus.description = '文件正在上传到服务器，请稍候...'
  uploadStatus.text = '正在上传文件...'
  uploadStatus.color = '#1890ff'

  // 模拟上传进度
  const fileSize = file.size
  const fileSizeMB = fileSize / (1024 * 1024)

  // 根据文件大小调整进度速度
  const getProgressSpeed = (currentProgress) => {
    const baseSpeed = Math.max(0.3, Math.min(2, 10 / fileSizeMB)) // 文件越大速度越慢

    if (currentProgress < 20) {
      // 前20%快速增长（模拟文件上传）
      return baseSpeed * (1.5 + Math.random() * 0.8)
    } else if (currentProgress < 50) {
      // 20%-50%中等速度（模拟文件验证）
      return baseSpeed * (0.8 + Math.random() * 0.6)
    } else if (currentProgress < 85) {
      // 50%-85%较慢（模拟数据处理）
      return baseSpeed * (0.4 + Math.random() * 0.4)
    } else {
      // 85%以上很慢（等待后端响应）
      return baseSpeed * (0.1 + Math.random() * 0.1)
    }
  }

  const simulateProgress = () => {
    const currentProgress = uploadProgress.value
    const speed = getProgressSpeed(currentProgress)

    if (currentProgress < 20) {
      uploadProgress.value += speed
      uploadStatus.text = '正在上传文件...'
    } else if (currentProgress < 50) {
      uploadProgress.value += speed
      uploadStatus.text = '正在验证文件格式...'
      uploadStatus.description = '服务器正在验证文件格式和内容...'
    } else if (currentProgress < 85) {
      uploadProgress.value += speed
      uploadStatus.text = '正在处理数据...'
      uploadStatus.description = '服务器正在解析和处理Excel数据...'
      uploadStatus.title = '正在处理数据'
    } else {
      uploadProgress.value += speed
      uploadStatus.text = '等待服务器响应...'
      uploadStatus.description = '数据处理即将完成，请稍候...'
    }

    // 确保不超过95%，等待实际响应
    uploadProgress.value = Math.min(95, uploadProgress.value)
  }

  // 启动定时器，根据文件大小调整更新频率
  const updateInterval = Math.max(150, Math.min(500, fileSizeMB * 50)) // 文件越大更新越慢
  progressTimer = setInterval(simulateProgress, updateInterval)
}

/**
 * 完成进度显示
 */
const completeProgress = (success = true) => {
  if (progressTimer) {
    clearInterval(progressTimer)
    progressTimer = null
  }

  if (success) {
    uploadProgress.value = 100
    uploadStatus.status = 'success'
    uploadStatus.title = '上传完成'
    uploadStatus.description = '文件已成功上传并处理完成！'
    uploadStatus.text = '上传完成'
    uploadStatus.color = '#52c41a'
  } else {
    uploadStatus.status = 'exception'
    uploadStatus.title = '上传失败'
    uploadStatus.description = '文件上传或处理过程中出现错误'
    uploadStatus.text = '上传失败'
    uploadStatus.color = '#ff4d4f'
  }

  // 成功时延迟1秒关闭弹窗
  if (success) {
    setTimeout(() => {
      resetUploadProgress()
    }, 1000)
  }
}

/**
 * 重置上传进度
 */
const resetUploadProgress = () => {
  if (progressTimer) {
    clearInterval(progressTimer)
    progressTimer = null
  }

  showUploadProgress.value = false
  uploadProgress.value = 0
  currentFile.value = null
  uploadStatus.status = 'active'
  uploadStatus.title = '准备上传'
  uploadStatus.description = '正在准备文件上传...'
  uploadStatus.text = '初始化中...'
  uploadStatus.color = '#1890ff'
}

/**
 * 取消上传
 */
const handleCancelUpload = () => {
  resetUploadProgress()
  showImportModal.value = false
  fileList.value = []
  importLoading.value = false
}

/**
 * 处理导入
 */
const handleImport = async () => {
  if (fileList.value.length === 0) {
    message.warning('请选择要导入的文件')
    return
  }

  // 防止重复点击
  if (importLoading.value) {
    return
  }

  try {
    importLoading.value = true

    const file = fileList.value[0].originFileObj || fileList.value[0]

    // 启动进度模拟
    startProgressSimulation(file)

    // 异步执行导入请求，不阻塞UI
    orderService.importOrders(file).then(response => {
      if (response.status === 100) {
        // 完成进度显示
        completeProgress(true)

        // 根据是否为重复任务显示不同的提示信息
        if (response.data && response.data.是否重复任务) {
          // 重复文件提示
          message.warning(response.message || '该文件已存在，无需重复上传')
        } else {
          // 正常导入成功提示
          message.success('文件导入完成！')
          // 提示用户可以在导入记录中查看详情
          message.info('您可以在"导入记录"中查看导入详情', 3)
        }

        // 刷新列表数据
        fetchOrderList()

        // 清理文件列表
        fileList.value = []
      } else {
        // 完成进度显示（失败）
        completeProgress(false)
        message.error(response.message || '导入失败')
      }
    }).catch(error => {
      console.error('导入失败:', error)

      // 完成进度显示（失败）
      completeProgress(false)

      if (error.code === 'ECONNABORTED') {
        message.warning('导入请求超时，但文件可能仍在后台处理中，请稍后查看导入记录')
      } else {
        message.error('导入失败，请稍后重试')
      }
    }).finally(() => {
      importLoading.value = false
    })

  } catch (error) {
    console.error('导入启动失败:', error)
    completeProgress(false)
    message.error('导入启动失败')
    importLoading.value = false
  }
}

/**
 * 处理导入取消
 */
const handleImportCancel = () => {
  // 重置上传进度
  resetUploadProgress()

  showImportModal.value = false
  fileList.value = []
  importResult.value = null
  // 重置loading状态
  importLoading.value = false
}

/**
 * 处理结果关闭
 */
const handleResultClose = () => {
  showResultModal.value = false
  importResult.value = null
  fileList.value = []
}

/**
 * 获取导入记录列表
 */
const fetchImportRecords = async () => {
  try {
    recordsLoading.value = true

    const params = {
      页码: recordsPagination.current,
      每页数量: recordsPagination.pageSize,
      状态筛选: recordsFilter.状态筛选
    }

    const response = await orderService.getImportRecords(params)

    if (response.status === 100) {
      importRecords.value = response.data.列表 || []
      recordsPagination.total = response.data.总数 || 0

      // 启动进行中任务的轮询
      startProgressPolling()
    } else {
      message.error(response.message || '获取导入记录失败')
    }
  } catch (error) {
    console.error('获取导入记录失败:', error)
    message.error('获取导入记录失败')
  } finally {
    recordsLoading.value = false
  }
}

/**
 * 启动进度轮询
 */
const startProgressPolling = () => {
  // 清除现有的轮询定时器
  progressPollingTimers.value.forEach(timer => clearInterval(timer))
  progressPollingTimers.value.clear()

  // 为进行中的任务启动轮询
  importRecords.value.forEach(record => {
    if (record.任务状态 === '进行中') {
      const timer = setInterval(async () => {
        await refreshRecordProgress(record.任务ID, false)
      }, 3000) // 每3秒轮询一次

      progressPollingTimers.value.set(record.任务ID, timer)
    }
  })
}

/**
 * 停止进度轮询
 */
const stopProgressPolling = () => {
  progressPollingTimers.value.forEach(timer => clearInterval(timer))
  progressPollingTimers.value.clear()
}

/**
 * 刷新单个记录的进度
 */
const refreshRecordProgress = async (taskId, showMessage = true) => {
  try {
    if (showMessage) {
      refreshingRecords.value.add(taskId)
    }

    const response = await orderService.getImportProgress(taskId)

    if (response.status === 100) {
      const updatedRecord = response.data

      // 更新记录列表中的对应记录
      const index = importRecords.value.findIndex(record => record.任务ID === taskId)
      if (index !== -1) {
        importRecords.value[index] = updatedRecord

        // 如果任务已完成，停止该任务的轮询
        if (updatedRecord.任务状态 !== '进行中') {
          const timer = progressPollingTimers.value.get(taskId)
          if (timer) {
            clearInterval(timer)
            progressPollingTimers.value.delete(taskId)
          }
        }
      }

      // 如果详情弹窗打开且是同一个记录，也更新详情
      if (selectedRecord.value && selectedRecord.value.任务ID === taskId) {
        selectedRecord.value = updatedRecord
      }

      if (showMessage) {
        message.success('进度刷新成功')
      }
    } else {
      if (showMessage) {
        message.error(response.message || '刷新进度失败')
      }
    }
  } catch (error) {
    console.error('刷新进度失败:', error)
    if (showMessage) {
      message.error('刷新进度失败')
    }
  } finally {
    if (showMessage) {
      refreshingRecords.value.delete(taskId)
    }
  }
}

/**
 * 处理导入记录表格变化
 */
const handleRecordsTableChange = (pag) => {
  recordsPagination.current = pag.current
  recordsPagination.pageSize = pag.pageSize
  fetchImportRecords()
}

/**
 * 显示记录详情
 */
const showRecordDetail = (record) => {
  selectedRecord.value = record
  showRecordDetailModal.value = true
}

/**
 * 处理导入记录弹窗取消
 */
const handleImportRecordsCancel = () => {
  showImportRecordsModal.value = false
  stopProgressPolling()
}

/**
 * 处理记录详情弹窗取消
 */
const handleRecordDetailCancel = () => {
  showRecordDetailModal.value = false
  selectedRecord.value = null
}

/**
 * 处理续传导入
 */
const handleContinueImport = async (record) => {
  try {
    continuingRecords.value.add(record.id)

    // 显示确认弹窗
    const confirmed = await new Promise((resolve) => {
      const modal = Modal.confirm({
        title: '确认续传导入',
        content: `
          <div style="margin-top: 16px;">
            <p><strong>文件名：</strong>${record.文件名}</p>
            <p><strong>当前进度：</strong>${Math.round(record.进度百分比)}%</p>
            <p><strong>已处理：</strong>${record.已处理行数 || 0} 行</p>
            <p><strong>剩余数据：</strong>约 ${Math.max(0, (record.总行数 || 0) - (record.已处理行数 || 0))} 行</p>
            <p style="color: #666; margin-top: 12px;">续传将从上次中断的位置继续处理，预计需要 ${Math.ceil(((record.总行数 || 0) - (record.已处理行数 || 0)) / 1000 * 2)} 分钟</p>
          </div>
        `,
        okText: '确认续传',
        cancelText: '取消',
        onOk: () => resolve(true),
        onCancel: () => resolve(false)
      })
    })

    if (!confirmed) {
      return
    }

    // 调用续传接口
    const response = await orderService.continueImport(record.id)

    if (response.status === 100) {
      message.success('续传任务启动成功，请稍后查看进度')

      // 刷新导入记录列表
      await fetchImportRecords()

      // 开始轮询进度
      startProgressPolling()

    } else {
      message.error(response.message || '续传失败')
    }

  } catch (error) {
    console.error('续传导入失败:', error)
    message.error('续传失败，请稍后重试')
  } finally {
    continuingRecords.value.delete(record.id)
  }
}

/**
 * 处理查看订单详情
 */
const handleViewDetail = async (record) => {
  try {
    orderDetailLoading.value = true
    showOrderDetailModal.value = true
    orderDetail.value = null

    const response = await orderService.getOrderDetail(record.订单id)

    if (response.status === 100) {
      orderDetail.value = response.data
    } else {
      message.error(response.message || '获取订单详情失败')
      showOrderDetailModal.value = false
    }
  } catch (error) {
    console.error('获取订单详情失败:', error)
    message.error('获取订单详情失败')
    showOrderDetailModal.value = false
  } finally {
    orderDetailLoading.value = false
  }
}

/**
 * 处理订单详情弹窗取消
 */
const handleOrderDetailCancel = () => {
  showOrderDetailModal.value = false
  orderDetail.value = null
}

/**
 * 获取导入记录状态颜色
 */
const getImportStatusColor = (status) => {
  const colorMap = {
    '进行中': 'processing',
    '已完成': 'success',
    '失败': 'error',
    '超时': 'warning',
    '部分失败': 'orange'
  }
  return colorMap[status] || 'default'
}

/**
 * 获取进度条状态
 */
const getProgressStatus = (status) => {
  if (status === '失败') return 'exception'
  if (status === '已完成') return 'success'
  return 'active'
}

// 监听导入记录弹窗状态
watch(showImportRecordsModal, (newVal) => {
  if (newVal) {
    // 弹窗打开时获取导入记录
    fetchImportRecords()
  } else {
    // 弹窗关闭时停止轮询
    stopProgressPolling()
  }
})

// 生命周期
onMounted(() => {
  fetchOrderList()
  fetchStatusOptions()
})

onUnmounted(() => {
  // 组件卸载时清理轮询定时器
  stopProgressPolling()

  // 清理上传进度定时器
  if (progressTimer) {
    clearInterval(progressTimer)
    progressTimer = null
  }
})
</script>

<style scoped>
.order-management-module {
  padding: 24px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 操作栏样式 */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.filter-label {
  color: #666;
  font-weight: 500;
  font-size: 14px;
}

.action-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.action-right {
  display: flex;
  align-items: center;
}

/* 表格容器样式 */
.table-container {
  background: #ffffff;
  border-radius: 6px;
  overflow: hidden;
}

/* 表格内容样式 */
.order-id {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 600;
  color: #1890ff;
}

.amount {
  font-weight: 600;
  color: #52c41a;
}

.rate {
  font-weight: 500;
  color: #722ed1;
}

.quantity {
  font-weight: 600;
  color: #1890ff;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 24px;
  background: #f0f8ff;
  border-radius: 4px;
  border: 1px solid #d6e4ff;
  padding: 0 6px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.quantity:hover {
  background: #e6f4ff;
  border-color: #91caff;
}

/* 导入弹窗样式 */
.import-content {
  padding: 8px 0;
}

.import-tips {
  margin-bottom: 16px;
}

.upload-area {
  margin: 16px 0;
}

/* 上传进度样式 */
.upload-progress {
  padding: 16px 0;
  min-height: 200px;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.progress-header h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  transition: color 0.3s ease;
}

.progress-header p {
  font-size: 14px;
  margin: 8px 0 0 0;
  color: #666;
  transition: color 0.3s ease;
}

.progress-content {
  padding: 0 16px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.progress-info span:last-child {
  transition: color 0.3s ease;
}

.file-info {
  margin-top: 16px;
  padding: 12px;
  background: #f5f5f5;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
  transition: all 0.3s ease;
}

.file-info:hover {
  background: #f0f0f0;
  border-color: #d9d9d9;
}

.file-info .ant-typography {
  margin: 0;
}

.progress-actions {
  margin-top: 24px;
  text-align: right;
  padding: 0 16px;
}

/* 导入结果样式 */
.import-result {
  padding: 8px 0;
}

.result-summary {
  margin-bottom: 16px;
}

.error-details {
  max-height: 200px;
  overflow-y: auto;
}

.error-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.error-item {
  padding: 4px 0;
}

/* 导入记录样式 */
.import-records-content {
  padding: 8px 0;
}

.records-filter {
  padding: 12px 16px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.records-table {
  background: #ffffff;
  border-radius: 6px;
  overflow: hidden;
}

.file-name {
  display: flex;
  align-items: center;
  font-weight: 500;
}

.progress-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-text {
  font-size: 12px;
  color: #666;
  min-width: 35px;
}

.stats-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.stat-label {
  color: #666;
  min-width: 32px;
}

.stat-value {
  font-weight: 500;
}

.stat-item.success .stat-value {
  color: #52c41a;
}

.stat-item.error .stat-value {
  color: #ff4d4f;
}

.stat-item.skip .stat-value {
  color: #faad14;
}

.record-detail-content {
  padding: 8px 0;
}

.error-message {
  padding: 8px 12px;
  background: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
  color: #ff4d4f;
  font-size: 12px;
  line-height: 1.4;
  max-height: 100px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .order-management-module {
    padding: 16px;
  }

  .action-bar {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .action-left,
  .action-right {
    justify-content: center;
  }
}
</style>
