<template>
  <div class="upcoming-membership-card" v-if="memberships && memberships.length > 0">
    <a-card :bordered="false" class="upcoming-card">
      <div class="upcoming-header">
        <div class="header-title">
          <calendar-outlined class="header-icon" />
          <h4>即将生效的会员</h4>
          <a-badge :count="memberships.length" :color="'#1890ff'" />
        </div>
      </div>
      
      <div class="upcoming-list">
        <div 
          v-for="(membership, index) in memberships" 
          :key="membership.会员id || index"
          class="upcoming-item"
        >
          <div class="item-header">
            <div class="item-info">
              <component :is="getMembershipIcon(membership)" class="item-icon" />
              <div class="item-details">
                <h5 class="item-name">{{ membership.会员名称 }}</h5>
                <p class="item-description">{{ getMembershipDescription(membership) }}</p>
              </div>
            </div>
            <a-tag color="blue" class="item-status">待生效</a-tag>
          </div>
          
          <div class="item-content">
            <a-row :gutter="16">
              <a-col :span="8">
                <div class="content-item">
                  <span class="label">生效时间:</span>
                  <span class="value">{{ formatDate(membership.开通时间) }}</span>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="content-item">
                  <span class="label">有效期:</span>
                  <span class="value">{{ getDuration(membership) }}</span>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="content-item">
                  <span class="label">月费价格:</span>
                  <span class="value">¥{{ membership.每月费用 || 0 }}</span>
                </div>
              </a-col>
            </a-row>
          </div>
          
          <!-- 无缝衔接提示 -->
          <div v-if="isSeamlessConnection(membership)" class="seamless-tip">
            <a-alert
              message="无缝衔接，权益不中断"
              type="success"
              :show-icon="true"
              :closable="false"
            >
              <template #icon>
                <check-circle-outlined />
              </template>
            </a-alert>
          </div>
          
          <!-- 时间间隔提示 -->
          <div v-else-if="getTimeGap(membership) > 0" class="time-gap-tip">
            <a-alert
              :message="`将在当前会员到期${getTimeGap(membership)}天后生效`"
              type="warning"
              :show-icon="true"
              :closable="false"
            >
              <template #icon>
                <clock-circle-outlined />
              </template>
            </a-alert>
          </div>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { 
  CalendarOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  CrownOutlined,
  GoldOutlined,
  RocketOutlined,
  StarOutlined
} from '@ant-design/icons-vue'

// 组件属性
const props = defineProps({
  memberships: {
    type: Array,
    default: () => []
  },
  currentMembership: {
    type: Object,
    default: null
  }
})

/**
 * 获取会员图标
 */
const getMembershipIcon = (membership) => {
  if (!membership) return StarOutlined
  
  const membershipName = membership.会员名称 || ''
  if (membershipName.includes('企业')) return GoldOutlined
  if (membershipName.includes('专业')) return RocketOutlined
  if (membershipName.includes('高级')) return CrownOutlined
  return StarOutlined
}

/**
 * 获取会员描述
 */
const getMembershipDescription = (membership) => {
  if (!membership) return ''
  
  const membershipName = membership.会员名称 || ''
  if (membershipName.includes('企业')) return '享受全功能权益，助力业务增长'
  if (membershipName.includes('专业')) return '专业功能全开放，提升工作效率'
  if (membershipName.includes('高级')) return '高级功能体验，满足进阶需求'
  return `${membershipName}套餐，享受对应权益`
}

/**
 * 格式化日期
 */
const formatDate = (dateString) => {
  if (!dateString) return '未知'
  
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return '日期格式错误'
  }
}

/**
 * 获取会员持续时间
 */
const getDuration = (membership) => {
  if (!membership.开通时间 || !membership.到期时间) return '未知'
  
  try {
    const startDate = new Date(membership.开通时间)
    const endDate = new Date(membership.到期时间)
    const diffTime = endDate - startDate
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays < 30) return `${diffDays}天`
    if (diffDays < 365) return `${Math.floor(diffDays / 30)}个月`
    return `${Math.floor(diffDays / 365)}年`
  } catch (error) {
    return '未知'
  }
}

/**
 * 判断是否无缝衔接
 */
const isSeamlessConnection = (membership) => {
  if (!props.currentMembership || !membership.开通时间) return false
  
  try {
    const currentExpiry = new Date(props.currentMembership.到期时间)
    const upcomingStart = new Date(membership.开通时间)
    
    // 计算时间差（毫秒）
    const timeDiff = upcomingStart - currentExpiry
    // 如果时间差在1天内，认为是无缝衔接
    return Math.abs(timeDiff) <= 24 * 60 * 60 * 1000
  } catch (error) {
    return false
  }
}

/**
 * 获取时间间隔（天数）
 */
const getTimeGap = (membership) => {
  if (!props.currentMembership || !membership.开通时间) return 0
  
  try {
    const currentExpiry = new Date(props.currentMembership.到期时间)
    const upcomingStart = new Date(membership.开通时间)
    
    const timeDiff = upcomingStart - currentExpiry
    return Math.ceil(timeDiff / (1000 * 60 * 60 * 24))
  } catch (error) {
    return 0
  }
}
</script>

<style scoped>
.upcoming-membership-card {
  margin-bottom: 24px;
}

.upcoming-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.upcoming-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.upcoming-header {
  margin-bottom: 16px;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-icon {
  font-size: 18px;
  color: #1890ff;
}

.header-title h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.upcoming-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.upcoming-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
}

.upcoming-item:hover {
  background: #f0f7ff;
  border-color: #1890ff;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.item-info {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  flex: 1;
}

.item-icon {
  font-size: 20px;
  color: #1890ff;
  margin-top: 2px;
}

.item-details {
  flex: 1;
}

.item-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.item-description {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.item-status {
  font-weight: 500;
}

.item-content {
  margin-bottom: 12px;
}

.content-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.content-item .label {
  color: #666;
  font-size: 14px;
}

.content-item .value {
  font-weight: 500;
  color: #1a1a1a;
  font-size: 14px;
}

.seamless-tip,
.time-gap-tip {
  margin-top: 12px;
}

.seamless-tip :deep(.ant-alert) {
  border-color: #52c41a;
  background-color: #f6ffed;
}

.time-gap-tip :deep(.ant-alert) {
  border-color: #faad14;
  background-color: #fffbe6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .item-header {
    flex-direction: column;
    gap: 12px;
  }
  
  .item-info {
    width: 100%;
  }
  
  .item-content .ant-row {
    flex-direction: column;
  }
  
  .item-content .ant-col {
    width: 100% !important;
    margin-bottom: 8px;
  }
  
  .content-item {
    border-bottom: 1px solid #f0f0f0;
    padding: 8px 0;
  }
  
  .content-item:last-child {
    border-bottom: none;
  }
}
</style> 