/**
 * 微信自动化配置服务
 * 
 * 该服务负责处理微信自动添加好友配置的所有API调用
 * 包括配置的增删改查、概览数据获取、微信账号管理等功能
 */

import { 主API客户端 as apiClient } from './apiClientFactory'

// 备用默认配置常量（仅在API失败时使用）
const FALLBACK_CONFIG = {
  配置名称: '默认配置',
  每日最大添加次数: 20,
  最小添加间隔分钟: 15,
  最大添加间隔分钟: 25,
  工作开始时间: '09:00',
  工作结束时间: '22:00',
  午休开始时间: '12:00',
  午休结束时间: '14:00',
  是否启用午休: 1,
  周末是否添加: 0,
  周末每日最大添加次数: 10,
  连续添加次数上限: 5,
  批次休息最小分钟: 60,
  批次休息最大分钟: 120,
  随机延迟最小分钟: 0,
  随机延迟最大分钟: 5,
  成功率模拟概率: 0.95,
  每小时最大添加次数: 4,
  异常检测暂停分钟: 30,
  验证消息模板: '你好，我是{达人昵称}的朋友，想和你交流一下',
  好友备注模板: '{达人昵称}-{联系方式类型}',
  是否启用: 1
}

/**
 * 微信自动化配置服务类
 */
class WeChatAutomationService {
  
  /**
   * 获取配置概览数据
   * 获取配置总数、启用数量、绑定账号数、今日添加等统计信息
   * 
   * @returns {Promise<Object>} API响应对象
   */
  async getConfigOverview() {
    try {
      const response = await apiClient.post('/wechat/automation/overview')
      return response
    } catch (error) {
      console.error('获取微信自动化配置概览失败:', error)
      throw error
    }
  }

  /**
   * 获取配置列表
   * 支持分页、搜索和筛选功能
   *
   * @param {Object} 查询参数 - 查询参数
   * @param {number} 查询参数.页码 - 页码
   * @param {number} 查询参数.每页数量 - 每页大小
   * @param {string} [查询参数.搜索关键词] - 搜索关键词
   * @returns {Promise<Object>} API响应对象
   */
  async getConfigList(查询参数 = {}) {
    try {
      const requestParams = {
        页码: 查询参数.页码 || 查询参数.page || 1,
        每页数量: 查询参数.每页数量 || 查询参数.pageSize || 10,
        搜索关键词: 查询参数.搜索关键词 || 查询参数.search || ''
      }

      const response = await apiClient.post('/wechat/automation/list', requestParams)
      return response
    } catch (error) {
      console.error('获取微信自动化配置列表失败:', error)
      throw error
    }
  }

  /**
   * 获取微信账号列表
   * 获取用户可用的微信账号，用于配置绑定
   * 
   * @returns {Promise<Object>} API响应对象
   */
  async getWechatAccounts() {
    try {
      const response = await apiClient.post('/wechat/automation/accounts')
      return response
    } catch (error) {
      console.error('获取微信账号列表失败:', error)
      throw error
    }
  }

  /**
   * 创建新的微信自动化配置
   * 
   * @param {Object} configData - 配置数据
   * @param {string} configData.配置名称 - 配置名称
   * @param {number} configData.微信信息表id - 绑定的微信账号ID
   * @param {number} configData.每日最大添加次数 - 每日最大添加次数
   * @param {number} configData.最小添加间隔分钟 - 最小添加间隔(分钟)
   * @param {number} configData.最大添加间隔分钟 - 最大添加间隔(分钟)
   * @param {string} configData.工作开始时间 - 工作开始时间(HH:mm:ss)
   * @param {string} configData.工作结束时间 - 工作结束时间(HH:mm:ss)
   * @param {string} [configData.午休开始时间] - 午休开始时间(HH:mm:ss)
   * @param {string} [configData.午休结束时间] - 午休结束时间(HH:mm:ss)
   * @param {number} configData.是否启用午休 - 是否启用午休(0/1)
   * @param {number} configData.周末是否添加 - 周末是否添加(0/1)
   * @param {number} [configData.周末每日最大添加次数] - 周末每日最大添加次数
   * @param {number} configData.连续添加次数上限 - 连续添加次数上限
   * @param {number} configData.批次休息最小分钟 - 批次休息最小分钟
   * @param {number} configData.批次休息最大分钟 - 批次休息最大分钟
   * @param {number} configData.随机延迟最小分钟 - 随机延迟最小分钟
   * @param {number} configData.随机延迟最大分钟 - 随机延迟最大分钟
   * @param {number} configData.成功率模拟概率 - 成功率模拟概率(0-1)
   * @param {number} configData.每小时最大添加次数 - 每小时最大添加次数
   * @param {number} configData.异常检测暂停分钟 - 异常检测暂停分钟
   * @param {number} configData.是否启用 - 是否启用配置(0/1)
   * @returns {Promise<Object>} API响应对象
   */
  async createConfig(configData) {
    try {
      console.log('🚀 创建微信自动化配置:', configData)
      
      // 数据格式转换和验证
      const requestData = this.formatConfigData(configData)
      
      const response = await apiClient.post('/wechat/automation/create', requestData)
      
      if (response.status === 100) {
        console.log('✅ 微信自动化配置创建成功')
      }
      
      return response
    } catch (error) {
      console.error('创建微信自动化配置失败:', error)
      throw error
    }
  }

  /**
   * 更新微信自动化配置
   * 
   * @param {number} configId - 配置id
   * @param {Object} configData - 更新的配置数据(结构同创建)
   * @returns {Promise<Object>} API响应对象
   */
  async updateConfig(configId, configData) {
    try {
      console.log('🔄 更新微信自动化配置:', { configId, configData })
      
      if (!configId) {
        throw new Error('配置id不能为空')
      }
      
      // 数据格式转换和验证
      const requestData = this.formatConfigData(configData)
      requestData.配置id = configId
      
      const response = await apiClient.post('/wechat/automation/update', requestData)
      
      if (response.status === 100) {
        console.log('✅ 微信自动化配置更新成功')
      }
      
      return response
    } catch (error) {
      console.error('更新微信自动化配置失败:', error)
      throw error
    }
  }

  /**
   * 删除微信自动化配置
   * 
   * @param {number} configId - 配置id
   * @returns {Promise<Object>} API响应对象
   */
  async deleteConfig(configId) {
    try {
      console.log('🗑️ 删除微信自动化配置:', configId)
      
      if (!configId) {
        throw new Error('配置id不能为空')
      }
      
      const response = await apiClient.post('/wechat/automation/delete', {
        配置id: configId
      })
      
      if (response.status === 100) {
        console.log('✅ 微信自动化配置删除成功')
      }
      
      return response
    } catch (error) {
      console.error('删除微信自动化配置失败:', error)
      throw error
    }
  }

  /**
   * 获取微信配置 - 统一接口
   * 支持根据配置id或微信信息表ID获取配置
   *
   * @param {Object} 参数 - 查询参数
   * @param {number} [参数.配置id] - 配置id
   * @param {number} [参数.微信信息表id] - 微信信息表ID
   * @returns {Promise<Object>} API响应对象
   */
  async getConfig(参数) {
    try {
      if (!参数.配置id && !参数.微信信息表id) {
        throw new Error('配置id和微信信息表id必须提供其中一个')
      }

      const response = await apiClient.post('/wechat/automation/config/detail', 参数)
      return response
    } catch (error) {
      console.error('获取微信配置失败:', error)
      throw error
    }
  }

  /**
   * 启用/禁用配置
   * 
   * @param {number} configId - 配置id
   * @param {boolean} enabled - 是否启用
   * @returns {Promise<Object>} API响应对象
   */
  async toggleConfigStatus(configId, enabled) {
    try {
      console.log(`${enabled ? '🟢 启用' : '🔴 禁用'}微信自动化配置:`, configId)
      
      if (!configId) {
        throw new Error('配置id不能为空')
      }
      
      const response = await apiClient.post('/wechat/automation/update', {
        配置id: configId,
        是否启用: enabled ? 1 : 0
      })
      
      if (response.status === 100) {
        console.log(`✅ 配置${enabled ? '启用' : '禁用'}成功`)
      }
      
      return response
    } catch (error) {
      console.error('切换微信自动化配置状态失败:', error)
      throw error
    }
  }

  /**
   * 复制配置
   * 基于现有配置创建一个新的配置副本
   * 
   * @param {number} sourceConfigId - 源配置id
   * @param {string} newConfigName - 新配置名称
   * @returns {Promise<Object>} API响应对象
   */
  async duplicateConfig(sourceConfigId, newConfigName) {
    try {
      console.log('📋 复制微信自动化配置:', { sourceConfigId, newConfigName })
      
      if (!sourceConfigId) {
        throw new Error('源配置id不能为空')
      }
      
      if (!newConfigName || newConfigName.trim() === '') {
        throw new Error('新配置名称不能为空')
      }
      
      const response = await apiClient.post('/wechat/automation/create', {
        源配置id: sourceConfigId,
        新配置名称: newConfigName.trim()
      })
      
      if (response.status === 100) {
        console.log('✅ 微信自动化配置复制成功')
      }
      
      return response
    } catch (error) {
      console.error('复制微信自动化配置失败:', error)
      throw error
    }
  }

  /**
   * 获取配置的运行统计
   * 获取指定配置的运行数据，如添加成功数、失败数等
   *
   * @param {number} configId - 配置id
   * @param {string} [dateRange='today'] - 时间范围: today, week, month
   * @returns {Promise<Object>} API响应对象
   */
  async getConfigStats(configId, dateRange = 'today') {
    try {
      if (!configId) {
        throw new Error('配置id不能为空')
      }

      const response = await apiClient.post('/wechat/automation/config/stats', {
        配置id: configId,
        时间范围: dateRange
      })

      return response
    } catch (error) {
      console.error('获取微信自动化配置统计失败:', error)
      throw error
    }
  }

  /**
   * 验证配置数据
   * 在提交前验证配置数据的合法性
   * 
   * @param {Object} configData - 配置数据
   * @returns {Object} 验证结果 { valid: boolean, errors: Array }
   */
  validateConfigData(configData) {
    const errors = []
    
    // 基础字段验证
    if (!configData.配置名称 || configData.配置名称.trim() === '') {
      errors.push('配置名称不能为空')
    }
    
    if (!configData.微信信息表id) {
      errors.push('必须绑定微信账号')
    }
    
    // 数值范围验证
    if (configData.每日最大添加次数 < 1 || configData.每日最大添加次数 > 50) {
      errors.push('每日最大添加次数必须在1-50之间')
    }

    if (configData.最小添加间隔分钟 < 1 || configData.最小添加间隔分钟 > 120) {
      errors.push('最小添加间隔必须在1-120分钟之间')
    }

    if (configData.最大添加间隔分钟 < 10 || configData.最大添加间隔分钟 > 180) {
      errors.push('最大添加间隔必须在10-180分钟之间')
    }

    if (configData.每小时最大添加次数 < 1 || configData.每小时最大添加次数 > 10) {
      errors.push('每小时最大添加次数必须在1-10之间')
    }

    if (configData.最小添加间隔分钟 >= configData.最大添加间隔分钟) {
      errors.push('最小添加间隔必须小于最大添加间隔')
    }
    
    // 时间逻辑验证
    if (configData.工作开始时间 >= configData.工作结束时间) {
      errors.push('工作开始时间必须早于结束时间')
    }
    
    if (configData.是否启用午休 && configData.午休开始时间 && configData.午休结束时间) {
      if (configData.午休开始时间 >= configData.午休结束时间) {
        errors.push('午休开始时间必须早于结束时间')
      }
    }
    
    // 概率值验证
    if (configData.成功率模拟概率 < 0 || configData.成功率模拟概率 > 1) {
      errors.push('成功率模拟概率必须在0-1之间')
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * 格式化配置数据
   * 将前端表单数据转换为后端API期望的格式
   * 
   * @private
   * @param {Object} configData - 原始配置数据
   * @returns {Object} 格式化后的配置数据
   */
  formatConfigData(configData) {
    // 创建数据副本，避免修改原数据
    const formatted = { ...configData }
    
    // 确保数值类型正确
    const numberFields = [
      '每日最大添加次数', '最小添加间隔分钟', '最大添加间隔分钟',
      '连续添加次数上限', '批次休息最小分钟', '批次休息最大分钟',
      '随机延迟最小分钟', '随机延迟最大分钟', '每小时最大添加次数',
      '异常检测暂停分钟', '周末每日最大添加次数', '微信信息表id'
    ]
    
    numberFields.forEach(field => {
      if (formatted[field] !== undefined && formatted[field] !== null) {
        formatted[field] = Number(formatted[field])
      }
    })
    
    // 确保浮点数精度
    if (formatted.成功率模拟概率 !== undefined) {
      formatted.成功率模拟概率 = Number(Number(formatted.成功率模拟概率).toFixed(2))
    }
    
    // 处理时间字段 - 简化处理，直接格式化dayjs对象
    const timeFields = ['工作开始时间', '工作结束时间', '午休开始时间', '午休结束时间']
    timeFields.forEach(field => {
      if (formatted[field] && typeof formatted[field] === 'object' && formatted[field].format) {
        // 如果是dayjs对象，格式化为HH:mm:ss
        formatted[field] = formatted[field].format('HH:mm:ss')
      }
      // 字符串和null值直接保持原样，让后端处理
    })
    
    // 确保布尔值转换为整数
    const booleanFields = ['是否启用午休', '周末是否添加']
    booleanFields.forEach(field => {
      if (formatted[field] !== undefined) {
        formatted[field] = formatted[field] ? 1 : 0
      }
    })
    
    // 清理空字符串
    Object.keys(formatted).forEach(key => {
      if (formatted[key] === '') {
        formatted[key] = null
      }
    })
    
    console.log('📝 格式化配置数据:', { original: configData, formatted })
    
    return formatted
  }

  /**
   * 获取默认配置
   * 从后端获取动态默认配置值
   *
   * @returns {Promise<Object>} 默认配置对象
   */
  async getDefaultConfig() {
    try {
      const response = await apiClient.get('/wechat/automation/default-config')

      if (response.status === 100) {
        console.log('✅ 获取默认配置成功')
        return response.data
      } else {
        console.warn('⚠️ 获取默认配置失败，使用本地默认值')
        return { ...FALLBACK_CONFIG }
      }
    } catch (error) {
      console.error('获取默认配置失败:', error)
      // 返回本地备用默认配置
      return { ...FALLBACK_CONFIG }
    }
  }
}

// 导出服务实例
export const wechatAutomationService = new WeChatAutomationService()

// 默认导出
export default wechatAutomationService 