<template>
  <div>
    <!-- 筛选区域 -->
    <div class="filter-section">
      <a-card>
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="意向状态">
              <a-select 
                v-model:value="filterParams.意向状态" 
                placeholder="选择意向状态"
                allowClear
                @change="handleSearch"
              >
                <a-select-option 
                  v-for="option in intentionStatusOptions" 
                  :key="option.value" 
                  :value="option.value"
                >
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="样品状态">
              <a-select 
                v-model:value="filterParams.样品状态" 
                placeholder="选择样品状态"
                allowClear
                @change="handleSearch"
              >
                <a-select-option 
                  v-for="option in sampleStatusOptions" 
                  :key="option.value" 
                  :value="option.value"
                >
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="产品搜索">
              <a-input 
                v-model:value="filterParams.产品名称" 
                placeholder="搜索产品名称"
                allowClear
                @pressEnter="handleSearch"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="对方微信号">
              <a-input 
                v-model:value="filterParams.对方微信号" 
                placeholder="输入对方微信号"
                allowClear
                @pressEnter="handleSearch"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24" style="text-align: right;">
              <a-space>
                <a-button type="primary" @click="handleSearch" :loading="loading">
                  <template #icon><SearchOutlined /></template>
                  搜索
                </a-button>
                <a-button @click="handleReset">
                  <template #icon><ReloadOutlined /></template>
                  重置
                </a-button>
                <a-button type="primary" @click="showCreateModal" :disabled="!wechatId">
                  <template #icon><PlusOutlined /></template>
                  新增对接
                </a-button>
              </a-space>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 进度列表 -->
    <div class="progress-content">
      <a-card>
        <a-table
          :columns="columns"
          :dataSource="listData"
          :loading="loading"
          :pagination="pagination"
          :scroll="{ x: 1500 }"
          @change="handleTableChange"
          rowKey="id"
        >
          <!-- 表格内容 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'product'">
              <div>
                <div class="product-name">{{ record.产品名称 }}</div>
                <div class="product-id">产品ID: {{ record.合作产品ID }}</div>
              </div>
            </template>
            <template v-else-if="column.key === 'wechatInfo'">
              <div>
                <div>我方: {{ record.我方微信号 }}</div>
                <div>对方: {{ record.对方微信号 }}</div>
              </div>
            </template>
            <template v-else-if="column.key === 'replyStatus'">
              <a-tag :color="getStatusTagConfig('回复状态', record.回复状态).color">
                {{ getStatusTagConfig('回复状态', record.回复状态).text }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'intentionStatus'">
              <a-tag :color="getStatusTagConfig('意向状态', record.意向状态).color">
                {{ getStatusTagConfig('意向状态', record.意向状态).text }}
              </a-tag>
              <div v-if="record.意向状态更新时间" class="status-time">
                {{ formatDateTime(record.意向状态更新时间) }}
              </div>
            </template>
            <template v-else-if="column.key === 'sampleStatus'">
              <a-tag :color="getStatusTagConfig('样品状态', record.样品状态).color">
                {{ getStatusTagConfig('样品状态', record.样品状态).text }}
              </a-tag>
              <div v-if="record.样品状态更新时间" class="status-time">
                {{ formatDateTime(record.样品状态更新时间) }}
              </div>
            </template>
            <template v-else-if="column.key === 'scheduleStatus'">
              <a-tag :color="getStatusTagConfig('排期状态', record.排期状态).color">
                {{ getStatusTagConfig('排期状态', record.排期状态).text }}
              </a-tag>
              <div v-if="record.排期开始时间 && record.排期结束时间" class="status-time">
                {{ formatDate(record.排期开始时间) }} ~ {{ formatDate(record.排期结束时间) }}
              </div>
            </template>
            <template v-else-if="column.key === 'broadcastStatus'">
              <a-tag :color="getStatusTagConfig('开播状态', record.开播状态).color">
                {{ getStatusTagConfig('开播状态', record.开播状态).text }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'progress'">
              <div>
                <a-progress 
                  :percent="record.进度百分比" 
                  size="small" 
                  :stroke-color="getProgressColor(record.进度百分比)"
                />
                <div class="overall-status">{{ record.综合状态 }}</div>
              </div>
            </template>
            <template v-else-if="column.key === 'sales'">
              <div v-if="record.销售额" class="sales-amount">
                ¥{{ record.销售额 }}
              </div>
              <div v-else class="no-sales">--</div>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewDetail(record)">
                  详情
                </a-button>
                <a-button type="link" size="small" @click="editProgress(record)">
                  编辑
                </a-button>
                <a-popconfirm
                  title="确定要删除这条对接记录吗？"
                  @confirm="deleteProgress(record.id)"
                >
                  <a-button type="link" size="small" danger>
                    删除
                  </a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 弹窗等 -->
  </div>
</template>

<script setup>
import { ref, watch, defineProps } from 'vue';
import { 
  SearchOutlined, 
  ReloadOutlined, 
  PlusOutlined 
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import progressService from '@/services/friend/progressService';
import { 
  getStatusTagConfig, 
  columns, 
  getIntentionStatusOptions,
  getSampleStatusOptions,
  getScheduleStatusOptions
} from './progressConfig';
import { formatDateTime, formatDate } from '@/utils/dateUtils';

const props = defineProps({
  wechatId: Number,
  listData: Array,
  loading: Boolean,
  pagination: Object
});

const emit = defineEmits(['search', 'reset', 'create', 'edit', 'delete', 'tableChange']);

// 获取各状态选项
const intentionStatusOptions = getIntentionStatusOptions();
const sampleStatusOptions = getSampleStatusOptions();

const filterParams = ref({
  意向状态: undefined,
  样品状态: undefined,
  产品名称: '',
  对方微信号: '',
});

const handleSearch = () => {
  emit('search', filterParams.value);
};

const handleReset = () => {
  filterParams.value = {
    意向状态: undefined,
    样品状态: undefined,
    产品名称: '',
    对方微信号: '',
  };
  emit('reset');
};

const handleTableChange = (pagination, filters, sorter) => {
  // 处理排序参数
  const sortParams = {};
  if (sorter && sorter.field) {
    sortParams.排序字段 = sorter.field;
    sortParams.排序方式 = sorter.order === 'descend' ? 'desc' : 'asc';
  }
  
  emit('tableChange', pagination, filters, {...sortParams});
};

const showCreateModal = () => {
  emit('create');
};

const viewDetail = (record) => {
  // 可以通过emit事件或直接在父组件处理
};

const editProgress = (record) => {
  emit('edit', record);
};

const deleteProgress = (id) => {
  emit('delete', id);
};

const getProgressColor = (percent) => {
  if (percent < 50) return '#f5222d';
  if (percent < 80) return '#faad14';
  return '#52c41a';
};

// 监听wechatId变化，如果变化则清空筛选条件
watch(() => props.wechatId, () => {
  handleReset();
});
</script>

<style lang="less" scoped>
.filter-section {
  margin-bottom: 16px;
}
.status-time {
  font-size: 12px;
  color: #888;
  margin-top: 4px;
}
.product-name {
  font-weight: 500;
}
.product-id {
  font-size: 12px;
  color: #888;
}
.sales-amount {
  color: #f5222d;
  font-weight: 500;
}
.no-sales {
  color: #bfbfbf;
}
.overall-status {
  font-size: 12px;
  color: #888;
  margin-top: 4px;
}
</style> 