<template>
  <div class="store-page douyin-products-page">
    <!-- 页面头部 -->
    <div class="store-page-header">
      <h1 class="store-page-title">
        <shopping-outlined class="store-title-icon" />
        抖音商品
      </h1>
      <p class="store-page-description">管理您在抖音平台的商品信息和销售数据</p>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <DouyinProductsModule />
    </div>
  </div>
</template>

<script setup>
import { ShoppingOutlined } from '@ant-design/icons-vue'
import DouyinProductsModule from '@/components/store/DouyinProductsModule.vue'
import '@/assets/css/store-common.css'

defineOptions({
  name: 'DouyinProducts'
})
</script>

<style scoped>
/* 页面特有样式 */
.page-content {
  background: #ffffff;
}

/* 抖音商品特有样式 */
.page-content :deep(.product-grid) {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.page-content :deep(.product-card) {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.page-content :deep(.product-card:hover) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.page-content :deep(.product-image) {
  width: 100%;
  height: 200px;
  object-fit: cover;
  background: #f5f5f5;
}

.page-content :deep(.product-info) {
  padding: 16px;
}

.page-content :deep(.product-title) {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.page-content :deep(.product-price) {
  font-size: 16px;
  font-weight: 600;
  color: #ff4d4f;
  margin-bottom: 8px;
}

.page-content :deep(.product-stats) {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
}
</style>
