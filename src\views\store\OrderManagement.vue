<template>
  <div class="store-page order-management-page">
    <!-- 页面头部 -->
    <div class="store-page-header">
      <h1 class="store-page-title">
        <file-text-outlined class="store-title-icon" />
        订单管理
      </h1>
      <p class="store-page-description">管理商户订单信息，支持Excel批量导入</p>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <OrderManagementModule />
    </div>
  </div>
</template>

<script setup>
import { FileTextOutlined } from '@ant-design/icons-vue'
import OrderManagementModule from '@/components/store/OrderManagementModule.vue'
import '@/assets/css/store-common.css'

defineOptions({
  name: 'OrderManagement'
})
</script>

<style scoped>
/* 页面特有样式 */
.page-content {
  background: #ffffff;
}
</style>
