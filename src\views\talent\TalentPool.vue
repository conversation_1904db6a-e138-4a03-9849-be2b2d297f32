<template>
  <div class="talent-pool-page">
    <!-- 平台切换器 -->
    <PlatformSwitcher
      v-model="currentPlatform"
      :stats="platformStats"
      :show-stats="true"
      :show-description="false"
      @platform-change="handlePlatformChange"
      class="platform-switcher-section"
    />

    <!-- 顶部筛选搜索区域 -->
    <div class="search-section">
      <a-card :bordered="false" class="search-card">
        <a-row :gutter="16" align="middle">
          <!-- 实时搜索框 -->
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="search-input-wrapper">
              <a-auto-complete
                v-model:value="searchKeyword"
                :options="searchSuggestions"
                :loading="searchLoading"
                size="large"
                class="search-autocomplete"
                @search="handleSearchInput"
                @select="handleSelectSuggestion"
                @keydown.enter="handleEnterSearch"
                @dropdown-visible-change="handleDropdownVisibleChange"
              >
                <a-input
                  placeholder="搜索达人（昵称、抖音号等）"
                  allow-clear
                  size="large"
                  @clear="handleClearSearch"
                />
                <template #option="item">
                  <div class="search-suggestion-item">
                    <div class="suggestion-avatar">
                      <a-avatar :size="32" :src="item.avatar">
                        {{ item.nickname?.charAt(0) || '?' }}
                      </a-avatar>
                    </div>
                    <div class="suggestion-info">
                      <div class="suggestion-name">{{ item.nickname || '未知昵称' }}</div>
                      <div class="suggestion-stats">
                        <span class="fans-count">{{ formatNumber(item.follower_count) }}粉丝</span>
                        <span class="uid-info">ID: {{ item.uid_number }}</span>
                      </div>
                    </div>
                  </div>
                </template>
              </a-auto-complete>
              <a-button
                type="primary"
                size="large"
                class="search-button"
                :loading="searchLoading"
                @click="handleButtonSearch"
              >
                <SearchOutlined />
              </a-button>
            </div>
          </a-col>
          
          <!-- 联系方式筛选 -->
          <a-col :xs="12" :sm="6" :md="4" :lg="3">
            <a-select
              v-model:value="searchForm.有联系方式"
              placeholder="联系方式"
              size="large"
              style="width: 100%"
              allow-clear
              @change="handleSearch"
            >
              <a-select-option value="true">有联系方式</a-select-option>
              <a-select-option value="false">无联系方式</a-select-option>
            </a-select>
          </a-col>
          
          <!-- 平台特定筛选条件 - 移除粉丝数范围组件以优化性能 -->

          <!-- 微信达人特定筛选 -->
          <a-col :xs="12" :sm="6" :md="4" :lg="3" v-if="currentPlatform === 'wechat'">
            <a-select
              v-model:value="searchForm.地区"
              placeholder="地区"
              size="large"
              style="width: 100%"
              allow-clear
              @change="handleSearch"
            >
              <a-select-option value="北京">北京</a-select-option>
              <a-select-option value="上海">上海</a-select-option>
              <a-select-option value="广州">广州</a-select-option>
              <a-select-option value="深圳">深圳</a-select-option>
              <a-select-option value="杭州">杭州</a-select-option>
            </a-select>
          </a-col>

          <a-col :xs="12" :sm="6" :md="4" :lg="3" v-if="currentPlatform === 'wechat'">
            <a-select
              v-model:value="searchForm.性别"
              placeholder="性别"
              size="large"
              style="width: 100%"
              allow-clear
              @change="handleSearch"
            >
              <a-select-option value="男">男</a-select-option>
              <a-select-option value="女">女</a-select-option>
            </a-select>
          </a-col>
          
          <!-- 操作按钮 -->
          <a-col :xs="12" :sm="6" :md="3" :lg="3">
            <a-button 
              type="default" 
              size="large" 
              @click="handleReset" 
              block
              :loading="loading"
            >
              <ReloadOutlined />
              重置
            </a-button>
          </a-col>
          
          <a-col :xs="12" :sm="6" :md="3" :lg="4">
            <a-button 
              type="primary" 
              size="large" 
              @click="handleAdvancedFilter" 
              block
            >
              <FilterOutlined />
              高级筛选
            </a-button>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 主内容区域 -->
    <div class="content-section">
      <!-- 达人列表 -->
      <a-card :bordered="false" class="talent-list-card">
            <!-- 卡片头部 -->
            <template #title>
              <div class="list-header">
                <span class="title">{{ platformTitle }}公海</span>
                <a-badge
                  :count="talents.length"
                  :number-style="{ backgroundColor: platformColor }"
                  style="margin-left: 8px"
                />
                <!-- 统计信息 -->
                <div class="stats-info">
                  <span class="stat-item">总数: {{ totalCount }}</span>
                  <span class="stat-item">已加载: {{ talents.length }}</span>
                </div>
              </div>
            </template>
            
            <!-- 操作区 -->
            <template #extra>
              <a-space>
                <!-- 刷新按钮 -->
                <a-button
                  @click="refreshList"
                  :loading="loading"
                  type="text"
                  size="large"
                >
                  <ReloadOutlined />
                </a-button>
              </a-space>
            </template>

            <!-- 达人列表内容 -->
            <div class="talent-list-content">
              <!-- 加载状态 -->
              <a-spin :spinning="loading" size="large">
                <!-- 空状态 -->
                <div v-if="talents.length === 0 && !loading" class="empty-state">
                  <a-empty
                    :description="emptyDescription"
                    :image="Empty.PRESENTED_IMAGE_SIMPLE"
                  >
                    <a-button type="primary" @click="handleReset">
                      <ReloadOutlined />
                      重新加载
                    </a-button>
                  </a-empty>
                </div>

                <!-- 表格视图 -->
                <div class="table-view">
                  <a-table
                    :columns="tableColumns"
                    :data-source="talents"
                    :pagination="false"
                    :scroll="{ x: 1140 }"
                    size="middle"
                    :row-class-name="getRowClassName"
                    :loading="loading"
                    bordered
                    :custom-row="(record) => ({
                      onClick: () => selectTalent(record),
                      style: { cursor: 'pointer' }
                    })"
                  >
                    <!-- 自定义列渲染 -->
                    <template #bodyCell="{ column, record }">
                      <!-- 头像列 -->
                      <template v-if="column.key === 'avatar'">
                        <a-avatar 
                          :src="record.avatar || record.头像" 
                          :alt="record.nickname || record.昵称"
                          :size="40"
                        >
                          <template #icon>
                            <UserOutlined />
                          </template>
                        </a-avatar>
                      </template>
                      
                      <!-- 基本信息列 -->
                      <template v-if="column.key === 'basicInfo'">
                        <div class="table-basic-info">
                          <div class="table-name">{{ record.nickname || record.昵称 || '未知昵称' }}</div>
                          <div class="table-account">
                            {{ currentPlatform === 'wechat'
                                ? (record.微信号 || '微信达人')
                                : (record.account_douyin || '抖音号未知')
                            }}
                          </div>
                        </div>
                      </template>

                      <!-- 粉丝数列 -->
                      <template v-if="column.key === 'fans'">
                        <span class="fans-count">
                          {{ currentPlatform === 'wechat'
                              ? (record.粉丝数文本 || '未知')
                              : formatNumber(record.粉丝数)
                          }}
                        </span>
                      </template>

                      <!-- 微信达人GMV列 -->
                      <template v-if="column.key === 'gmv' && currentPlatform === 'wechat'">
                        <span class="gmv-text">{{ record.GMV文本 || '未知' }}</span>
                      </template>

                      <!-- 微信达人内容类型列 -->
                      <template v-if="column.key === 'contentType' && currentPlatform === 'wechat'">
                        <a-tag
                          v-if="record.内容类型 && record.内容类型.length > 0"
                          color="purple"
                          size="small"
                        >
                          {{ record.内容类型[0].name }}
                        </a-tag>
                        <span v-else class="text-gray">未知</span>
                      </template>
                      
                      <!-- 联系方式列 -->
                      <template v-if="column.key === 'contact'">
                        <div class="contact-display-wrapper">
                          <template v-if="getContactDisplay(record).hasContact">
                            <div class="contact-item-compact">
                              <!-- 联系方式图标 -->
                              <div class="contact-icon-wrapper">
                                <component
                                  :is="getContactIcon(getContactDisplay(record).type)"
                                  :class="['contact-icon', `contact-icon-${getContactDisplay(record).type}`]"
                                />
                              </div>

                              <!-- 联系方式内容 -->
                              <div class="contact-content">
                                <a-tooltip
                                  :title="`${getContactTypeLabel(getContactDisplay(record).type)}: ${getContactDisplay(record).value}`"
                                  placement="topLeft"
                                >
                                  <div class="contact-value">
                                    {{ formatContactValue(getContactDisplay(record).value) }}
                                  </div>
                                </a-tooltip>
                              </div>

                              <!-- 复制按钮 -->
                              <div class="contact-copy-btn">
                                <a-tooltip title="复制" placement="top">
                                  <a-button
                                    type="text"
                                    size="small"
                                    class="copy-btn-mini"
                                    @click="(e) => copyToClipboard(getContactDisplay(record).value, e)"
                                  >
                                    <CopyOutlined />
                                  </a-button>
                                </a-tooltip>
                              </div>

                              <!-- 联系方式数量提示 -->
                              <div
                                v-if="record.联系方式数量 > 1"
                                class="contact-count-mini"
                              >
                                <a-tooltip :title="`共有 ${record.联系方式数量} 个联系方式`" placement="top">
                                  <span class="count-mini">+{{ record.联系方式数量 - 1 }}</span>
                                </a-tooltip>
                              </div>
                            </div>
                          </template>

                          <!-- 无联系方式状态 -->
                          <template v-else>
                            <div class="no-contact-compact">
                              <PhoneOutlined class="no-contact-icon" />
                              <span class="no-contact-text">无</span>
                            </div>
                          </template>
                        </div>
                      </template>
                      
                      <!-- 状态列 -->
                      <template v-if="column.key === 'status'">
                        <div class="status-tags">
                          <!-- 认领状态 -->
                          <a-tag
                            :color="(record.已认领 || record.当前用户认领状态?.已认领) ? 'red' : 'green'"
                            size="small"
                          >
                            {{ (record.已认领 || record.当前用户认领状态?.已认领) ? '已认领' : '可认领' }}
                          </a-tag>

                          <!-- 账号状态 -->
                          <a-tag
                            :color="record.账号状态 === 1 ? 'orange' : 'blue'"
                            size="small"
                          >
                            {{ record.账号状态 === 1 ? '已注销' : '正常' }}
                          </a-tag>
                        </div>
                      </template>
                      
                      <!-- 操作列 -->
                      <template v-if="column.key === 'actions'">
                        <a-space>
                          <a-button
                            v-if="!record.已认领 && !record.当前用户认领状态?.已认领"
                            type="primary"
                            size="small"
                            @click.stop="handleClaimTalent(record)"
                            :loading="claimingIds.has(record.id)"
                          >
                            认领
                          </a-button>
                          
                          <a-button 
                            v-else
                            danger
                            size="small"
                            @click.stop="handleUnclaimTalent(record)"
                            :loading="claimingIds.has(record.id)"
                          >
                            取消认领
                          </a-button>
                          
                          <a-button 
                            size="small"
                            @click.stop="handleShareTalent(record)"
                          >
                            分享
                          </a-button>
                        </a-space>
                      </template>
                    </template>
                  </a-table>
                </div>
              </a-spin>
              
              <!-- 自动加载区域 -->
              <div class="auto-load-section">
                <!-- 加载中状态 -->
                <div v-if="loading && talents.length > 0" class="loading-indicator">
                  <a-spin size="small" />
                  <span style="margin-left: 8px;">正在加载更多...</span>
                </div>

                <!-- 触发器（有更多数据时显示） -->
                <div v-else-if="hasMore" class="load-trigger" ref="loadTrigger">
                  <div class="trigger-content">
                    <span style="color: #ccc; font-size: 12px;">滚动加载更多</span>
                  </div>
                </div>

                <!-- 加载完成提示 -->
                <div v-else-if="talents.length > 0" class="load-complete">
                  <a-divider>
                    <span style="color: #999; font-size: 12px;">
                      已加载全部 {{ totalCount }} 个达人
                    </span>
                  </a-divider>
                </div>
              </div>
            </div>
          </a-card>
    </div>

    <!-- 达人详情抽屉 -->
    <a-drawer
      v-model:open="showDetailDrawer"
      title="达人详情"
      placement="right"
      :width="480"
      :closable="true"
      @close="clearSelection"
    >
      <TalentDetailComponent
        v-if="selectedTalent"
        :talent="selectedTalent"
        :loading="detailLoading"
        :show-claim-button="true"
        @claim="handleClaimTalent"
        @unclaim="handleUnclaimTalent"
        @share="handleShareTalent"
        @update="handleTalentUpdate"
      />
    </a-drawer>



    <!-- 高级筛选弹窗 -->
    <AdvancedFilterComponent
      v-model:open="showAdvancedFilter"
      @apply="handleAdvancedFilterApply"
      @reset="handleAdvancedFilterReset"
    />
  </div>
</template>

<script setup>
import {
  CopyOutlined,
  FilterOutlined,
  PhoneOutlined,
  ReloadOutlined,
  SearchOutlined,
  UserOutlined
} from '@ant-design/icons-vue'
import { Empty, message } from 'ant-design-vue'
import { computed, nextTick, onMounted, onUnmounted, reactive, ref, watch } from 'vue'

// 导入组件和服务
import AdvancedFilterComponent from '../../components/talent/AdvancedFilterComponent.vue'
import PlatformSwitcher from '../../components/talent/PlatformSwitcher.vue'
import TalentDetailComponent from '../../components/talent/TalentDetailComponent.vue'
import talentService from '../../services/talentService'

defineOptions({
  name: 'TalentPool'
})

/**
 * 响应式数据定义
 * 管理页面的所有状态和数据
 */
// 基础状态
const loading = ref(false)              // 列表加载状态
const detailLoading = ref(false)        // 详情加载状态
const talents = ref([])                 // 达人列表数据
const selectedTalent = ref(null)        // 当前选中的达人
const totalCount = ref(0)               // 总数量
const hasMore = ref(true)               // 是否还有更多数据
const claimingIds = ref(new Set())      // 正在操作认领的达人id集合

// 当前平台
const currentPlatform = ref('douyin')

// 平台统计数据
const platformStats = ref({
  douyin: { total: 0, claimed: 0, withContact: 0 },
  wechat: { total: 0, claimed: 0, withContact: 0 }
})

// 搜索表单
const searchForm = reactive({
  关键词: '',                          // 统一的搜索关键词（抖音号/微信号/昵称）
  有联系方式: undefined,               // 联系方式筛选 - 字符串类型以兼容ASelect组件
  uid: null,                          // 新增：支持通过uid进行精准查询
  // 微信达人特有字段
  地区: null,
  性别: null
})

// 实时搜索相关状态
const searchKeyword = ref('')           // 搜索关键词
const searchLoading = ref(false)        // 搜索加载状态
const searchSuggestions = ref([])       // 搜索建议列表
let searchTimeout = null                // 防抖定时器
const isSelectingFromSuggestions = ref(false) // 是否正在从建议中选择

// 粉丝数范围筛选移至高级筛选中，移除普通搜索中的粉丝数范围以优化性能

// 视图模式 - 固定为表格模式
const viewMode = ref('table')           // 当前视图模式：固定为 table

// 高级筛选
const showAdvancedFilter = ref(false)   // 是否显示高级筛选弹窗
const advancedFilters = ref({})         // 高级筛选条件

// 详情抽屉
const showDetailDrawer = ref(false)     // 是否显示详情抽屉

// 自动加载相关
const loadTrigger = ref(null)           // 加载触发器元素引用
const isAutoLoading = ref(false)        // 是否正在自动加载
let intersectionObserver = null         // Intersection Observer 实例

// 分页相关
const pagination = reactive({
  页码: 1,
  每页数量: 20,                        // 优化为每次加载20个达人
  最后ID: 0                           // 流式分页的关键参数
})

/**
 * 计算属性
 * 基于响应式数据计算的派生状态
 */

// 平台相关计算属性
const platformTitle = computed(() => {
  return currentPlatform.value === 'douyin' ? '抖音达人' : '微信达人'
})

const platformColor = computed(() => {
  return currentPlatform.value === 'douyin' ? '#ff6b35' : '#07c160'
})

const searchPlaceholder = computed(() => {
  return currentPlatform.value === 'douyin' ? '搜索抖音号' : '搜索微信号、昵称'
})

const emptyDescription = computed(() => {
  const platform = currentPlatform.value === 'douyin' ? '抖音' : '微信'
  return `暂无${platform}达人数据，请尝试调整筛选条件或添加新的${platform}达人`
})

// 表格列定义 - 根据平台动态调整
const tableColumns = computed(() => {
  const baseColumns = [
    {
      title: '头像',
      key: 'avatar',
      width: 80,
      align: 'center'
    },
    {
      title: '基本信息',
      key: 'basicInfo',
      width: 220,
      ellipsis: true
    }
  ]

  // 根据平台添加不同的数据列
  if (currentPlatform.value === 'douyin') {
    baseColumns.push(
      {
        title: '粉丝数',
        key: 'fans',
        width: 100,
        align: 'right',
        sorter: (a, b) => (a.粉丝数 || 0) - (b.粉丝数 || 0)
      },
      {
        title: '关注数',
        dataIndex: '关注数',
        width: 80,
        align: 'right',
        customRender: ({ text }) => formatNumber(text),
        sorter: (a, b) => (a.关注数 || 0) - (b.关注数 || 0)
      },
      {
        title: '城市',
        dataIndex: 'city',
        width: 120,
        align: 'center',
        customRender: ({ text }) => text || '-',
        sorter: (a, b) => {
          const aCity = a.city || '';
          const bCity = b.city || '';
          return aCity.localeCompare(bCity, 'zh-CN');
        }
      }
    )
  } else if (currentPlatform.value === 'wechat') {
    baseColumns.push(
      {
        title: '粉丝数',
        key: 'fans',
        width: 120,
        align: 'center'
      },
      {
        title: 'GMV',
        key: 'gmv',
        width: 120,
        align: 'center'
      },
      {
        title: '内容类型',
        key: 'contentType',
        width: 120,
        align: 'center'
      }
    )
  }

  // 添加通用列
  baseColumns.push(
    {
      title: '联系方式',
      key: 'contact',
      width: 160,
      align: 'center'
    },
    {
      title: '状态',
      key: 'status',
      width: 120,
      align: 'center'
    },
    {
      title: '操作',
      key: 'actions',
      width: 160,
      align: 'center',
      fixed: 'right'
    }
  )

  return baseColumns
})



/**
 * 工具函数
 * 提供各种数据处理和格式化功能
 */

/**
 * 格式化数字显示
 * @param {number} num - 要格式化的数字
 * @returns {string} 格式化后的字符串
 */
const formatNumber = (num) => {
  if (!num || num === 0) return '0'
  
  if (num >= 100000000) {
    return (num / 100000000).toFixed(1) + '亿'
  } else if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  }
  
  return num.toString()
}

/**
 * 格式化粉丝数范围提示方法已移除，粉丝数范围筛选移至高级筛选中
 */

/**
 * 检查达人是否有联系方式
 * @param {Object} talent - 达人对象
 * @returns {boolean} 是否有联系方式
 */
const hasContactInfo = (talent) => {
  // 优先检查后端返回的联系方式状态字段
  if (talent.有联系方式_状态 !== undefined) {
    return talent.有联系方式_状态 === 1
  }

  // 兼容性检查：检查联系方式数量
  if (talent.联系方式数量 !== undefined) {
    return talent.联系方式数量 > 0
  }

  // 兼容性检查：检查其他可能的联系方式字段（用于详情页面等）
  return !!(
    talent.联系方式列表?.length ||
    talent.联系方式 ||
    talent.wechat_id ||
    talent.phone ||
    talent.email ||
    talent.有联系方式_计算 === 1 ||  // 微信达人的联系方式状态字段
    talent.有无联系方式 === 1        // 微信达人原始字段
  )
}

/**
 * 获取联系方式显示信息
 * @param {Object} talent - 达人对象
 * @returns {Object} 联系方式显示信息
 */
const getContactDisplay = (talent) => {
  // 优先使用后端返回的主要联系方式
  if (talent.主要联系方式 && talent.主要联系方式类型) {
    return {
      hasContact: true,
      value: talent.主要联系方式,
      type: talent.主要联系方式类型
    }
  }

  // 兼容微信达人的联系方式字段
  if (talent.有联系方式_计算 === 1 || talent.有无联系方式 === 1) {
    // 从联系方式列表中获取第一个
    if (talent.联系方式列表?.length) {
      const contact = talent.联系方式列表[0]
      return {
        hasContact: true,
        value: contact.联系内容 || contact.值,
        type: contact.联系类型 || contact.类型
      }
    }
  }

  // 兼容性检查：检查其他字段
  if (talent.wechat_id) {
    return { hasContact: true, value: talent.wechat_id, type: '微信' }
  }
  if (talent.phone) {
    return { hasContact: true, value: talent.phone, type: '手机' }
  }
  if (talent.email) {
    return { hasContact: true, value: talent.email, type: '邮箱' }
  }

  return { hasContact: false, value: '', type: '' }
}

/**
 * 获取联系方式类型对应的图标
 * @param {string} type - 联系方式类型
 * @returns {string} 图标组件名
 */
const getContactIcon = (type) => {
  switch (type) {
    case '微信':
      return 'WechatOutlined'
    case '手机':
    case 'phone':
      return 'PhoneOutlined'
    case '邮箱':
      return 'MailOutlined'
    default:
      return 'PhoneOutlined'
  }
}

/**
 * 获取联系方式类型对应的颜色
 * @param {string} type - 联系方式类型
 * @returns {string} 颜色值
 */
const getContactTypeColor = (type) => {
  switch (type) {
    case '微信':
      return 'green'
    case '手机':
    case 'phone':
      return 'blue'
    case '邮箱':
      return 'orange'
    default:
      return 'default'
  }
}

/**
 * 获取联系方式类型的显示标签
 * @param {string} type - 联系方式类型
 * @returns {string} 显示标签
 */
const getContactTypeLabel = (type) => {
  switch (type) {
    case '微信':
      return '微信号'
    case '手机':
    case 'phone':
      return '手机号'
    case '邮箱':
      return '邮箱'
    case '电话':
      return '电话'
    default:
      return '联系方式'
  }
}

/**
 * 格式化联系方式值的显示
 * @param {string} value - 联系方式值
 * @returns {string} 格式化后的显示值
 */
const formatContactValue = (value) => {
  if (!value) return ''

  // 由于联系方式列宽度增加，可以显示更多字符
  if (value.length > 16) {
    return value.substring(0, 13) + '...'
  }

  return value
}

/**
 * 复制到剪贴板
 * @param {string} text - 要复制的文本
 * @param {Event} event - 点击事件对象
 */
const copyToClipboard = async (text, event) => {
  // 阻止事件冒泡
  if (event) {
    event.stopPropagation()

    // 添加复制动画效果
    const button = event.currentTarget
    if (button) {
      button.classList.add('copying')
      setTimeout(() => {
        button.classList.remove('copying')
      }, 300)
    }
  }

  try {
    await navigator.clipboard.writeText(text)
    message.success({
      content: `已复制：${text.length > 20 ? text.substring(0, 20) + '...' : text}`,
      duration: 2,
      style: {
        marginTop: '10vh'
      }
    })
  } catch (err) {
    // 降级方案
    try {
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.opacity = '0'
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      message.success({
        content: `已复制：${text.length > 20 ? text.substring(0, 20) + '...' : text}`,
        duration: 2,
        style: {
          marginTop: '10vh'
        }
      })
    } catch (fallbackErr) {
      message.error('复制失败，请手动复制')
    }
  }
}

/**
 * 获取表格行的样式类名
 * @param {Object} record - 行数据
 * @returns {string} 样式类名
 */
const getRowClassName = (record) => {
  const classes = []
  
  if (selectedTalent.value?.id === record.id) {
    classes.push('selected-row')
  }
  
  if (record.已认领 || record.当前用户认领状态?.已认领) {
    classes.push('claimed-row')
  }
  
  return classes.join(' ')
}

/**
 * 数据加载相关方法
 */

/**
 * 加载达人列表数据
 * @param {boolean} append - 是否追加到现有数据（用于加载更多）
 */
const loadTalentList = async (append = false) => {
  if (loading.value) return
  
  try {
    loading.value = true
    
    // 构建请求参数
    const params = {
      页码: pagination.页码,
      每页数量: pagination.每页数量,
      最后ID: append ? pagination.最后ID : 0,
      关键词: searchForm.关键词 || null,
      // 将字符串值转换为Boolean值传递给后端API
      有联系方式: searchForm.有联系方式 === 'true' ? true : searchForm.有联系方式 === 'false' ? false : undefined,
      uid: searchForm.uid || null,  // 新增：传递uid参数进行精准查询
      筛选条件: {}
    }

    // 当使用uid精准查询时，跳过所有筛选条件以优化性能
    if (!searchForm.uid) {
      // 只有在非uid查询时才添加筛选条件
      params.筛选条件 = {
        ...advancedFilters.value
      }
      
      // 根据平台添加特定筛选条件
      if (currentPlatform.value === 'douyin') {
        // 抖音达人特有筛选条件 - 移除粉丝数范围以优化性能
        // 粉丝数范围筛选移至高级筛选中
      } else if (currentPlatform.value === 'wechat') {
        // 微信达人特有筛选条件
        if (searchForm.地区) {
          params.筛选条件.地区 = searchForm.地区
        }
        if (searchForm.性别) {
          params.筛选条件.性别 = searchForm.性别
        }
      }
    }

    // 根据平台调用不同的API
    let response
    if (currentPlatform.value === 'douyin') {
      response = await talentService.getTalentPool(params)
    } else if (currentPlatform.value === 'wechat') {
      response = await talentService.getWechatTalentPool(params)
    } else {
      throw new Error('不支持的平台类型')
    }
    
    if (response.status === 100 && response.data) {
      const data = response.data
      
      // 处理返回数据
      if (append) {
        talents.value.push(...(data.达人列表 || []))
      } else {
        talents.value = data.达人列表 || []
        pagination.页码 = 1
      }
      
      // 更新分页信息
      pagination.最后ID = data.下一页最后ID || 0
      totalCount.value = data.总数 || talents.value.length
      hasMore.value = (data.达人列表?.length || 0) >= pagination.每页数量
      
      // 记录成功日志
      console.log('达人列表加载成功:', {
        append,
        count: data.达人列表?.length || 0,
        total: totalCount.value,
        hasMore: hasMore.value
      })
      
    } else {
      throw new Error(response.message || '加载达人列表失败')
    }
    
  } catch (error) {
    console.error('加载达人列表失败:', error)

    // 针对不同类型的错误提供不同的用户提示
    let errorMessage = '加载达人列表失败，请重试'

    if (error.message) {
      if (error.message.includes('第三方搜索')) {
        errorMessage = '第三方搜索服务暂时不可用，已为您展示本地数据'
      } else if (error.message.includes('超时')) {
        errorMessage = '搜索超时，请检查网络连接后重试'
      } else {
        errorMessage = error.message
      }
    }

    message.error(errorMessage)

    // 加载失败时的处理
    if (!append) {
      talents.value = []
      totalCount.value = 0
      hasMore.value = false
    }
  } finally {
    loading.value = false

    // 数据加载完成后重新设置自动加载监听
    nextTick(() => {
      setupAutoLoad()
    })
  }
}

/**
 * 加载更多数据
 */
const loadMore = async () => {
  if (!hasMore.value || loading.value || isAutoLoading.value) return

  isAutoLoading.value = true
  pagination.页码++
  await loadTalentList(true)
  isAutoLoading.value = false
}

/**
 * 刷新达人列表
 * 重新加载第一页数据，用于认领操作后更新显示
 */
const refreshTalentList = async () => {
  // 重置分页状态
  pagination.页码 = 1
  pagination.最后ID = 0
  
  // 重新加载第一页数据
  await loadTalentList(false)
}

/**
 * 设置自动加载监听
 */
const setupAutoLoad = () => {
  // 先清理之前的Observer
  if (intersectionObserver) {
    intersectionObserver.disconnect()
    intersectionObserver = null
  }

  nextTick(() => {
    if (!loadTrigger.value) return

    // 创建 Intersection Observer
    intersectionObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && hasMore.value && !loading.value && !isAutoLoading.value) {
            loadMore()
          }
        })
      },
      {
        root: null,
        rootMargin: '100px', // 提前100px触发
        threshold: 0.1
      }
    )

    // 开始观察
    intersectionObserver.observe(loadTrigger.value)
  })
}

/**
 * 刷新列表 - 重新开始搜索
 */
const refreshList = async () => {
  // 先清空现有数据
  talents.value = []

  // 清除选中状态
  selectedTalent.value = null
  showDetailDrawer.value = false

  // 重置分页状态，重新开始搜索
  pagination.页码 = 1
  pagination.最后ID = 0
  hasMore.value = true

  // 重新加载数据（不追加，从头开始）
  await loadTalentList(false)
}

/**
 * 事件处理方法
 */

/**
 * 处理搜索 - 现在只处理筛选条件，关键词搜索通过弹窗处理
 */
const handleSearch = async () => {
  console.log('执行筛选搜索:', searchForm)
  await refreshList()
}

/**
 * 处理搜索输入 - 防抖处理
 */
const handleSearchInput = (value) => {
  // 清除之前的定时器
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }

  // 如果输入为空，清空建议列表
  if (!value || value.trim().length === 0) {
    searchSuggestions.value = []
    return
  }

  // 设置防抖延迟
  searchTimeout = setTimeout(async () => {
    await performSearch(value.trim())
  }, 300)
}

/**
 * 执行搜索请求
 */
const performSearch = async (keyword) => {
  if (!keyword) return

  searchLoading.value = true
  console.log('🔍 执行达人搜索:', keyword)

  try {
    // 调用后端搜索达人接口
    const response = await talentService.searchTalentPool({
      抖音号: keyword
    })

    if (response.status === 100) {
      const data = response.data
      const talentList = data.达人列表 || []

      // 转换为下拉选项格式
      searchSuggestions.value = talentList.map(talent => ({
        value: talent.uid_number,
        label: talent.nickname || '未知昵称',
        avatar: talent.avatar,
        nickname: talent.nickname,
        uid_number: talent.uid_number,
        follower_count: talent.follower_count
      }))

      console.log('🎯 搜索结果:', searchSuggestions.value.length, '个达人')
    } else {
      searchSuggestions.value = []
      console.log('❌ 搜索失败:', response.message)
    }
  } catch (error) {
    console.error('搜索达人失败:', error)
    searchSuggestions.value = []
  } finally {
    searchLoading.value = false
  }
}

/**
 * 处理选择搜索建议
 */
const handleSelectSuggestion = (value, option) => {
  console.log('🎯 选择达人:', value, option)
  
  // 标记正在从建议中选择
  isSelectingFromSuggestions.value = true
  
  // 使用uid进行精准查询，清空关键词避免模糊查询
  searchForm.uid = value  // 新增：使用uid进行精准查询
  searchForm.关键词 = null  // 清空关键词避免模糊查询
  
  // 清空搜索关键词和建议列表
  searchKeyword.value = ''
  searchSuggestions.value = []
  
  // 刷新列表以应用筛选
  refreshList()
  
  message.success(`已筛选显示达人: ${option.label}`)
  
  // 延迟重置选择状态
  setTimeout(() => {
    isSelectingFromSuggestions.value = false
  }, 100)
}

/**
 * 处理清空搜索
 */
const handleClearSearch = () => {
  searchKeyword.value = ''
  searchSuggestions.value = []
  
  // 清空搜索关键词筛选
  if (searchForm.关键词) {
    searchForm.关键词 = ''
    refreshList()
  }
}

/**
 * 智能搜索逻辑：根据当前状态决定搜索方式
 */
const handleSmartSearch = async () => {
  const keyword = searchKeyword.value.trim()
  if (!keyword) {
    message.warning('请输入搜索关键词')
    return
  }

  searchLoading.value = true
  console.log('🔍 开始智能搜索:', keyword)

  try {
    // 检查是否已经有完全匹配的达人建议（精确匹配昵称或UID）
    const exactMatch = searchSuggestions.value.find(suggestion => 
      suggestion.nickname === keyword ||
      suggestion.uid_number === keyword ||
      suggestion.label === keyword
    )

    if (exactMatch) {
      // 如果找到精确匹配的建议，直接通过uid_number搜索我们的达人库
      console.log('🎯 使用已获取的达人UID进行搜索:', exactMatch.uid_number)
      
      searchForm.关键词 = exactMatch.uid_number
      searchKeyword.value = ''
      searchSuggestions.value = []
      
      await refreshList()
      message.success(`已筛选显示达人: ${exactMatch.nickname}`)
      return
    }

    // 如果没有精确匹配的建议，调用第三方接口搜索
    console.log('🔍 调用第三方接口搜索达人:', keyword)
    
    // 调用第三方接口搜索
    const response = await talentService.searchTalentPool({
      抖音号: keyword
    })

    if (response.status === 100) {
      const data = response.data
      const talentList = data.达人列表 || []

      if (talentList.length > 0) {
        // 如果只有一个结果，直接选择并筛选
        if (talentList.length === 1) {
          const talent = talentList[0]
          // 使用uid进行精准查询，清空关键词避免模糊查询
          searchForm.uid = talent.uid_number
          searchForm.关键词 = null
          searchKeyword.value = ''
          searchSuggestions.value = []
          
          await refreshList()
          message.success(`已筛选显示达人: ${talent.nickname}`)
        } else {
          // 多个结果，更新搜索建议并提示用户选择
          searchSuggestions.value = talentList.map(talent => ({
            value: talent.uid_number,
            label: talent.nickname || '未知昵称',
            avatar: talent.avatar,
            nickname: talent.nickname,
            uid_number: talent.uid_number,
            follower_count: talent.follower_count
          }))

          message.info(`找到 ${talentList.length} 个匹配的达人，建议列表已更新，请选择或继续输入筛选`)
        }
      } else {
        searchSuggestions.value = []
        message.info('未找到匹配的达人')
      }
    } else {
      searchSuggestions.value = []
      message.error(response.message || '搜索失败')
    }
  } catch (error) {
    console.error('搜索失败:', error)
    searchSuggestions.value = []
    message.error('搜索失败，请重试')
  } finally {
    searchLoading.value = false
  }
}

/**
 * 处理回车搜索
 */
const handleEnterSearch = (e) => {
  // 如果正在从建议中选择，不触发搜索
  if (isSelectingFromSuggestions.value) {
    return
  }
  
  // 如果有建议列表，让a-auto-complete组件自然处理回车键
  if (searchSuggestions.value.length > 0) {
    // 不阻止默认行为，让组件处理建议选择
    return
  }
  
  // 如果没有建议列表，阻止默认行为并触发搜索
  e.preventDefault()
  e.stopPropagation()
  handleSmartSearch()
}

/**
 * 处理按钮搜索
 */
const handleButtonSearch = () => {
  handleSmartSearch()
}

/**
 * 处理下拉框显示状态变化
 */
const handleDropdownVisibleChange = (visible) => {
  console.log('🔽 下拉框状态变化:', visible)
  // 当下拉框关闭时，重置选择状态
  if (!visible) {
    setTimeout(() => {
      isSelectingFromSuggestions.value = false
    }, 100)
  }
}



/**
 * 处理重置筛选
 */
const handleReset = async () => {
  // 重置所有筛选条件
  searchForm.关键词 = ''
  searchForm.有联系方式 = undefined
  searchForm.uid = null  // 新增：重置uid参数
  searchForm.地区 = null
  searchForm.性别 = null
  // 移除 fansRange 重置，粉丝数范围筛选移至高级筛选
  advancedFilters.value = {}
  
  // 重置搜索相关状态
  searchKeyword.value = ''
  searchSuggestions.value = []
  
  console.log('重置筛选条件')
  await refreshList()
}

/**
 * 粉丝数范围变化处理方法已移除，粉丝数范围筛选移至高级筛选中
 */

/**
 * 平台切换相关方法
 */

/**
 * 处理平台切换
 * @param {Object} platformInfo - 平台信息
 */
const handlePlatformChange = async (platformInfo) => {
  console.log('🔄 平台切换:', platformInfo)

  // 重置搜索表单
  searchForm.关键词 = ''
  searchForm.有联系方式 = undefined
  searchForm.uid = null  // 新增：重置uid参数
  searchForm.地区 = null
  searchForm.性别 = null

  // 重置其他筛选条件
  // 移除 fansRange 重置，粉丝数范围筛选移至高级筛选
  advancedFilters.value = {}

  // 清除选中状态
  selectedTalent.value = null

  // 重新加载数据
  await refreshList()

  // 重新设置自动加载监听
  nextTick(() => {
    setupAutoLoad()
  })
}

/**
 * 处理高级筛选
 */
const handleAdvancedFilter = () => {
  showAdvancedFilter.value = true
}

/**
 * 应用高级筛选
 */
const handleAdvancedFilterApply = (filters) => {
  advancedFilters.value = filters
  showAdvancedFilter.value = false
  console.log('应用高级筛选:', filters)
  handleSearch()
}

/**
 * 重置高级筛选
 */
const handleAdvancedFilterReset = () => {
  advancedFilters.value = {}
  showAdvancedFilter.value = false
  console.log('重置高级筛选')
  handleSearch()
}

/**
 * 达人操作相关方法
 */

/**
 * 选择达人（显示详情）
 * @param {Object} talent - 达人对象
 */
const selectTalent = async (talent) => {
  selectedTalent.value = talent
  showDetailDrawer.value = true

  // 加载完整的达人详情
  await loadTalentDetail(talent.id)
}

/**
 * 加载达人详情
 * @param {number} talentId - 达人id
 */
const loadTalentDetail = async (talentId) => {
  if (!talentId) return
  
  try {
    detailLoading.value = true
    
    // 根据平台调用不同的详情API
    let response
    if (currentPlatform.value === 'douyin') {
      response = await talentService.getTalentDetail(talentId)
    } else if (currentPlatform.value === 'wechat') {
      response = await talentService.getWechatTalentDetail(talentId)
    } else {
      throw new Error('不支持的平台类型')
    }
    
    if (response.status === 100 && response.data) {
      // 更新选中达人的详细信息
      selectedTalent.value = {
        ...selectedTalent.value,
        ...response.data
      }
      
      console.log('达人详情加载成功:', response.data)
    } else {
      throw new Error(response.message || '加载达人详情失败')
    }
    
  } catch (error) {
    console.error('加载达人详情失败:', error)
    message.error('加载达人详情失败')
  } finally {
    detailLoading.value = false
  }
}

/**
 * 清除选择
 */
const clearSelection = () => {
  selectedTalent.value = null
  showDetailDrawer.value = false
}

/**
 * 处理认领达人
 * @param {Object} talent - 达人对象
 */
const handleClaimTalent = async (talent) => {
  if (!talent || claimingIds.value.has(talent.id)) return
  
  try {
    claimingIds.value.add(talent.id)
    
    // 根据平台调用不同的认领API
    let response
    if (currentPlatform.value === 'douyin') {
      response = await talentService.claimTalent(talent.id)
    } else if (currentPlatform.value === 'wechat') {
      response = await talentService.claimWechatTalent(talent.id)
    } else {
      throw new Error('不支持的平台类型')
    }
    
    if (response.status === 100) {
      message.success('认领成功')
      
      // 更新达人状态
      updateTalentStatus(talent.id, { 已认领: true, 当前用户认领状态: { 已认领: true } })
      
      console.log('认领达人成功:', talent.id)
    } else {
      throw new Error(response.message || '认领失败')
    }
    
  } catch (error) {
    console.error('认领达人失败:', error)
    message.error(error.message || '认领失败，请重试')
  } finally {
    claimingIds.value.delete(talent.id)
  }
}

/**
 * 处理达人数据更新
 * 当达人详情页面触发数据更新时，重新加载达人详情
 * @param {Object} talent - 达人对象
 */
const handleTalentUpdate = async (talent) => {
  if (!talent) return
  
  try {
    console.log('🔄 处理达人数据更新事件:', talent.id || talent.达人id)
    
    // 重新加载达人详情
    if (selectedTalent.value && (selectedTalent.value.id === talent.id || selectedTalent.value.达人id === talent.达人id)) {
      await loadTalentDetail(selectedTalent.value.id || selectedTalent.value.达人id)
    }
    
    // 重新加载达人列表以显示最新数据
    await loadTalentList()
    
  } catch (error) {
    console.error('处理达人数据更新失败:', error)
    // 这里不显示错误消息，因为更新操作本身已经有成功/失败提示
  }
}

/**
 * 处理取消认领达人
 * @param {Object} talent - 达人对象
 */
const handleUnclaimTalent = async (talent) => {
  if (!talent || claimingIds.value.has(talent.id)) return
  
  try {
    claimingIds.value.add(talent.id)
    
    // 根据平台调用不同的取消认领API
    let response
    if (currentPlatform.value === 'douyin') {
      response = await talentService.unclaimTalent(talent.id)
    } else if (currentPlatform.value === 'wechat') {
      response = await talentService.unclaimWechatTalent(talent.id)
    } else {
      throw new Error('不支持的平台类型')
    }
    
    if (response.status === 100) {
      message.success('取消认领成功')
      
      // 更新达人状态
      updateTalentStatus(talent.id, { 已认领: false, 当前用户认领状态: { 已认领: false } })
      
      console.log('取消认领达人成功:', talent.id)
    } else {
      throw new Error(response.message || '取消认领失败')
    }
    
  } catch (error) {
    console.error('取消认领达人失败:', error)
    message.error(error.message || '取消认领失败，请重试')
  } finally {
    claimingIds.value.delete(talent.id)
  }
}

/**
 * 处理分享达人
 * @param {Object} talent - 达人对象
 */
const handleShareTalent = async (talent) => {
  const shareText = `达人推荐：${talent.nickname || talent.昵称}\n抖音号：${talent.account_douyin}\n粉丝数：${formatNumber(talent.粉丝数)}`
  
  try {
    await navigator.clipboard.writeText(shareText)
    message.success('达人信息已复制到剪贴板')
    console.log('分享达人:', talent.id)
  } catch (error) {
    console.error('复制分享内容失败:', error)
    message.error('复制失败，请手动复制')
  }
}



/**
 * 更新达人状态
 * @param {number} talentId - 达人id
 * @param {Object} updates - 更新的字段
 */
const updateTalentStatus = (talentId, updates) => {
  // 更新列表中的达人状态
  const index = talents.value.findIndex(t => t.id === talentId)
  if (index !== -1) {
    Object.assign(talents.value[index], updates)
  }
  
  // 更新选中达人的状态
  if (selectedTalent.value?.id === talentId) {
    Object.assign(selectedTalent.value, updates)
  }
}

/**
 * 生命周期钩子
 */
onMounted(() => {
  // 页面加载时获取达人列表
  loadTalentList()

  // 延迟设置自动加载监听，确保DOM完全渲染
  setTimeout(() => {
    setupAutoLoad()
  }, 1000)
})

onUnmounted(() => {
  // 清理 Intersection Observer
  if (intersectionObserver) {
    intersectionObserver.disconnect()
    intersectionObserver = null
  }
  
  // 清理搜索防抖定时器
  if (searchTimeout) {
    clearTimeout(searchTimeout)
    searchTimeout = null
  }
})

// 监听视图模式变化
watch(viewMode, (newMode) => {
  console.log('视图模式切换:', newMode)
  // 切换视图模式时，可以进行一些优化操作
  nextTick(() => {
    // 确保DOM更新后执行相关操作
  })
})
</script>

<style scoped>
/**
 * 达人池页面样式
 * 采用现代化的布局和交互设计
 */

/* 页面整体布局 */
.talent-pool-page {
  padding: 0;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 平台切换器样式 */
.platform-switcher-section {
  margin-bottom: 16px;
  background: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 搜索区域样式 */
.search-section {
  margin-bottom: 16px;
}

.search-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 粉丝数范围容器样式已移除，粉丝数范围筛选移至高级筛选中 */

/* 内容区域样式 */
.content-section {
  min-height: calc(100vh - 200px);
}

/* 列表卡片样式 */
.talent-list-card {
  min-height: 600px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.list-header {
  display: flex;
  align-items: center;
  gap: 16px;
}

.title {
  font-size: 18px;
  font-weight: 600;
}

.stats-info {
  display: flex;
  gap: 12px;
  margin-left: auto;
}

.stat-item {
  font-size: 13px;
  color: #666;
}

/* 卡片视图样式 */
.card-view {
  min-height: 400px;
}

/* 微信达人视图样式 */
.wechat-talent-view {
  min-height: 400px;
  padding: 8px 0;
}

.talent-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  padding: 8px 0;
}

.talent-card-item {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
}

.talent-card-item:hover {
  transform: translateY(-2px);
}

.talent-card-item.selected {
  box-shadow: 0 4px 20px rgba(24, 144, 255, 0.2);
  border: 2px solid #1890ff;
}

.talent-card-item.claimed {
  opacity: 0.8;
}

.talent-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.talent-avatar {
  flex-shrink: 0;
}

.talent-basic-info {
  flex: 1;
  min-width: 0;
}

.talent-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.talent-douyin {
  margin: 0;
  font-size: 13px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 12px;
  padding: 12px;
  background: #fafafa;
  border-radius: 8px;
}

.card-stats .stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.stat-value {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
}

.card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 12px;
  min-height: 24px;
}

.card-actions {
  display: flex;
  justify-content: center;
}

/* 表格视图样式 */
.table-view {
  min-height: 200px;
}

.table-basic-info {
  line-height: 1.4;
}

.table-name {
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
  font-size: 14px;
}

.table-account {
  font-size: 12px;
  color: #666;
  opacity: 0.8;
}

.fans-count {
  font-weight: 500;
  color: #1890ff;
}

.gmv-text {
  font-weight: 500;
  color: #52c41a;
}

.status-tags {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
}

.text-gray {
  color: #999;
  font-size: 12px;
}

/* 表格行样式 */
:deep(.ant-table-tbody tr.selected-row) {
  background-color: #e6f7ff !important;
}

:deep(.ant-table-tbody tr.claimed-row) {
  background-color: #fff2e8 !important;
}

:deep(.ant-table-tbody tr:hover) {
  background-color: #f5f5f5 !important;
}

/* 抽屉样式优化 */
:deep(.ant-drawer-body) {
  padding: 16px;
}

:deep(.ant-drawer-header) {
  border-bottom: 1px solid #f0f0f0;
}

/* 加载更多区域 */
.load-more-section {
  margin-top: 24px;
  text-align: center;
}

/* 空状态样式 */
.empty-state {
  padding: 40px 20px;
  text-align: center;
}

/* 联系方式显示样式 - 优化设计 */
.contact-display-wrapper {
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  padding: 0 4px; /* 添加左右内边距，更好利用空间 */
}

.contact-item-compact {
  display: flex;
  align-items: center;
  gap: 6px; /* 增加间距，利用更宽的空间 */
  padding: 6px 8px; /* 增加内边距 */
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  transition: all 0.2s ease;
  position: relative;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.contact-item-compact:hover {
  background: #f0f9ff;
  border-color: #d1ecf1;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 联系方式图标 */
.contact-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px; /* 增加图标容器尺寸 */
  height: 18px;
  flex-shrink: 0;
}

.contact-icon {
  font-size: 14px; /* 增加图标大小 */
  color: #666;
  transition: color 0.2s ease;
}

.contact-icon-微信 {
  color: #07c160;
}

.contact-icon-手机,
.contact-icon-phone,
.contact-icon-电话 {
  color: #1890ff;
}

.contact-icon-邮箱 {
  color: #fa8c16;
}

/* 联系方式内容区域 */
.contact-content {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.contact-value {
  font-size: 13px; /* 增加字体大小 */
  font-weight: 500;
  color: #262626;
  cursor: pointer;
  transition: color 0.2s ease;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.3; /* 调整行高 */
}

.contact-value:hover {
  color: #1890ff;
}

/* 复制按钮 */
.contact-copy-btn {
  display: flex;
  align-items: center;
  opacity: 0;
  transition: opacity 0.2s ease;
  flex-shrink: 0;
}

.contact-item-compact:hover .contact-copy-btn {
  opacity: 1;
}

.copy-btn-mini {
  width: 18px; /* 增加按钮尺寸 */
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  color: #8c8c8c;
  transition: all 0.2s ease;
  padding: 0;
  min-width: 18px;
}

.copy-btn-mini:hover {
  background: #e6f7ff;
  color: #1890ff;
}

.copy-btn-mini .anticon {
  font-size: 12px; /* 增加图标大小 */
}

/* 联系方式数量标识 */
.contact-count-mini {
  position: absolute;
  top: -3px;
  right: -3px;
  background: #1890ff;
  color: white;
  border-radius: 6px;
  min-width: 12px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8px;
  font-weight: 500;
  line-height: 1;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.count-mini {
  padding: 0 1px;
}

/* 无联系方式状态 */
.no-contact-compact {
  display: flex;
  align-items: center;
  gap: 6px; /* 增加间距 */
  padding: 6px 8px; /* 增加内边距 */
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  color: #bfbfbf;
  width: 100%;
  box-sizing: border-box;
  justify-content: center;
}

.no-contact-icon {
  font-size: 12px; /* 增加图标大小 */
  color: #d9d9d9;
}

.no-contact-text {
  font-size: 12px; /* 增加字体大小 */
  color: #bfbfbf;
}

/* 动画效果 */
@keyframes contactItemFadeIn {
  from {
    opacity: 0;
    transform: translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.contact-item-compact {
  animation: contactItemFadeIn 0.3s ease-out;
}

/* 复制按钮点击效果 */
@keyframes copySuccess {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.15);
  }
  100% {
    transform: scale(1);
  }
}

.copy-btn-mini.copying {
  animation: copySuccess 0.3s ease-out;
}

/* 联系方式类型图标的微动画 */
.contact-icon {
  transition: all 0.2s ease;
}

.contact-item-compact:hover .contact-icon {
  transform: scale(1.05);
}

/* 数量标识的脉冲效果 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 3px rgba(24, 144, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
  }
}

.contact-count-mini {
  animation: pulse 2s infinite;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .talent-cards-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  }

  /* 中等屏幕下的联系方式优化 */
  .contact-display-wrapper {
    width: 100%;
    max-width: 100%;
  }

  .contact-item-compact {
    padding: 3px 5px;
    gap: 3px;
  }

  .contact-value {
    font-size: 11px;
  }

  .contact-icon-wrapper {
    width: 14px;
    height: 14px;
  }

  .contact-icon {
    font-size: 11px;
  }
}

@media (max-width: 768px) {
  .talent-pool-page {
    padding: 8px;
  }

  .search-section {
    margin-bottom: 12px;
  }

  /* 小屏幕下的联系方式优化 */
  .contact-display-wrapper {
    width: 100%;
    max-width: 100%;
  }

  .contact-item-compact {
    padding: 2px 4px;
    gap: 2px;
  }

  .contact-icon-wrapper {
    width: 12px;
    height: 12px;
  }

  .contact-icon {
    font-size: 10px;
  }

  .contact-value {
    font-size: 10px;
  }

  .contact-count-mini {
    width: 10px;
    height: 10px;
    font-size: 7px;
    top: -2px;
    right: -2px;
  }

  .copy-btn-mini {
    width: 14px;
    height: 14px;
  }

  .copy-btn-mini .anticon {
    font-size: 8px;
  }

  .no-contact-compact {
    padding: 2px 4px;
    gap: 2px;
  }

  .no-contact-text {
    font-size: 10px;
  }

  .no-contact-icon {
    font-size: 9px;
  }
}
  
  .talent-cards-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .card-stats {
    grid-template-columns: repeat(2, 1fr);
  }

/* 加载状态样式 */
.talent-list-content .ant-spin-nested-loading {
  min-height: 200px;
}

/* 自动加载样式 */
.auto-load-section {
  padding: 16px 0;
  text-align: center;
  min-height: 60px;
}

.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 14px;
  padding: 20px 0;
}

.load-trigger {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 10px 0;
}

.trigger-content {
  padding: 10px 20px;
  border-radius: 4px;
  background: #fafafa;
  border: 1px dashed #d9d9d9;
}

.load-complete {
  padding: 16px 0;
}

/* 表头样式优化 */
:deep(.ant-table-thead > tr > th) {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  padding: 8px 12px;
}

/* 确保表头文字不换行 */
:deep(.ant-table-thead .ant-table-cell) {
  white-space: nowrap !important;
  word-break: keep-all !important;
}

/* 搜索弹窗样式 */
.search-talent-form {
  .search-section {
    margin-bottom: 24px;
  }

  .search-input-wrapper {
    .input-label {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .label-text {
        font-size: 14px;
        font-weight: 500;
        color: #262626;
      }

      .required-mark {
        color: #ff4d4f;
        margin-left: 4px;
        font-size: 14px;
      }
    }
  }

  .search-tip {
    display: flex;
    align-items: flex-start;
    margin-top: 12px;
    padding: 12px 16px;
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
    border-radius: 6px;

    .tip-icon {
      color: #52c41a;
      margin-right: 8px;
      margin-top: 2px;
      flex-shrink: 0;
    }

    .tip-text {
      color: #389e0d;
      font-size: 13px;
      line-height: 1.4;
    }
  }

  .search-results {
    max-height: 400px;
    overflow-y: auto;
    margin-top: 16px;
  }

  .results-list {
    .talent-item {
      display: flex;
      align-items: center;
      padding: 16px;
      border: 2px solid #f0f0f0;
      border-radius: 12px;
      margin-bottom: 12px;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      background: #fafafa;

      &:hover {
        border-color: #40a9ff;
        background-color: #f6ffed;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
      }

      &.selected {
        border-color: #1890ff;
        background-color: #e6f7ff;
        box-shadow: 0 4px 16px rgba(24, 144, 255, 0.2);
        transform: translateY(-1px);
      }

      .talent-avatar {
        margin-right: 16px;

        .ant-avatar {
          border: 2px solid #fff;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }

      .talent-info {
        flex: 1;

        .talent-name {
          font-size: 16px;
          font-weight: 600;
          color: #262626;
          margin-bottom: 6px;
          line-height: 1.4;
        }

        .talent-account {
          font-size: 13px;
          color: #8c8c8c;
          margin-bottom: 8px;
          font-family: 'Monaco', 'Menlo', monospace;
        }

        .talent-stats {
          display: flex;
          align-items: center;
          gap: 16px;

          .fans-count {
            font-size: 13px;
            color: #595959;
            background: #f5f5f5;
            padding: 2px 8px;
            border-radius: 12px;
            font-weight: 500;
          }

          .exists-tag {
            font-size: 12px;
            color: #52c41a;
            display: flex;
            align-items: center;
            background: #f6ffed;
            padding: 3px 8px;
            border-radius: 12px;
            border: 1px solid #b7eb8f;
            font-weight: 500;
          }
        }
      }
    }
  }

  .no-results {
    text-align: center;
    padding: 48px 24px;
    background: #fafafa;
    border-radius: 12px;
    margin-top: 16px;

    :deep(.ant-empty-description) {
      color: #8c8c8c;
      font-size: 14px;
    }
  }
}

/* 搜索建议样式 */
.search-suggestion-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  gap: 12px;
  
  .suggestion-avatar {
    flex-shrink: 0;
  }
  
  .suggestion-info {
    flex: 1;
    min-width: 0;
    
    .suggestion-name {
      font-size: 14px;
      font-weight: 500;
      color: #1f2937;
      margin-bottom: 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .suggestion-stats {
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 12px;
      color: #6b7280;
      
      .fans-count {
        color: #3b82f6;
      }
      
      .uid-info {
        color: #9ca3af;
      }
    }
  }
}

/* 搜索建议下拉框样式优化 */
:deep(.ant-select-dropdown) {
  .ant-select-item {
    padding: 8px 12px;
    
    &:hover {
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    }
    
    &.ant-select-item-option-selected {
      background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    }
  }
}

/* 搜索输入框和按钮布局 */
.search-input-wrapper {
  display: flex;
  width: 100%;
  
  .search-autocomplete {
    flex: 1;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    
    :deep(.ant-select-selector) {
      border-right: none;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
    
    :deep(.ant-select-focused) {
      .ant-select-selector {
        border-right: none;
        box-shadow: none;
      }
    }
  }
  
  .search-button {
    flex-shrink: 0;
    width: 40px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left: none;
    
    &:hover {
      border-left: none;
    }
    
    &:focus {
      border-left: none;
    }
  }
}


</style>