#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复达人头像URL脚本
用于查找 kol.达人表 中包含 "x-signature" 的头像URL并进行转换

功能说明：
1. 查找数据库中包含 "x-signature" 的头像URL
2. 从原始URL中提取文件名
3. 生成新的标准化URL格式
4. 批量更新数据库记录
5. 验证修复结果

原始URL格式：
https://p3-sign.douyinpic.com/aweme/200x200/aweme-avatar/tos-cn-avt-0015_31ce0474badfc342a55cc018bb70aa28.jpeg?x-expires=1703530800u0026x-signature=GYDw9R3tw56h%2BihcpfWWTzEKzNI%3Du0026from=**********

目标URL格式：
https://p3.douyinpic.com/aweme/100x100/aweme-avatar/tos-cn-avt-0015_31ce0474badfc342a55cc018bb70aa28.jpeg

使用方法：
1. 在后端项目根目录下运行: python 手动执行/修复达人头像URL.py
2. 查看找到的需要修复的记录数量
3. 输入 'yes' 确认执行修复，或其他任意键取消
4. 等待修复完成并查看结果统计

注意事项：
- 修复前会显示前5条记录作为预览
- 需要手动确认才会执行实际的数据库更新操作
- 修复完成后会自动验证剩余未修复的记录数量
- 直接对接后端异步数据库，无测试数据

作者: CRM系统开发团队
创建时间: 2025-01-07
"""

import asyncio
import os
import sys
from typing import Dict, Optional

# 设置项目路径
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(script_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入项目模块
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例  # noqa: E402
from 日志 import 应用日志器, 错误日志器  # noqa: E402


class 达人头像URL修复器:
    """达人头像URL修复工具类"""
    
    def __init__(self):
        self.需要修复的记录 = []
        self.修复成功数量 = 0
        self.修复失败数量 = 0
        
    def 转换URL(self, url: str) -> Optional[str]:
        """
        转换包含x-signature的URL为标准格式

        转换规则：
        1. 去掉 ? 及其后面的所有参数
        2. 将 p3-sign 替换为 p3

        示例：
        https://p3-sign.douyinpic.com/aweme/200x200/c16000003f97583dac4.jpeg?x-expires=1703538000u0026x-signature=26wD%2FYoyWeKNE8zqVI9oFcmXo98%3Du0026from=**********
        -> https://p3.douyinpic.com/aweme/200x200/c16000003f97583dac4.jpeg

        https://p11-sign.douyinpic.com/aweme/200x200/aweme-avatar/tos-cn-i-0813_aa70a8a4e007418cb3b90fab20baaf50.jpeg?x-expires=1703383200u0026x-signature=kLVTAL0Tu4QLfQ8OtfglxhUMy8Y%3Du0026
        -> https://p11.douyinpic.com/aweme/200x200/aweme-avatar/tos-cn-i-0813_aa70a8a4e007418cb3b90fab20baaf50.jpeg

        参数:
            url: 原始URL

        返回:
            转换后的URL，如果转换失败返回None
        """
        try:
            # 1. 去掉 ? 及其后面的所有参数
            clean_url = url.split('?')[0]

            # 2. 去掉 -sign
            clean_url = clean_url.replace('-sign', '')

            return clean_url

        except Exception as e:
            print(f"❌ 转换URL时发生异常: {str(e)}, URL: {url}")
            return None
    



    
    async def 分批处理修复(self) -> bool:
        """
        分批处理修复记录，一批一批处理，不全部加载到内存

        返回:
            是否成功完成所有修复
        """
        try:
            print("� 开始分批处理头像URL修复...")

            # 分批查询和处理，使用基于ID的分页（效率更高）
            批次大小 = 1000
            最后处理的ID = 0
            批次计数 = 0
            总修复数量 = 0
            总成功数量 = 0

            while True:
                批次计数 += 1
                print(f"\n🔄 正在处理第 {批次计数} 批数据 (从ID {最后处理的ID + 1} 开始)...")

                查询SQL = f"""
                    SELECT id, avatar, 昵称, account_douyin
                    FROM kol.达人表
                    WHERE avatar IS NOT NULL
                    AND avatar LIKE '%sign%'
                    AND id > {最后处理的ID}
                    ORDER BY id
                    LIMIT {批次大小}
                """

                批次记录 = await 异步连接池实例.执行查询(查询SQL)

                if not 批次记录:
                    print("✅ 所有数据处理完成")
                    break

                print(f"   📝 本批次查询到 {len(批次记录)} 条记录")

                # 处理本批次的记录
                批次成功数量 = 0
                for 记录 in 批次记录:
                    总修复数量 += 1
                    原始URL = 记录['avatar']
                    转换后URL = self.转换URL(原始URL)

                    print(f"   🔧 处理ID {记录['id']}:")
                    print(f"      原始: {原始URL}")
                    print(f"      转换: {转换后URL}")

                    成功 = await self.修复单条记录(记录['id'], 原始URL)
                    if 成功:
                        批次成功数量 += 1
                        总成功数量 += 1
                        print("      ✅ 修复成功")
                    else:
                        print("      ❌ 修复失败")
                    print("")

                最后处理的ID = 批次记录[-1]['id']  # 更新为本批次最后一条记录的ID

                print(f"   ✅ 本批次修复成功 {批次成功数量}/{len(批次记录)} 条，最后ID: {最后处理的ID}")
                print(f"   📊 累计处理: {总修复数量} 条，成功: {总成功数量} 条")

                # 每批次之间稍作停顿，避免数据库压力过大
                await asyncio.sleep(0.1)

            print(f"\n🎉 修复完成！总共处理 {总修复数量} 条记录，成功修复 {总成功数量} 条")
            return True

        except Exception as e:
            print(f"❌ 分批处理时发生异常: {str(e)}")
            return False
    
    async def 修复单条记录(self, 达人id: int, 原始URL: str) -> bool:
        """
        修复单条记录的头像URL - 使用异步连接池实例

        参数:
            达人id: 达人的ID
            原始URL: 原始头像URL

        返回:
            修复是否成功
        """
        try:
            # 转换URL
            新URL = self.转换URL(原始URL)
            if not 新URL:
                print(f"❌ 达人id {达人id} 的头像URL无法转换，跳过修复")
                return False

            # 使用异步连接池实例更新数据库
            更新SQL = """
                UPDATE kol.达人表
                SET avatar = %s
                WHERE id = $1
            """

            # 执行更新操作
            影响行数 = await 异步连接池实例.执行更新(更新SQL, (新URL, 达人id))

            if 影响行数 > 0:
                return True
            else:
                print(f"❌ 达人id {达人id} 更新失败，可能记录不存在")
                return False

        except Exception as e:
            print(f"❌ 修复达人id {达人id} 时发生异常: {str(e)}")
            return False
    
    async def 循环执行修复(self, 确认修复: bool = False) -> Dict[str, int]:
        """
        循环执行单条记录修复 - 批量处理所有需要修复的记录

        参数:
            确认修复: 是否确认执行修复操作

        返回:
            修复结果统计
        """
        if not 确认修复:
            应用日志器.warning("未确认修复操作，仅进行预览模式")
            return {"预览记录数": len(self.需要修复的记录)}

        应用日志器.info(f"开始循环执行修复，共 {len(self.需要修复的记录)} 条记录...")

        成功数量 = 0
        失败数量 = 0

        # 循环处理每条记录
        for i, 记录 in enumerate(self.需要修复的记录, 1):
            达人id = 记录['id']
            原始URL = 记录['avatar']

            # 调用单条记录修复函数
            if await self.修复单条记录(达人id, 原始URL):
                成功数量 += 1
            else:
                失败数量 += 1

            # 每修复100条记录输出一次进度（减少日志输出）
            if i % 100 == 0:
                print(f"✅ 已处理 {i}/{len(self.需要修复的记录)} 条记录，成功: {成功数量}，失败: {失败数量}")

            # 每修复1000条记录暂停一下，避免数据库压力过大
            if i % 1000 == 0:
                await asyncio.sleep(0.1)

        # 保存统计结果
        self.修复成功数量 = 成功数量
        self.修复失败数量 = 失败数量

        print(f"🎉 循环修复完成！总计: {len(self.需要修复的记录)}, 成功: {成功数量}，失败: {失败数量}")

        return {
            "总记录数": len(self.需要修复的记录),
            "修复成功": 成功数量,
            "修复失败": 失败数量
        }
    
    async def 验证修复结果(self) -> Dict[str, int]:
        """
        验证修复结果
        
        返回:
            验证结果统计
        """
        try:
            应用日志器.info("开始验证修复结果...")
            
            # 查询仍然包含 x-signature 的记录
            验证SQL = """
                SELECT COUNT(*) as remaining_count
                FROM kol.达人表 
                WHERE avatar IS NOT NULL 
                AND avatar LIKE '%x-signature%'
            """
            
            结果 = await 异步连接池实例.执行查询(验证SQL)
            剩余数量 = 结果[0]['remaining_count'] if 结果 else 0
            
            应用日志器.info(f"验证完成，仍有 {剩余数量} 条记录包含 x-signature")
            
            return {"剩余未修复记录数": 剩余数量}
            
        except Exception as e:
            错误日志器.error(f"验证修复结果时发生异常: {str(e)}")
            return {"验证失败": True}


async def 测试单条修复():
    """测试单条记录修复功能"""
    try:
        应用日志器.info("=" * 60)
        应用日志器.info("测试单条记录修复功能")
        应用日志器.info("=" * 60)

        修复器 = 达人头像URL修复器()

        # 测试URL转换功能
        测试URLs = [
            "https://p3-sign.douyinpic.com/aweme/200x200/aweme-avatar/tos-cn-avt-0015_31ce0474badfc342a55cc018bb70aa28.jpeg?x-expires=1703530800u0026x-signature=GYDw9R3tw56h%2BihcpfWWTzEKzNI%3Du0026from=**********",
            "https://p3-sign.douyinpic.com/aweme/200x200/c16000003f97583dac4.jpeg?x-expires=1703538000u0026x-signature=26wD%2FYoyWeKNE8zqVI9oFcmXo98%3Du0026from=**********"
        ]

        print("🧪 测试URL转换功能:")
        for i, 测试URL in enumerate(测试URLs, 1):
            print(f"\n测试 {i}:")
            print(f"原始URL: {测试URL}")

            new_url = 修复器.转换URL(测试URL)
            if new_url:
                print(f"转换后: {new_url}")
            else:
                print("❌ 转换失败")

        # 查询一条实际记录进行测试
        查询SQL = """
            SELECT id, avatar, 昵称
            FROM kol.达人表
            WHERE avatar IS NOT NULL
            AND avatar LIKE '%x-signature%'
            LIMIT 1
        """

        记录列表 = await 异步连接池实例.执行查询(查询SQL)

        if 记录列表:
            记录 = 记录列表[0]
            print("\n找到测试记录:")
            print(f"ID: {记录['id']}")
            print(f"昵称: {记录.get('昵称', 'N/A')}")
            print(f"原始URL: {记录['avatar']}")

            # 询问是否执行修复
            用户输入 = input("\n是否修复这条记录？(输入 'yes' 确认): ").strip().lower()

            if 用户输入 == 'yes':
                成功 = await 修复器.修复单条记录(记录['id'], 记录['avatar'])
                if 成功:
                    print("✅ 修复成功！")
                else:
                    print("❌ 修复失败！")
            else:
                print("取消修复")
        else:
            print("没有找到包含 x-signature 的记录")

    except Exception as e:
        错误日志器.error(f"测试单条修复时发生异常: {str(e)}")
        raise


async def main():
    """主函数"""
    try:
        print("=" * 60)
        print("🚀 达人头像URL修复工具启动")
        print("=" * 60)

        修复器 = 达人头像URL修复器()

        # 直接开始分批处理修复
        成功 = await 修复器.分批处理修复()

        if 成功:
            print("✅ 批量修复任务完成")
        else:
            print("❌ 批量修复任务失败")
        
        应用日志器.info("达人头像URL修复工具执行完成")
        
    except KeyboardInterrupt:
        应用日志器.info("用户中断操作")
    except Exception as e:
        错误日志器.error(f"程序执行异常: {str(e)}")
        raise


if __name__ == "__main__":
    print("达人头像URL修复工具")
    print("1. 测试单条记录修复")
    print("2. 批量修复所有记录")

    选择 = input("请选择操作 (1 或 2): ").strip()

    if 选择 == "1":
        # 运行测试函数
        asyncio.run(测试单条修复())
    elif 选择 == "2":
        # 运行完整修复
        asyncio.run(main())
    else:
        print("无效选择，程序退出")
