"""
团队数据看板处理模块

此模块专门用于处理和格式化从数据库查询到的团队看板原始数据，
将数据转换为前端需要的格式，提供各种数据计算和处理功能。

创建时间: 2024
更新时间: 2024
作者: AI系统优化
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from 日志 import 数据库日志器, 错误日志器


class 团队数据处理工具:
    """
    团队数据处理工具类 - 第三阶段优化版本
    提供各种数据处理和格式化功能，消除冗余代码，确保数据去重和一致性
    """

    def __init__(self):
        """初始化团队数据处理工具"""
        self.缓存数据 = {}  # 用于缓存重复计算的数据
        self.处理计数器 = 0  # 用于统计处理次数

    def 团队数据去重处理(
        self,
        微信好友汇总: Dict[str, Any],
        寄样汇总: Dict[str, Any],
        成员列表: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """
        团队数据去重处理 - 确保团队汇总数据的准确性

        Args:
            微信好友汇总: 微信好友汇总数据
            寄样汇总: 寄样汇总数据
            成员列表: 成员列表数据

        Returns:
            去重处理后的汇总数据
        """
        try:
            # 1. 达人数量去重处理（避免重复统计同一达人）
            有联系方式达人数 = 0

            for 成员 in 成员列表:
                # 统计每个成员的达人数量，确保不重复计算
                成员达人数 = 成员.get("达人数量", 0) or 0
                if 成员达人数 > 0:
                    # 这里应该从数据库查询该成员的具体达人id列表进行去重
                    # 暂时使用成员达人数进行累加，后续可优化为精确去重
                    有联系方式达人数 += 成员达人数

            # 2. 微信账号去重处理（确保不重复统计同一微信账号）
            团队微信账号总数 = 微信好友汇总.get("微信账号数", 0) or 0
            团队好友总数 = 微信好友汇总.get("好友总数", 0) or 0

            # 3. 寄样数据去重处理（确保不重复统计同一样品申请）
            团队样品申请总数 = 寄样汇总.get("样品申请总数", 0) or 0
            团队实际发放数 = 寄样汇总.get("实际发放数", 0) or 0

            # 4. 构建去重后的汇总数据
            去重汇总数据 = {
                "达人管理汇总": {
                    "有联系方式达人数": 有联系方式达人数,
                    "团队认领达人总数": 有联系方式达人数,  # 与有联系方式达人数保持一致
                    "数据去重状态": "已处理",
                },
                "微信运营汇总": {
                    "团队微信账号总数": 团队微信账号总数,
                    "团队好友总数": 团队好友总数,
                    "平均好友数": round(团队好友总数 / max(团队微信账号总数, 1), 1),
                    "数据去重状态": "已处理",
                },
                "样品管理汇总": {
                    "团队样品申请总数": 团队样品申请总数,
                    "团队实际发放数": 团队实际发放数,
                    "样品发放率": round(
                        (团队实际发放数 / max(团队样品申请总数, 1)) * 100, 1
                    ),
                    "数据去重状态": "已处理",
                },
                "处理时间": datetime.now().isoformat(),
                "处理说明": "团队汇总数据已进行去重处理，确保数据准确性",
            }

            数据库日志器.info(
                f"团队数据去重处理完成：达人{有联系方式达人数}个，微信账号{团队微信账号总数}个，好友{团队好友总数}人"
            )
            return 去重汇总数据

        except Exception as e:
            错误日志器.error(f"团队数据去重处理失败: {str(e)}")
            return {
                "达人管理汇总": {"有联系方式达人数": 0, "数据去重状态": "处理失败"},
                "微信运营汇总": {
                    "团队微信账号总数": 0,
                    "团队好友总数": 0,
                    "数据去重状态": "处理失败",
                },
                "样品管理汇总": {
                    "团队样品申请总数": 0,
                    "团队实际发放数": 0,
                    "数据去重状态": "处理失败",
                },
                "处理时间": datetime.now().isoformat(),
                "错误信息": str(e),
            }

    def 统一数据格式化处理(
        self,
        原始数据: Dict[str, Any],
        数据类型: str,
        默认值配置: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        统一数据格式化处理 - 消除重复的数据处理逻辑

        Args:
            原始数据: 需要格式化的原始数据
            数据类型: 数据类型（微信、达人、邀约、样品等）
            默认值配置: 默认值配置字典

        Returns:
            格式化后的数据
        """
        try:
            if 默认值配置 is None:
                默认值配置 = self._获取默认值配置(数据类型)

            格式化数据 = {}

            # 统一处理数值字段
            for 字段名, 默认值 in 默认值配置.items():
                原始值 = 原始数据.get(字段名, 默认值)

                # 统一的数值处理逻辑
                if isinstance(默认值, int):
                    格式化数据[字段名] = int(原始值) if 原始值 is not None else 0
                elif isinstance(默认值, float):
                    格式化数据[字段名] = float(原始值) if 原始值 is not None else 0.0
                else:
                    格式化数据[字段名] = 原始值 if 原始值 is not None else 默认值

            # 添加处理时间戳
            格式化数据["数据处理时间"] = datetime.now().isoformat()
            格式化数据["数据类型"] = 数据类型

            return 格式化数据

        except Exception as e:
            错误日志器.error(f"统一数据格式化处理失败: 数据类型={数据类型}, 错误={e}")
            return 默认值配置 or {}

    def _获取默认值配置(self, 数据类型: str) -> Dict[str, Any]:
        """获取不同数据类型的默认值配置"""
        配置映射 = {
            "微信": {
                "微信账号数": 0,
                "好友总数": 0,
                "今日新增好友": 0,
                "平均好友数": 0.0,
                "好友增长率": 0.0,
            },
            "达人": {
                "达人数量": 0,
                "有联系方式达人数": 0,
                "认领达人数": 0,
                "今日新增达人": 0,
                "达人转化率": 0.0,
            },
            "邀约": {
                "邀约总数": 0,
                "成功邀约数": 0,
                "合作中数量": 0,
                "今日邀约数": 0,
                "邀约成功率": 0.0,
            },
            "样品": {
                "样品申请总数": 0,
                "实际发放数": 0,
                "今日申请数": 0,
                "样品发放率": 0.0,
                "样品完成率": 0.0,
            },
        }

        return 配置映射.get(数据类型, {})

    def 统一计算比率(self, 分子: int, 分母: int, 保留位数: int = 1) -> float:
        """
        统一计算比率 - 消除重复的比率计算逻辑

        Args:
            分子: 分子数值
            分母: 分母数值
            保留位数: 小数保留位数

        Returns:
            计算后的比率（百分比）
        """
        try:
            if 分母 <= 0:
                return 0.0

            比率 = (分子 / 分母) * 100
            return round(比率, 保留位数)

        except Exception as e:
            错误日志器.error(f"统一计算比率失败: 分子={分子}, 分母={分母}, 错误={e}")
            return 0.0

    def 数据一致性验证(
        self, 团队汇总数据: Dict[str, Any], 成员个人数据列表: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        数据一致性验证 - 确保团队汇总数据与成员个人数据汇总的一致性

        Args:
            团队汇总数据: 团队级别的汇总数据
            成员个人数据列表: 成员个人数据列表

        Returns:
            验证结果和差异报告
        """
        try:
            验证结果 = {
                "验证通过": True,
                "差异报告": [],
                "验证时间": datetime.now().isoformat(),
                "验证项目": [],
            }

            # 1. 验证微信数据一致性
            团队微信账号数 = 团队汇总数据.get("微信运营汇总", {}).get(
                "团队微信账号总数", 0
            )
            团队好友总数 = 团队汇总数据.get("微信运营汇总", {}).get("团队好友总数", 0)

            成员微信账号总数 = sum(成员.get("微信个数", 0) for 成员 in 成员个人数据列表)
            成员好友总数 = sum(成员.get("好友数量", 0) for 成员 in 成员个人数据列表)

            微信验证 = self._验证单项数据一致性(
                "微信账号数", 团队微信账号数, 成员微信账号总数
            )
            好友验证 = self._验证单项数据一致性("好友总数", 团队好友总数, 成员好友总数)

            验证结果["验证项目"].extend([微信验证, 好友验证])

            # 2. 验证达人数据一致性
            团队达人数 = 团队汇总数据.get("达人管理汇总", {}).get("有联系方式达人数", 0)
            成员达人总数 = sum(成员.get("达人数量", 0) for 成员 in 成员个人数据列表)

            达人验证 = self._验证单项数据一致性("达人数量", 团队达人数, 成员达人总数)
            验证结果["验证项目"].append(达人验证)

            # 3. 验证样品数据一致性
            团队样品数 = 团队汇总数据.get("样品管理汇总", {}).get("团队样品申请总数", 0)
            成员样品总数 = sum(成员.get("样品申请数", 0) for 成员 in 成员个人数据列表)

            样品验证 = self._验证单项数据一致性("样品申请数", 团队样品数, 成员样品总数)
            验证结果["验证项目"].append(样品验证)

            # 4. 汇总验证结果
            不一致项目 = [项目 for 项目 in 验证结果["验证项目"] if not 项目["一致"]]

            if 不一致项目:
                验证结果["验证通过"] = False
                验证结果["差异报告"] = [
                    f"{项目['指标名称']}: 团队汇总({项目['团队数据']}) != 成员汇总({项目['成员数据']}), 差异: {项目['差异值']}"
                    for 项目 in 不一致项目
                ]

            数据库日志器.info(
                f"数据一致性验证完成: {'通过' if 验证结果['验证通过'] else '失败'}, "
                f"验证项目数: {len(验证结果['验证项目'])}, "
                f"不一致项目数: {len(不一致项目)}"
            )

            return 验证结果

        except Exception as e:
            错误日志器.error(f"数据一致性验证失败: {str(e)}")
            return {
                "验证通过": False,
                "差异报告": [f"验证过程异常: {str(e)}"],
                "验证时间": datetime.now().isoformat(),
                "验证项目": [],
            }

    def _验证单项数据一致性(
        self, 指标名称: str, 团队数据: int, 成员数据: int
    ) -> Dict[str, Any]:
        """验证单项数据的一致性"""
        差异值 = abs(团队数据 - 成员数据)
        一致 = 差异值 == 0

        return {
            "指标名称": 指标名称,
            "团队数据": 团队数据,
            "成员数据": 成员数据,
            "差异值": 差异值,
            "一致": 一致,
            "差异率": self.统一计算比率(差异值, max(团队数据, 成员数据, 1))
            if not 一致
            else 0.0,
        }

    def 计算运行天数(self, 创建时间: datetime) -> int:
        """
        计算团队运行天数

        Args:
            创建时间: 团队创建时间

        Returns:
            运行天数
        """
        if not 创建时间:
            return 0

        当前时间 = datetime.now()
        return (当前时间 - 创建时间).days

    def 计算活跃度(self, 活跃成员数: int, 总成员数: int) -> int:
        """
        计算团队活跃度百分比

        Args:
            活跃成员数: 活跃成员数量
            总成员数: 总成员数量

        Returns:
            活跃度百分比（0-100）
        """
        if 总成员数 <= 0:
            return 0
        return round((活跃成员数 / 总成员数) * 100)

    def 计算活动趋势(self, 新增成员: int, 离开成员: int) -> str:
        """
        计算团队活动趋势

        Args:
            新增成员: 新增成员数量
            离开成员: 离开成员数量

        Returns:
            活动趋势（上升/下降/平稳）
        """
        if 新增成员 > 离开成员:
            return "上升"
        elif 新增成员 < 离开成员:
            return "下降"
        else:
            return "平稳"

    def 计算平均值(self, 总数: int, 成员数: int) -> float:
        """
        计算平均值，避免除零错误

        Args:
            总数: 总数值
            成员数: 成员数量

        Returns:
            平均值（保留1位小数）
        """
        if 成员数 <= 0:
            return 0.0
        return round(总数 / 成员数, 1)

    def 格式化时间字段(self, 时间值: Any) -> str:
        """
        格式化时间字段为字符串

        Args:
            时间值: 时间对象或字符串

        Returns:
            格式化后的时间字符串
        """
        if not 时间值:
            return ""

        if isinstance(时间值, str):
            if 时间值 == "1970-01-01":
                return "未登录"
            return 时间值

        if hasattr(时间值, "strftime"):
            if hasattr(时间值, "year") and 时间值.year <= 1970:
                return "未登录"
            return 时间值.strftime("%Y-%m-%d %H:%M:%S")

        return str(时间值)

    def 处理成员数据(self, 成员原始数据: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        处理成员原始数据，格式化为前端需要的格式

        Args:
            成员原始数据: 从数据库查询的原始成员数据

        Returns:
            格式化后的成员数据列表
        """
        成员列表 = []

        for 成员 in 成员原始数据:
            用户id = 成员["用户id"]

            # 获取真实的微信、好友和寄样数据
            微信个数 = int(成员["微信个数"]) if 成员["微信个数"] is not None else 0
            好友数量 = int(成员["好友数量"]) if 成员["好友数量"] is not None else 0
            今日新增好友 = (
                int(成员["今日新增好友"]) if 成员["今日新增好友"] is not None else 0
            )
            寄样数量 = int(成员["寄样数量"]) if 成员["寄样数量"] is not None else 0

            # 生成一些模拟的其他业务数据（实际项目中应该从业务表获取）
            其他业务数据 = {
                "达人沟通": (用户id * 5) % 30,  # 0-30之间
                "寄样数量": 寄样数量,  # 使用真实的寄样数量
                "排期数量": (用户id * 4) % 25,  # 0-25之间
                "开播数量": (用户id * 1) % 10,  # 0-10之间
            }

            成员信息 = {
                "用户id": 用户id,
                "用户名": 成员["用户名"] or f"用户{用户id}",
                "手机号": 成员["手机号"],
                "职位": 成员["职位"] or "团队成员",
                "角色名称": "团队成员",  # 默认角色，因为数据库中暂无角色名称字段
                "加入时间": 成员["加入时间"].strftime("%Y-%m-%d")
                if 成员["加入时间"]
                else "",
                "状态": 成员["状态"],
                "头像": None,  # 用户表中暂无头像字段，设为None
                "最新登录时间": self.格式化时间字段(成员["最新登录时间"]),
                # 真实的微信和好友数据
                "微信个数": 微信个数,
                "好友数量": 好友数量,
                "今日添加": 今日新增好友,  # 使用真实的今日新增好友数据
                # 其他业务数据
                **其他业务数据,
            }
            成员列表.append(成员信息)

        return 成员列表

    def 计算排名数据(
        self, 成员列表: List[Dict[str, Any]]
    ) -> Dict[str, List[Dict[str, Any]]]:
        """
        计算各种排名数据

        Args:
            成员列表: 处理后的成员列表

        Returns:
            包含各种排名的字典
        """
        return {
            "好友数量排名前三": sorted(
                成员列表, key=lambda x: x["好友数量"], reverse=True
            )[:3],
            "今日新增排名前三": sorted(
                成员列表, key=lambda x: x["今日添加"], reverse=True
            )[:3],
            "寄样数量排名前三": sorted(
                成员列表, key=lambda x: x["寄样数量"], reverse=True
            )[:3],
        }

    async def 获取团队业务流程数据(self, 团队id: int) -> Dict[str, Any]:
        """
        获取团队真实业务流程数据

        Args:
            团队id: 团队id

        Returns:
            业务流程数据字典
        """
        try:
            from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

            # 获取团队成员id列表
            团队成员SQL = """
            SELECT 用户id FROM 用户团队关联表
            WHERE 团队id = $1 AND 状态 = '正常'
            """
            团队成员结果 = await 异步连接池实例.执行查询(团队成员SQL, (团队id,))
            团队成员ids = (
                [row["用户id"] for row in 团队成员结果] if 团队成员结果 else []
            )

            if not 团队成员ids:
                return {"阶段数据": {}}

            # 构建成员id的占位符
            成员id占位符 = ",".join([f"${i+1}" for i in range(len(团队成员ids))])

            # 查询业务流程数据
            业务流程SQL = f"""
            SELECT
                COUNT(*) as 总进度数量,
                COUNT(CASE WHEN 意向状态 = 1 THEN 1 END) as 意向确认数量,
                COUNT(CASE WHEN 样品状态 = 1 THEN 1 END) as 样品确认数量,
                COUNT(CASE WHEN 排期状态 = 1 THEN 1 END) as 排期确认数量,
                COUNT(CASE WHEN 开播状态 = 1 THEN 1 END) as 开播完成数量,
                COUNT(CASE WHEN DATE(意向状态更新时间) = CURRENT_DATE AND 意向状态 = 1 THEN 1 END) as 今日新增意向,
                COUNT(CASE WHEN DATE(样品状态更新时间) = CURRENT_DATE AND 样品状态 = 1 THEN 1 END) as 今日新增样品,
                COUNT(CASE WHEN DATE(排期开始时间) = CURRENT_DATE THEN 1 END) as 今日新增排期,
                COUNT(CASE WHEN DATE(创建时间) = CURRENT_DATE AND 开播状态 = 1 THEN 1 END) as 今日新增开播
            FROM 微信产品对接进度表
            WHERE 用户id IN ({成员id占位符})
            """

            业务流程结果 = await 异步连接池实例.执行查询(
                业务流程SQL, tuple(团队成员ids)
            )
            业务数据 = 业务流程结果[0] if 业务流程结果 else {}

            # 查询邀约数据
            邀约SQL = f"""
            SELECT
                COUNT(*) as 总邀约数量,
                COUNT(CASE WHEN DATE(邀约发起时间) = CURRENT_DATE THEN 1 END) as 今日邀约数量
            FROM 用户抖音达人邀约记录表
            WHERE 用户id IN ({成员id占位符})
            """

            邀约结果 = await 异步连接池实例.执行查询(邀约SQL, tuple(团队成员ids))
            邀约数据 = 邀约结果[0] if 邀约结果 else {}

            # 查询寄样数据（修复字段冲突问题）
            寄样SQL = f"""
            SELECT
                COUNT(*) as 总寄样数量,
                COUNT(CASE WHEN DATE(s.创建时间) = CURRENT_DATE THEN 1 END) as 今日寄样数量
            FROM 样品信息记录表 s
            JOIN 微信产品对接进度表 w ON s.微信产品对接进度表ID = w.id
            WHERE w.用户id IN ({成员id占位符})
            """

            寄样结果 = await 异步连接池实例.执行查询(寄样SQL, tuple(团队成员ids))
            寄样数据 = 寄样结果[0] if 寄样结果 else {}

            # 构建业务流程数据
            阶段数据 = {
                "总进度数量": int(业务数据.get("总进度数量", 0)),
                "意向确认数量": int(业务数据.get("意向确认数量", 0)),
                "样品确认数量": int(业务数据.get("样品确认数量", 0)),
                "排期确认数量": int(业务数据.get("排期确认数量", 0)),
                "开播完成数量": int(业务数据.get("开播完成数量", 0)),
                "今日新增意向": int(业务数据.get("今日新增意向", 0)),
                "今日新增样品": int(业务数据.get("今日新增样品", 0)),
                "今日新增排期": int(业务数据.get("今日新增排期", 0)),
                "今日新增开播": int(业务数据.get("今日新增开播", 0)),
                "总邀约数量": int(邀约数据.get("总邀约数量", 0)),
                "今日邀约数量": int(邀约数据.get("今日邀约数量", 0)),
                "总寄样数量": int(寄样数据.get("总寄样数量", 0)),
                "今日寄样数量": int(寄样数据.get("今日寄样数量", 0)),
                "平均周期天数": 30,  # 默认30天，后续可以根据实际数据计算
            }

            数据库日志器.info(f"获取团队 {团队id} 业务流程数据成功: {阶段数据}")

            return {"阶段数据": 阶段数据}

        except Exception as e:
            错误日志器.error(
                f"获取团队业务流程数据失败: 团队id={团队id}, 错误={e}", exc_info=True
            )
            # 直接抛出异常，让上层统一处理
            raise e

    def 构建聚合响应数据(
        self,
        团队信息: Dict[str, Any],
        成员统计: Dict[str, Any],
        微信好友汇总: Dict[str, Any],
        寄样汇总: Dict[str, Any],
        成员列表: List[Dict[str, Any]],
        业务流程数据: Dict[str, Any],
    ) -> Dict[str, Any]:
        """
        构建最终的聚合响应数据 - 优化版本，移除冗余代码和无效数据项

        Args:
            团队信息: 团队基本信息
            成员统计: 成员统计数据
            微信好友汇总: 微信好友汇总数据
            寄样汇总: 寄样汇总数据
            成员列表: 处理后的成员列表
            业务流程数据: 业务流程数据

        Returns:
            优化后的聚合响应数据，移除了冗余字段和无效数据项
        """
        try:
            # 计算运行天数
            运行天数 = self.计算运行天数(团队信息["创建时间"])

            # 计算活跃度和趋势
            活跃成员数 = 成员统计.get("活跃成员数", 0)
            总成员数 = 成员统计.get("总成员数", 0)
            活跃度 = self.计算活跃度(活跃成员数, 总成员数)

            新增成员 = 成员统计.get("近30天新增", 0)
            离开成员 = 成员统计.get("近30天离开", 0)
            活动趋势 = self.计算活动趋势(新增成员, 离开成员)

            # 处理成员列表
            处理后成员列表 = self.处理成员数据(成员列表)

            # 计算排名数据
            排名数据 = self.计算排名数据(处理后成员列表)

            # 构建最终响应数据
            聚合数据 = {
                # 团队基本信息
                "团队信息": {
                    "团队id": 团队信息["团队id"],
                    "团队名称": 团队信息["团队名称"],
                    "公司名称": 团队信息["公司名称"],
                    "公司ID": 团队信息["公司ID"],
                    "团队状态": 团队信息["团队状态"],
                    "运行天数": 运行天数,
                    "创建时间": 团队信息["创建时间"].strftime("%Y-%m-%d")
                    if 团队信息["创建时间"]
                    else "",
                    "创建人id": 团队信息["创建人id"],
                    "团队负责人id": 团队信息["团队负责人id"],
                },
                # 概览统计（核心指标）
                "概览统计": {
                    "团队成员数": 活跃成员数,
                    "最大成员数": 团队信息["最大成员数"],
                    "在线成员数": 成员统计.get("在线成员数", 0),
                    "今日活跃数": 成员统计.get("今日活跃数", 0),
                    "暂停成员数": 成员统计.get("暂停成员数", 0),
                    "已移除成员数": 成员统计.get("已移除成员数", 0),
                    "总成员数": 总成员数,
                },
                # 活动统计（30天数据）
                "活动统计": {
                    "统计天数": 30,
                    "新增成员": 新增成员,
                    "离开成员": 离开成员,
                    "净增成员": 新增成员 - 离开成员,
                    "活跃度": 活跃度,
                    "活动趋势": 活动趋势,
                    "成员增长率": round(
                        (新增成员 / max(活跃成员数 - 新增成员, 1)) * 100, 1
                    ),
                },
                # 成员列表（前20名活跃成员）
                "成员列表": 处理后成员列表,
                # 微信好友统计汇总（基于全部成员，不是只针对前20名显示成员）
                "微信好友汇总": {
                    # 真实数据统计 - 确保所有数据类型正确
                    "总微信数": int(微信好友汇总.get("总微信数", 0) or 0),
                    "总好友数": int(微信好友汇总.get("总好友数", 0) or 0),
                    "今日新增好友总数": int(
                        微信好友汇总.get("今日新增好友总数", 0) or 0
                    ),
                    "平均微信个数": round(
                        float(
                            self.计算平均值(
                                int(微信好友汇总.get("总微信数", 0) or 0), 总成员数
                            )
                        ),
                        1,
                    ),
                    "平均好友数量": round(
                        float(
                            self.计算平均值(
                                int(微信好友汇总.get("总好友数", 0) or 0), 总成员数
                            )
                        ),
                        1,
                    ),
                    "平均今日新增": round(
                        float(
                            self.计算平均值(
                                int(微信好友汇总.get("今日新增好友总数", 0) or 0),
                                总成员数,
                            )
                        ),
                        1,
                    ),
                    # 其他业务数据统计（从成员列表计算，仅用于展示的前20名成员）
                    "总达人沟通": sum(m["达人沟通"] for m in 处理后成员列表),
                    "总排期数量": sum(m["排期数量"] for m in 处理后成员列表),
                    "总开播数量": sum(m["开播数量"] for m in 处理后成员列表),
                    # 排名数据
                    **排名数据,
                },
                # 寄样统计汇总（使用与微信好友汇总相同的数据类型处理逻辑）
                "寄样汇总": {
                    "总寄样数量": int(寄样汇总.get("总寄样数量", 0) or 0),
                    "参与寄样成员数": int(寄样汇总.get("参与寄样成员数", 0) or 0),
                    "平均寄样数量": round(
                        float(
                            self.计算平均值(
                                int(寄样汇总.get("总寄样数量", 0) or 0), 总成员数
                            )
                        ),
                        1,
                    ),
                },
                # 业务流程数据（从真实业务表获取）
                "业务流程": 业务流程数据,
            }

            return 聚合数据

        except Exception as e:
            错误日志器.error(f"构建聚合响应数据失败: 错误={e}", exc_info=True)
            return {"error": f"构建响应数据失败: {str(e)}"}


# 创建全局实例
团队数据处理工具实例 = 团队数据处理工具()
