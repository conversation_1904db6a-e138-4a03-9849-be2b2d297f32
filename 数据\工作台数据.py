"""
工作台数据访问层

负责工作台相关的所有数据库查询操作
"""

from datetime import datetime
from typing import Dict, Any, List
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 系统日志器


class 工作台数据访问层:
    """工作台数据访问层"""

    def __init__(self, 数据库连接池=None):
        """初始化数据访问层"""
        self.数据库连接池 = 数据库连接池 or 异步连接池实例

    async def 获取_微信运营统计_数据(
        self, 用户id: int, 开始时间: datetime, 结束时间: datetime
    ) -> Dict[str, Any]:
        """获取微信运营统计数据"""
        try:
            async with self.数据库连接池.获取连接() as 连接:
                统计数据 = await 连接.fetchrow(
                    """
                    SELECT
                        COUNT(DISTINCT uwx.微信id) as 微信账号数,
                        COUNT(DISTINCT CASE
                            WHEN wxhy.对方微信号id IS NOT NULL
                                 AND wxhy.好友入库时间 <= $2
                            THEN wxhy.对方微信号id
                            ELSE NULL
                        END) as 好友总数,
                        COUNT(DISTINCT CASE
                            WHEN DATE(wxhy.好友入库时间) = CURRENT_DATE
                            THEN wxhy.对方微信号id
                            ELSE NULL
                        END) as 今日新增好友,
                        COUNT(DISTINCT CASE
                            WHEN DATE(wxhy.好友入库时间) = CURRENT_DATE - INTERVAL '1 day'
                            THEN wxhy.对方微信号id
                            ELSE NULL
                        END) as 昨日新增好友,
                        COUNT(DISTINCT CASE
                            WHEN DATE(wxhy.好友入库时间) >= CURRENT_DATE - INTERVAL '7 days'
                            THEN wxhy.对方微信号id
                            ELSE NULL
                        END) as 本周新增好友,
                        COUNT(DISTINCT CASE
                            WHEN DATE(wxhy.好友入库时间) >= CURRENT_DATE - INTERVAL '30 days'
                            THEN wxhy.对方微信号id
                            ELSE NULL
                        END) as 本月新增好友,
                        COUNT(DISTINCT CASE
                            WHEN wxhy.好友入库时间 BETWEEN $1 AND $2
                            THEN wxhy.对方微信号id
                            ELSE NULL
                        END) as 时间范围新增好友
                    FROM 用户微信关联表 uwx
                    LEFT JOIN 微信好友表 wxhy ON uwx.微信id = wxhy.我方微信号id
                    WHERE uwx.用户id = $3 AND uwx.状态 = 1
                """,
                开始时间, 结束时间, 用户id,
                )
                return dict(统计数据) if 统计数据 else {}
        except Exception as e:
            系统日志器.error(f"获取微信运营统计数据失败: {str(e)}")
            raise e

    async def 获取_微信核心指标_数据(
        self, 用户id: int, 开始时间: datetime, 结束时间: datetime
    ) -> Dict[str, Any]:
        """获取微信运营核心指标数据"""
        try:
            async with self.数据库连接池.获取连接() as 连接:
                统计数据 = await 连接.fetchrow(
                    """
                    SELECT
                        -- 1. 微信账号数量
                        COUNT(DISTINCT uwx.微信id) as 微信账号数量,

                        -- 2. 好友总数
                        COUNT(DISTINCT wxhy.对方微信号id) as 好友总数,

                        -- 3. 今日新增好友数量（固定今日）
                        COUNT(DISTINCT CASE
                            WHEN DATE(wxhy.好友入库时间) = CURRENT_DATE
                            THEN wxhy.对方微信号id
                            ELSE NULL
                        END) as 今日新增,

                        -- 4. 昨日新增好友数量（固定昨日）
                        COUNT(DISTINCT CASE
                            WHEN DATE(wxhy.好友入库时间) = CURRENT_DATE - INTERVAL '1 day'
                            THEN wxhy.对方微信号id
                            ELSE NULL
                        END) as 昨日新增,

                        -- 5. 本周新增好友数量（固定本周）
                        COUNT(DISTINCT CASE
                            WHEN wxhy.好友入库时间 >= CURRENT_DATE - INTERVAL '7 days'
                            THEN wxhy.对方微信号id
                            ELSE NULL
                        END) as 本周新增,

                        -- 6. 上周新增好友数量
                        COUNT(DISTINCT CASE
                            WHEN wxhy.好友入库时间 >= CURRENT_DATE - INTERVAL '14 days'
                                 AND wxhy.好友入库时间 < CURRENT_DATE - INTERVAL '7 days'
                            THEN wxhy.对方微信号id
                            ELSE NULL
                        END) as 上周新增,

                        -- 7. 本月新增好友数量（固定本月）
                        COUNT(DISTINCT CASE
                            WHEN wxhy.好友入库时间 >= DATE_TRUNC('month', CURRENT_DATE)
                            THEN wxhy.对方微信号id
                            ELSE NULL
                        END) as 本月新增,

                        -- 8. 上月新增好友数量
                        COUNT(DISTINCT CASE
                            WHEN wxhy.好友入库时间 >= DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '1 month'
                                 AND wxhy.好友入库时间 < DATE_TRUNC('month', CURRENT_DATE)
                            THEN wxhy.对方微信号id
                            ELSE NULL
                        END) as 上月新增,

                        -- 9. 本季度新增好友数量
                        COUNT(DISTINCT CASE
                            WHEN wxhy.好友入库时间 >= DATE_TRUNC('quarter', CURRENT_DATE)
                            THEN wxhy.对方微信号id
                            ELSE NULL
                        END) as 本季度新增,

                        -- 10. 上季度新增好友数量
                        COUNT(DISTINCT CASE
                            WHEN wxhy.好友入库时间 >= DATE_TRUNC('quarter', CURRENT_DATE) - INTERVAL '3 months'
                                 AND wxhy.好友入库时间 < DATE_TRUNC('quarter', CURRENT_DATE)
                            THEN wxhy.对方微信号id
                            ELSE NULL
                        END) as 上季度新增,

                        -- 11. 时间范围内新增好友数量（基于传入的时间范围）
                        COUNT(DISTINCT CASE
                            WHEN wxhy.好友入库时间 BETWEEN $1::timestamp AND $2::timestamp
                            THEN wxhy.对方微信号id
                            ELSE NULL
                        END) as 时间范围新增,

                        -- 12. 发送好友请求数（基于时间范围，使用发送请求时间字段筛选）
                        COUNT(DISTINCT CASE
                            WHEN wxhy.发送请求时间 BETWEEN $1::timestamp AND $2::timestamp
                            THEN wxhy.我方微信号id || '-' || wxhy.对方微信号id
                            ELSE NULL
                        END) as 发送好友请求数,

                        -- 13. 沟通好友数（好友已入库且我方在时间范围内有消息时间）
                        COUNT(DISTINCT CASE
                            WHEN wxhy.好友入库时间 IS NOT NULL
                                 AND wxhy.我方最后一条消息发送时间 BETWEEN $1::timestamp AND $2::timestamp
                            THEN wxhy.对方微信号id
                            ELSE NULL
                        END) as 沟通好友数,

                        -- 14. 互动好友数（我方和对方都有消息时间）
                        COUNT(DISTINCT CASE
                            WHEN wxhy.我方最后一条消息发送时间 IS NOT NULL
                                 AND wxhy.对方最后一条消息发送时间 IS NOT NULL
                            THEN wxhy.对方微信号id
                            ELSE NULL
                        END) as 互动好友数

                    FROM 用户微信关联表 uwx
                    LEFT JOIN 微信好友表 wxhy ON uwx.微信id = wxhy.我方微信号id
                    WHERE uwx.用户id = $3 AND uwx.状态 = 1
                """,
                开始时间, 结束时间, 用户id,
                )
                return dict(统计数据) if 统计数据 else {}
        except Exception as e:
            系统日志器.error(f"获取微信核心指标数据失败: {str(e)}")
            raise e

    async def 获取_邀约业务_数据(
        self, 用户id: int, 开始时间: datetime, 结束时间: datetime
    ) -> Dict[str, Any]:
        """获取邀约业务数据"""
        try:
            async with self.数据库连接池.获取连接() as 连接:
                邀约数据 = await 连接.fetchrow(
                    """
                    SELECT
                        COUNT(CASE WHEN 邀约发起时间 <= $2 THEN 1 END) as 邀约总数,
                        COUNT(CASE WHEN DATE(邀约发起时间) = CURRENT_DATE THEN 1 END) as 今日邀约数,
                        COUNT(CASE WHEN DATE(邀约发起时间) = CURRENT_DATE - INTERVAL '1 day' THEN 1 END) as 昨日邀约数,
                        COUNT(CASE WHEN 邀约发起时间 >= CURRENT_DATE - INTERVAL '7 days' THEN 1 END) as 本周邀约数,
                        COUNT(CASE WHEN 邀约发起时间 >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as 本月邀约数,
                        COUNT(CASE WHEN 邀约状态 = '1308' AND 邀约发起时间 <= $2 THEN 1 END) as 意向合作数,
                        COUNT(CASE WHEN 邀约状态 = '1305' AND 邀约发起时间 <= $2 THEN 1 END) as 已建联数,
                        COUNT(CASE WHEN 邀约状态 = '1306' AND 邀约发起时间 <= $2 THEN 1 END) as 合作中数,
                        COUNT(CASE WHEN 邀约发起时间 BETWEEN $3 AND $2 THEN 1 END) as 时间范围邀约数
                    FROM 用户抖音达人邀约记录表
                    WHERE 用户id = $1
                """,
                用户id, 结束时间, 开始时间,
                )
                return dict(邀约数据) if 邀约数据 else {}
        except Exception as e:
            系统日志器.error(f"获取邀约业务数据失败: {str(e)}")
            raise e

    async def 获取_平台达人基础统计_数据(
        self, 用户id: int, 平台: str, 开始时间: datetime, 结束时间: datetime
    ) -> Dict[str, Any]:
        """获取平台达人基础统计数据"""
        try:
            基础统计SQL = """
            SELECT
                COUNT(DISTINCT CASE WHEN uda.认领时间 <= $1 THEN uda.达人id END) as 总数,
                COUNT(DISTINCT CASE WHEN uda.认领时间 BETWEEN $2 AND $1 THEN uda.达人id END) as 时间范围新增,
                COUNT(DISTINCT CASE WHEN uda.认领时间 >= NOW() - INTERVAL '7 days' THEN uda.达人id END) as 七天新增
            FROM 用户达人关联表 uda
            WHERE uda.用户id = $3
            AND uda.状态 = 1
            AND uda.平台 = $4
            """

            async with self.数据库连接池.获取连接() as 连接:
                基础统计结果 = await 连接.fetch(
                    基础统计SQL, 结束时间, 开始时间, 用户id, 平台
                )

                if not 基础统计结果:
                    return {}

                return dict(基础统计结果[0])
        except Exception as e:
            系统日志器.error(f"获取平台达人基础统计数据失败: {str(e)}")
            raise e

    async def 获取_平台达人联系方式统计_数据(self, 用户id: int, 平台: str) -> int:
        """获取平台达人联系方式统计数据"""
        try:
            if 平台 == "微信":
                联系方式SQL = """
                SELECT COUNT(DISTINCT uda.达人id) as 有联系方式数
                FROM 用户达人关联表 uda
                INNER JOIN 用户达人补充信息表 udsi ON uda.id = udsi.用户达人关联表id
                INNER JOIN 微信信息表 wi ON udsi.微信信息表id = wi.id
                WHERE uda.用户id = $1
                AND uda.状态 = 1
                AND uda.平台 = '微信'
                AND wi.微信号 IS NOT NULL
                AND wi.微信号 != ''
                """
            elif 平台 == "抖音":
                联系方式SQL = """
                SELECT COUNT(DISTINCT uda.达人id) as 有联系方式数
                FROM 用户达人关联表 uda
                INNER JOIN 达人和联系方式的关联表 dcr ON uda.达人id = dcr.抖音达人表id
                INNER JOIN 联系方式表 ct ON dcr.联系方式表id = ct.id
                WHERE uda.用户id = $1
                AND uda.状态 = 1
                AND uda.平台 = '抖音'
                AND ct.联系方式 IS NOT NULL
                AND ct.联系方式 != ''
                """
            else:
                return 0

            async with self.数据库连接池.获取连接() as 连接:
                联系方式结果 = await 连接.fetch(联系方式SQL, 用户id)
                if 联系方式结果:
                    return 联系方式结果[0].get("有联系方式数", 0)
                return 0
        except Exception as e:
            系统日志器.error(f"获取平台达人联系方式统计数据失败: {str(e)}")
            raise e

    async def 获取_联系方式统计_数据(
        self, 用户id: int, 开始时间: datetime, 结束时间: datetime
    ) -> Dict[str, int]:
        """获取联系方式统计数据"""
        try:
            # 构建时间条件
            时间条件 = "AND u.认领时间 >= $2 AND u.认领时间 <= $3"

            async with self.数据库连接池.获取连接() as 连接:
                # 获取微信平台有联系方式的达人数
                微信查询 = f"""
                SELECT COUNT(DISTINCT u.达人id) as count
                FROM 用户达人关联表 u
                INNER JOIN 用户达人补充信息表 p ON u.id = p.用户达人关联表id
                WHERE u.用户id = $1 AND u.状态 = 1 AND u.平台 = '微信'
                AND (
                    -- 微信信息表中的联系方式
                    (p.微信信息表id IS NOT NULL AND EXISTS (
                        SELECT 1 FROM 微信信息表 w
                        WHERE w.id = p.微信信息表id
                        AND w.微信号 IS NOT NULL
                        AND TRIM(w.微信号) != ''
                    ))
                    OR
                    -- 补充信息表中的其他联系方式
                    (p.联系方式 IS NOT NULL AND TRIM(p.联系方式) != '')
                )
                {时间条件}
                """
                微信结果 = await 连接.fetchrow(微信查询, 用户id, 开始时间, 结束时间)
                微信联系方式数 = 微信结果["count"] if 微信结果 else 0

                # 获取抖音平台有联系方式的达人数
                抖音查询 = f"""
                SELECT COUNT(DISTINCT u.达人id) as count
                FROM 用户达人关联表 u
                INNER JOIN 用户达人补充信息表 p ON u.id = p.用户达人关联表id
                WHERE u.用户id = $1 AND u.状态 = 1 AND u.平台 = '抖音'
                AND (
                    -- 微信信息表中的联系方式
                    (p.微信信息表id IS NOT NULL AND EXISTS (
                        SELECT 1 FROM 微信信息表 w
                        WHERE w.id = p.微信信息表id
                        AND w.微信号 IS NOT NULL
                        AND TRIM(w.微信号) != ''
                    ))
                    OR
                    -- 补充信息表中的其他联系方式
                    (p.联系方式 IS NOT NULL AND TRIM(p.联系方式) != '')
                )
                {时间条件}
                """
                抖音结果 = await 连接.fetchrow(抖音查询, 用户id, 开始时间, 结束时间)
                抖音联系方式数 = 抖音结果["count"] if 抖音结果 else 0

                return {"微信": 微信联系方式数, "抖音": 抖音联系方式数}
        except Exception as e:
            系统日志器.error(f"获取联系方式统计数据失败: {str(e)}")
            raise e

    async def 获取_好友统计_数据(
        self, 用户id: int, 结束时间: datetime
    ) -> Dict[str, int]:
        """获取好友统计数据"""
        try:
            # 修正累计性指标：好友总数应该统计到时间段结束时间点的累计总数
            时间条件 = "AND u.认领时间 <= $3"

            # 获取微信平台已添加好友的达人数（累计到结束时间点）
            微信查询 = f"""
            SELECT COUNT(DISTINCT u.达人id) as count
            FROM 用户达人关联表 u
            LEFT JOIN 用户达人补充信息表 p ON u.id = p.用户达人关联表id
            LEFT JOIN 微信信息表 w ON p.微信信息表id = w.id
            LEFT JOIN 用户微信关联表 uw ON w.id = uw.微信id
            LEFT JOIN 微信好友表 wf ON w.id = wf.对方微信号id AND uw.微信id = wf.我方微信号id
            WHERE u.用户id = $1 AND u.状态 = 1 AND u.平台 = '微信'
            AND uw.用户id = $2
            AND wf.我方微信号id IS NOT NULL
            {时间条件}
            """
            async with self.数据库连接池.获取连接() as 连接:
                微信结果 = await 连接.fetchrow(微信查询, 用户id, 用户id, 结束时间)
                微信好友数 = 微信结果["count"] if 微信结果 else 0

            # 获取抖音平台已添加好友的达人数（抖音暂无好友关系，返回0）
            抖音好友数 = 0

            return {"微信": 微信好友数, "抖音": 抖音好友数}
        except Exception as e:
            系统日志器.error(f"获取好友统计数据失败: {str(e)}")
            raise e

    async def 获取_趋势_数据(
        self, 用户id: int, 数据类型: str, 开始时间: datetime, 结束时间: datetime
    ) -> List[Dict[str, Any]]:
        """获取趋势数据"""
        try:
            async with self.数据库连接池.获取连接() as 连接:
                if 数据类型 == "invitation":
                    # 邀约趋势数据
                    趋势数据 = await 连接.fetch(
                        """
                        SELECT
                            DATE(邀约发起时间) as 日期,
                            COUNT(*) as 数值
                        FROM 用户抖音达人邀约记录表
                        WHERE 用户id = $1
                        AND 邀约发起时间 BETWEEN $2 AND $3
                        GROUP BY DATE(邀约发起时间)
                        ORDER BY 日期
                        """,
                        用户id, 开始时间, 结束时间,
                    )
                elif 数据类型 == "cooperation":
                    # 合作趋势数据
                    趋势数据 = await 连接.fetch(
                        """
                        SELECT
                            DATE(邀约发起时间) as 日期,
                            COUNT(*) as 数值
                        FROM 用户抖音达人邀约记录表
                        WHERE 用户id = $1
                        AND 邀约发起时间 BETWEEN $2 AND $3
                        AND 邀约状态 IN ('1305', '1306', '1308')
                        GROUP BY DATE(邀约发起时间)
                        ORDER BY 日期
                        """,
                        用户id, 开始时间, 结束时间,
                    )
                else:
                    return []

                return [dict(row) for row in 趋势数据] if 趋势数据 else []
        except Exception as e:
            系统日志器.error(f"获取趋势数据失败: {str(e)}")
            raise e

    async def 获取_达人活跃度_数据(self, 用户id: int) -> List[Dict[str, Any]]:
        """获取达人活跃度数据"""
        try:
            async with self.数据库连接池.获取连接() as 连接:
                活跃度查询 = """
                SELECT
                    uda.id as 达人关联id,
                    uda.达人id,
                    uda.认领时间,
                    uda.状态,
                    GREATEST(
                        COALESCE(wxhy.我方最后一条消息发送时间, '1900-01-01'::timestamp),
                        COALESCE(wxhy.对方最后一条消息发送时间, '1900-01-01'::timestamp)
                    ) as 最近消息时间,
                    udbi.联系方式,
                    udbi.个人标签,
                    udbi.个人备注,
                    COUNT(s.id) as 样品数量
                FROM 用户达人关联表 uda
                LEFT JOIN 用户达人补充信息表 udbi ON uda.id = udbi.用户达人关联表id
                LEFT JOIN 微信好友表 wxhy ON udbi.微信信息表id = wxhy.对方微信号id
                LEFT JOIN 样品信息记录表 s ON udbi.id = s.用户达人补充信息表id
                WHERE uda.用户id = $1 AND uda.状态 = 1
                GROUP BY uda.id, uda.认领时间, uda.状态, 最近消息时间, udbi.联系方式, udbi.个人标签, udbi.个人备注
                """

                达人记录 = await 连接.fetch(活跃度查询, 用户id)
                return [dict(row) for row in 达人记录] if 达人记录 else []
        except Exception as e:
            系统日志器.error(f"获取达人活跃度数据失败: {str(e)}")
            raise e

    async def 获取_合作项目_数据(
        self, 用户id: int, 开始时间: datetime, 结束时间: datetime
    ) -> Dict[str, Any]:
        """获取合作项目数据"""
        try:
            async with self.数据库连接池.获取连接() as 连接:
                项目数据 = await 连接.fetchrow(
                    """
                    SELECT
                        COUNT(CASE WHEN 邀约状态 IN ('1305', '1306', '1308') THEN 1 END) as 合作项目数,
                        COUNT(CASE WHEN 邀约状态 = '1308' THEN 1 END) as 意向合作数,
                        COUNT(CASE WHEN 邀约状态 = '1305' THEN 1 END) as 样品已发数,
                        COUNT(CASE WHEN 邀约状态 = '1306' THEN 1 END) as 已开播数,
                        0 as 总销售额,
                        COUNT(CASE WHEN DATE(邀约发起时间) = CURRENT_DATE AND 邀约状态 IN ('1305', '1306', '1308') THEN 1 END) as 今日新增项目,
                        COUNT(CASE WHEN DATE(邀约发起时间) = CURRENT_DATE - INTERVAL '1 day' AND 邀约状态 IN ('1305', '1306', '1308') THEN 1 END) as 昨日新增项目,
                        COUNT(CASE WHEN 邀约发起时间 BETWEEN $2 AND $3 AND 邀约状态 IN ('1305', '1306', '1308') THEN 1 END) as 时间范围新增项目
                    FROM 用户抖音达人邀约记录表
                    WHERE 用户id = $1
                """,
                    用户id, 开始时间, 结束时间,
                )
            return dict(项目数据) if 项目数据 else {}
        except Exception as e:
            系统日志器.error(f"获取合作项目数据失败: {str(e)}")
            raise e

    async def 获取_团队数据_概览(
        self, 用户id: int, 开始时间: datetime, 结束时间: datetime
    ) -> Dict[str, Any]:
        """获取团队数据概览"""
        try:
            async with self.数据库连接池.获取连接() as 连接:
                # 获取团队基础数据
                团队基础数据 = await 连接.fetchrow(
                    """
                    SELECT
                        COUNT(DISTINCT ut1.团队id) as 参与团队数,
                        COUNT(DISTINCT ut2.用户id) as 团队成员总数
                    FROM 用户团队关联表 ut1
                    JOIN 用户团队关联表 ut2 ON ut1.团队id = ut2.团队id
                    WHERE ut1.用户id = $1
                    """,
                    用户id,
                )

                # 获取团队邀约总数
                团队邀约数据 = await 连接.fetchrow(
                    """
                    SELECT COUNT(*) as 团队邀约总数
                    FROM 用户抖音达人邀约记录表 ur
                    WHERE ur.用户id IN (
                        SELECT ut2.用户id
                        FROM 用户团队关联表 ut1
                        JOIN 用户团队关联表 ut2 ON ut1.团队id = ut2.团队id
                        WHERE ut1.用户id = $1
                    )
                    AND ur.邀约发起时间 BETWEEN $2 AND $3
                    """,
                    用户id, 开始时间, 结束时间,
                )

                结果 = {}
                if 团队基础数据:
                    结果.update(dict(团队基础数据))
                if 团队邀约数据:
                    结果.update(dict(团队邀约数据))

                return 结果
        except Exception as e:
            系统日志器.error(f"获取团队数据概览失败: {str(e)}")
            raise e

    async def 获取_待办事项_数据(self, 用户id: int) -> List[Dict[str, Any]]:
        """获取待办事项数据"""
        try:
            async with self.数据库连接池.获取连接() as 连接:
                # 获取未发货样品
                未发货样品 = await 连接.fetch(
                    """
                    SELECT s.id, s.收件人, s.用户产品表id, s.创建时间
                    FROM 样品信息记录表 s
                    LEFT JOIN 微信产品对接进度表 w ON s.微信产品对接进度表id = w.id
                    WHERE w.用户id = $1
                    AND (s.用户审核状态 = 1 OR s.负责人审核状态 = 1)
                    AND (s.快递单号 IS NULL OR s.快递单号 = '')
                    ORDER BY s.创建时间 DESC
                    LIMIT 5
                """,
                用户id,
                )

                # 获取待跟进邀约
                待跟进邀约 = await 连接.fetch(
                    """
                    SELECT id, kol_nickname, 邀约发起时间, 邀约状态
                    FROM 用户抖音达人邀约记录表
                    WHERE 用户id = $1
                    AND 邀约状态 IN ('1301', '1302', '1303')
                    ORDER BY 邀约发起时间 DESC
                    LIMIT 5
                """,
                用户id,
                )

                待办事项 = []

                # 处理未发货样品
                for 样品 in 未发货样品:
                    待办事项.append({
                        "类型": "样品发货",
                        "标题": f"样品待发货 - {样品['收件人']}",
                        "描述": "样品申请已通过，需要安排发货",
                        "创建时间": 样品["创建时间"],
                        "优先级": "高",
                        "关联ID": 样品["id"],
                    })

                # 处理待跟进邀约
                for 邀约 in 待跟进邀约:
                    待办事项.append({
                        "类型": "邀约跟进",
                        "标题": f"邀约跟进 - {邀约['kol_nickname']}",
                        "描述": f"邀约状态：{邀约['邀约状态']}，需要跟进",
                        "创建时间": 邀约["邀约发起时间"],
                        "优先级": "中",
                        "关联ID": 邀约["id"],
                    })

                return 待办事项
        except Exception as e:
            系统日志器.error(f"获取待办事项数据失败: {str(e)}")
            raise e

    async def 批量更新_邀约状态_数据(self, 邀约IDs: List[int], 新状态: str, 用户id: int) -> Dict[str, Any]:
        """批量更新邀约状态数据"""
        try:
            async with self.数据库连接池.获取连接() as 连接:
                # 验证邀约记录是否属于当前用户
                验证查询 = """
                SELECT id FROM 用户抖音达人邀约记录表
                WHERE id = ANY($1) AND 用户id = $2
                """

                有效记录 = await 连接.fetch(验证查询, 邀约IDs, 用户id)
                有效IDs = [记录["id"] for 记录 in 有效记录]

                if not 有效IDs:
                    return {
                        "成功数量": 0,
                        "失败数量": len(邀约IDs),
                        "错误信息": "没有找到有效的邀约记录",
                    }

                # 批量更新状态
                更新查询 = """
                UPDATE 用户抖音达人邀约记录表
                SET 邀约状态 = $1, 更新时间 = NOW()
                WHERE id = ANY($2) AND 用户id = $3
                """

                更新结果 = await 连接.execute(更新查询, 新状态, 有效IDs, 用户id)
                更新数量 = int(更新结果.split()[-1]) if 更新结果 else 0

                return {
                    "成功数量": 更新数量,
                    "失败数量": len(邀约IDs) - 更新数量,
                    "有效IDs": 有效IDs,
                }
        except Exception as e:
            系统日志器.error(f"批量更新邀约状态数据失败: {str(e)}")
            raise e

    async def 获取_达人认领_数据(
        self, 用户id: int, 开始时间: datetime, 结束时间: datetime
    ) -> Dict[str, Any]:
        """获取达人认领数据"""
        try:
            async with self.数据库连接池.获取连接() as 连接:
                达人数据 = await 连接.fetchrow(
                    """
                    SELECT
                        COUNT(CASE WHEN 认领时间 <= $1 THEN 1 END) as 认领达人数,
                        COUNT(CASE WHEN 状态 = 1 AND 认领时间 <= $2 THEN 1 END) as 有效认领数,
                        COUNT(CASE WHEN DATE(认领时间) = CURRENT_DATE THEN 1 END) as 今日新增认领,
                        COUNT(CASE WHEN DATE(认领时间) = CURRENT_DATE - INTERVAL '1 day' THEN 1 END) as 昨日新增认领,
                        COUNT(CASE WHEN 认领时间 >= CURRENT_DATE - INTERVAL '7 days' THEN 1 END) as 本周新增认领,
                        COUNT(CASE WHEN 认领时间 >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as 本月新增认领,
                        COUNT(CASE WHEN 认领时间 BETWEEN $3 AND $4 THEN 1 END) as 时间范围新增认领
                    FROM 用户达人关联表
                    WHERE 用户id = $5
                """,
                结束时间, 结束时间, 开始时间, 结束时间, 用户id,
                )
                return dict(达人数据) if 达人数据 else {}
        except Exception as e:
            系统日志器.error(f"获取达人认领数据失败: {str(e)}")
            raise e

    async def 获取_达人活跃度详细_数据(self, 用户id: int) -> List[Dict[str, Any]]:
        """获取达人活跃度详细数据"""
        try:
            async with self.数据库连接池.获取连接() as 连接:
                活跃度查询 = """
                SELECT
                    uda.id as 关联id,
                    uda.认领时间,
                    uda.状态,

                    -- 沟通活跃度：最近消息时间
                    GREATEST(
                        COALESCE(wxhy.我方最后一条消息发送时间, '1900-01-01'::timestamp),
                        COALESCE(wxhy.对方最后一条消息发送时间, '1900-01-01'::timestamp)
                    ) as 最近消息时间,

                    -- 联系方式完整度
                    CASE WHEN udbi.联系方式 IS NOT NULL AND udbi.联系方式 != '' THEN 1 ELSE 0 END as 有联系方式,

                    -- 样品活跃度：样品数量
                    COUNT(s.id) as 样品数量,

                    -- 信息完整度
                    CASE WHEN udbi.个人标签 IS NOT NULL AND jsonb_array_length(udbi.个人标签::jsonb) > 0 THEN 1 ELSE 0 END as 有个人标签,
                    CASE WHEN udbi.个人备注 IS NOT NULL AND udbi.个人备注 != '' THEN 1 ELSE 0 END as 有备注信息

                FROM 用户达人关联表 uda
                LEFT JOIN 用户达人补充信息表 udbi ON uda.id = udbi.用户达人关联表id
                LEFT JOIN 微信好友表 wxhy ON udbi.微信信息表id = wxhy.对方微信号id
                LEFT JOIN 样品信息记录表 s ON udbi.id = s.用户达人补充信息表id
                WHERE uda.用户id = $1 AND uda.状态 = 1
                GROUP BY uda.id, uda.认领时间, uda.状态, 最近消息时间, udbi.联系方式, udbi.个人标签, udbi.个人备注
                """

                达人记录 = await 连接.fetch(活跃度查询, 用户id)
                return [dict(row) for row in 达人记录] if 达人记录 else []
        except Exception as e:
            系统日志器.error(f"获取达人活跃度详细数据失败: {str(e)}")
            raise e

    async def 获取_主要团队_数据(self, 用户id: int) -> Dict[str, Any]:
        """获取用户的主要团队数据"""
        try:
            async with self.数据库连接池.获取连接() as 连接:
                主要团队数据 = await 连接.fetchrow(
                    """
                    SELECT t.id as 团队id, t.团队名称
                    FROM 用户团队关联表 ut
                    LEFT JOIN 团队表 t ON ut.团队id = t.id
                    WHERE ut.用户id = $1 AND t.团队状态 = '正常'
                    ORDER BY ut.加入时间 DESC
                    LIMIT 1
                    """,
                    用户id,
                )
                return dict(主要团队数据) if 主要团队数据 else {}
        except Exception as e:
            系统日志器.error(f"获取主要团队数据失败: {str(e)}")
            raise e

    async def 获取_微信指标_数据(
        self, 用户id: int, 指标键: str, 日期开始: datetime, 日期结束: datetime
    ) -> int:
        """获取微信相关指标数据"""
        try:
            async with self.数据库连接池.获取连接() as 连接:
                if 指标键 == "wechat_accounts":
                    # 微信账号数量（当日新增绑定的微信账号数）
                    查询SQL = """
                        SELECT COUNT(DISTINCT w.微信id) as 数量
                        FROM 用户微信关联表 w
                        WHERE w.用户id = $1
                        AND w.绑定时间 BETWEEN $2 AND $3
                    """
                    结果 = await 连接.fetchrow(查询SQL, 用户id, 日期开始, 日期结束)

                elif 指标键 == "friends_total":
                    # 好友总数（当日新增好友数）
                    结果 = await 连接.fetchrow(
                        """
                        SELECT COUNT(*) as 数量
                        FROM 微信好友表 f
                        JOIN 用户微信关联表 w ON f.我方微信号id = w.微信id
                        WHERE w.用户id = $1 AND w.状态 = 1
                        AND f.好友入库时间 BETWEEN $2 AND $3
                        AND (f.是否失效 IS NULL OR f.是否失效 = 0)
                    """,
                        用户id, 日期开始, 日期结束,
                    )

                elif 指标键 == "new_friends":
                    # 当日新增好友
                    结果 = await 连接.fetchrow(
                        """
                        SELECT COUNT(*) as 数量
                        FROM 微信好友表 f
                        JOIN 用户微信关联表 w ON f.我方微信号id = w.微信id
                        WHERE w.用户id = $1 AND w.状态 = 1
                        AND f.好友入库时间 BETWEEN $2 AND $3
                    """,
                        用户id, 日期开始, 日期结束,
                    )

                elif 指标键 == "friend_requests":
                    # 发送好友请求数（使用发送请求时间字段筛选）
                    结果 = await 连接.fetchrow(
                        """
                        SELECT COUNT(*) as 数量
                        FROM 微信好友表 f
                        JOIN 用户微信关联表 w ON f.我方微信号id = w.微信id
                        WHERE w.用户id = $1 AND w.状态 = 1
                        AND f.发送请求时间 BETWEEN $2 AND $3
                    """,
                        用户id, 日期开始, 日期结束,
                    )

                elif 指标键 == "stored_friends":
                    # 入库好友数（使用好友入库时间筛选）
                    结果 = await 连接.fetchrow(
                        """
                        SELECT COUNT(*) as 数量
                        FROM 微信好友表 f
                        JOIN 用户微信关联表 w ON f.我方微信号id = w.微信id
                        WHERE w.用户id = $1 AND w.状态 = 1
                        AND f.好友入库时间 BETWEEN $2 AND $3
                        AND f.好友入库时间 IS NOT NULL
                    """,
                        用户id, 日期开始, 日期结束,
                    )

                elif 指标键 == "communication_friends":
                    # 沟通好友数（好友已入库且我方在时间范围内有消息时间）
                    结果 = await 连接.fetchrow(
                        """
                        SELECT COUNT(*) as 数量
                        FROM 微信好友表 f
                        JOIN 用户微信关联表 w ON f.我方微信号id = w.微信id
                        WHERE w.用户id = $1 AND w.状态 = 1
                        AND f.好友入库时间 IS NOT NULL
                        AND f.我方最后一条消息发送时间 BETWEEN $2 AND $3
                    """,
                        用户id, 日期开始, 日期结束,
                    )

                elif 指标键 == "interaction_friends":
                    # 互动好友数（我方和对方都有消息时间）
                    结果 = await 连接.fetchrow(
                        """
                        SELECT COUNT(*) as 数量
                        FROM 微信好友表 f
                        JOIN 用户微信关联表 w ON f.我方微信号id = w.微信id
                        WHERE w.用户id = $1 AND w.状态 = 1
                        AND f.我方最后一条消息发送时间 IS NOT NULL
                        AND f.对方最后一条消息发送时间 IS NOT NULL
                    """,
                        用户id, 日期开始, 日期结束,
                    )
                else:
                    return 0

                return 结果["数量"] if 结果 else 0
        except Exception as e:
            系统日志器.error(f"获取微信指标数据失败: {str(e)}")
            raise e

    async def 获取_邀约指标_数据(
        self, 用户id: int, 指标键: str, 日期开始: datetime, 日期结束: datetime
    ) -> int:
        """获取邀约相关指标数据"""
        try:
            async with self.数据库连接池.获取连接() as 连接:
                if 指标键 == "invitation_count":
                    # 邀约数量（当日新增邀约数量）
                    结果 = await 连接.fetchrow(
                        """
                        SELECT COUNT(*) as 数量
                        FROM 用户抖音达人邀约记录表
                        WHERE 用户id = $1
                        AND 邀约发起时间 BETWEEN $2 AND $3
                    """,
                        用户id, 日期开始, 日期结束,
                    )

                elif 指标键 == "success_rate":
                    # 成功率（当日新增成功邀约数）
                    结果 = await 连接.fetchrow(
                        """
                        SELECT COUNT(*) as 数量
                        FROM 用户抖音达人邀约记录表
                        WHERE 用户id = $1
                        AND 邀约发起时间 BETWEEN $2 AND $3
                        AND 邀约状态 = '100'
                    """,
                        用户id, 日期开始, 日期结束,
                    )
                else:
                    return 0

                return 结果["数量"] if 结果 else 0
        except Exception as e:
            系统日志器.error(f"获取邀约指标数据失败: {str(e)}")
            raise e

    async def 获取_达人指标_数据(
        self, 用户id: int, 指标键: str, 日期开始: datetime, 日期结束: datetime
    ) -> int:
        """获取达人相关指标数据"""
        try:
            async with self.数据库连接池.获取连接() as 连接:
                if 指标键 == "talent_count":
                    # 达人数量（当日新增认领达人数）
                    结果 = await 连接.fetchrow(
                        """
                        SELECT COUNT(DISTINCT 达人id) as 数量
                        FROM 用户达人关联表
                        WHERE 用户id = $1 AND 状态 = 1
                        AND 认领时间 BETWEEN $2 AND $3
                    """,
                        用户id, 日期开始, 日期结束,
                    )

                elif 指标键 == "cooperation_count":
                    # 合作数量（当日新增邀约合作数）
                    结果 = await 连接.fetchrow(
                        """
                        SELECT COUNT(*) as 数量
                        FROM 用户抖音达人邀约记录表
                        WHERE 用户id = $1
                        AND 邀约发起时间 BETWEEN $2 AND $3
                        AND 邀约状态 IN ('100', '200', '300', '400')
                    """,
                        用户id, 日期开始, 日期结束,
                    )
                else:
                    return 0

                return 结果["数量"] if 结果 else 0
        except Exception as e:
            系统日志器.error(f"获取达人指标数据失败: {str(e)}")
            raise e

    async def 获取_微信时间范围_数据(self, 用户id: int) -> Dict[str, Any]:
        """获取微信时间范围数据"""
        try:
            async with self.数据库连接池.获取连接() as 连接:
                时间范围结果 = await 连接.fetchrow(
                    """
                    SELECT MIN(w.绑定时间) as 最早绑定时间, MAX(w.绑定时间) as 最晚绑定时间
                    FROM 用户微信关联表 w
                    WHERE w.用户id = $1 AND w.状态 = 1
                """,
                用户id,
                )
                return dict(时间范围结果) if 时间范围结果 else {}
        except Exception as e:
            系统日志器.error(f"获取微信时间范围数据失败: {str(e)}")
            raise e

    async def 验证并删除_邀约记录_数据(self, 邀约ID: int, 用户id: int) -> Dict[str, Any]:
        """验证并删除邀约记录"""
        try:
            async with self.数据库连接池.获取连接() as 连接:
                # 验证邀约记录是否属于当前用户
                验证查询 = """
                SELECT id, kol_nickname FROM 用户抖音达人邀约记录表
                WHERE id = $1 AND 用户id = $2
                """

                邀约记录 = await 连接.fetchrow(验证查询, 邀约ID, 用户id)

                if not 邀约记录:
                    return {"成功": False, "错误信息": "邀约记录不存在或无权限删除"}

                # 删除邀约记录
                删除查询 = """
                DELETE FROM 用户抖音达人邀约记录表
                WHERE id = $1 AND 用户id = $2
                """

                await 连接.execute(删除查询, 邀约ID, 用户id)

                return {
                    "成功": True,
                    "达人昵称": 邀约记录["kol_nickname"],
                    "邀约ID": 邀约ID,
                }
        except Exception as e:
            系统日志器.error(f"验证并删除邀约记录失败: {str(e)}")
            raise e

    async def 批量更新_达人联系方式_数据(
        self, 达人关联ids: List[int], 联系方式: str, 联系方式类型: str, 备注: str, 用户id: int
    ) -> Dict[str, Any]:
        """批量更新达人联系方式数据"""
        try:
            async with self.数据库连接池.获取连接() as 连接:
                # 验证达人关联记录是否属于当前用户
                占位符列表 = [f"${i+1}" for i in range(len(达人关联ids))]
                验证查询 = f"""
                SELECT id FROM 用户达人关联表
                WHERE id IN ({','.join(占位符列表)}) AND 用户id = ${len(达人关联ids)+1} AND 状态 = 1
                """

                有效记录 = await 连接.fetch(验证查询, *达人关联ids, 用户id)
                有效IDs = [记录["id"] for 记录 in 有效记录]

                if not 有效IDs:
                    return {
                        "成功数量": 0,
                        "失败数量": len(达人关联ids),
                        "错误信息": "没有找到有效的达人关联记录",
                    }

                成功数量 = 0

                # 为每个有效的关联记录更新或创建联系方式
                for 关联id in 有效IDs:
                    # 检查是否已存在联系方式记录
                    检查查询 = """
                    SELECT id FROM 用户达人补充信息表
                    WHERE 用户达人关联表id = $1
                    """

                    现有记录 = await 连接.fetchrow(检查查询, 关联id)

                    if 现有记录:
                        # 更新现有记录
                        更新查询 = """
                        UPDATE 用户达人补充信息表
                        SET 联系方式 = $1, 联系方式类型 = $2, 个人备注 = $3, 更新时间 = NOW()
                        WHERE 用户达人关联表id = $4
                        """

                        await 连接.execute(
                            更新查询, 联系方式, 联系方式类型, 备注, 关联id
                        )
                        成功数量 += 1
                    else:
                        # 创建新记录
                        插入查询 = """
                        INSERT INTO 用户达人补充信息表
                        (联系方式, 联系方式类型, 个人备注, 用户达人关联表id, 创建时间, 更新时间)
                        VALUES ($1, $2, $3, $4, NOW(), NOW())
                        """

                        await 连接.execute(
                            插入查询, 联系方式, 联系方式类型, 备注, 关联id
                        )
                        成功数量 += 1

                return {
                    "成功数量": 成功数量,
                    "失败数量": len(达人关联ids) - 成功数量,
                    "有效记录数": len(有效IDs),
                    "总请求数": len(达人关联ids),
                }
        except Exception as e:
            系统日志器.error(f"批量更新达人联系方式数据失败: {str(e)}")
            raise e

    async def 解除_达人关联_数据(self, 关联id: int, 解除原因: str, 用户id: int) -> Dict[str, Any]:
        """解除达人关联数据"""
        try:
            async with self.数据库连接池.获取连接() as 连接:
                # 验证关联记录是否属于当前用户
                验证查询 = """
                SELECT uda.id, uda.平台, uda.备注
                FROM 用户达人关联表 uda
                WHERE uda.id = $1 AND uda.用户id = $2 AND uda.状态 = 1
                """

                关联记录 = await 连接.fetchrow(验证查询, 关联id, 用户id)

                if not 关联记录:
                    return {"成功": False, "错误信息": "关联记录不存在或无权限操作"}

                # 更新关联状态为0（解除关联）并添加解除原因到备注
                from datetime import datetime
                当前时间 = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                新备注 = f"{关联记录['备注']} [用户解除关联于 {当前时间}，原因: {解除原因}]"

                更新查询 = """
                UPDATE 用户达人关联表
                SET 状态 = 0, 备注 = $1
                WHERE id = $2 AND 用户id = $3
                """

                await 连接.execute(更新查询, 新备注, 关联id, 用户id)

                return {
                    "成功": True,
                    "关联id": 关联id,
                    "平台": 关联记录["平台"],
                    "解除原因": 解除原因,
                }
        except Exception as e:
            系统日志器.error(f"解除达人关联数据失败: {str(e)}")
            raise e
