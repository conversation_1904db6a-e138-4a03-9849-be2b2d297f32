"""
异步微信数据访问层

负责微信相关的数据库操作，包括微信账号权限验证、微信好友信息更新等
遵循三层分离架构中的数据访问层职责
"""

from typing import Dict, Any, Optional
# PostgreSQL连接池导入
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志.日志配置 import 错误日志器


async def 异步数据_验证用户微信账号权限(用户id: int, 微信id: int) -> bool:
    """
    数据层：验证用户对指定微信账号的权限
    
    Args:
        用户id: 用户标识
        微信id: 微信账号ID
        
    Returns:
        bool: True表示有权限，False表示无权限
    """
    try:
        验证SQL = """
        SELECT 1 FROM 用户微信关联表
        WHERE 用户id = $1 AND 微信id = $2 AND 状态 = 1
        """

        验证结果 = await 异步连接池实例.执行查询(验证SQL, (用户id, 微信id))
        
        return len(验证结果) > 0
        
    except Exception as e:
        错误日志器.error(f"数据_验证用户微信账号权限失败: {str(e)}")
        return False


async def 异步数据_检查微信好友记录是否存在(我方微信id: int, 好友识别ID: str) -> bool:
    """
    数据层：检查微信好友记录是否存在
    
    Args:
        我方微信id: 我方微信账号ID
        好友识别ID: 好友识别ID
        
    Returns:
        bool: True表示记录存在，False表示不存在
    """
    try:
        检查SQL = """
        SELECT COUNT(*) as count FROM 微信好友表
        WHERE 我方微信号id = $1 AND 识别id = $2
        """
        
        检查结果 = await 异步连接池实例.执行查询(检查SQL, (我方微信id, 好友识别ID))
        
        记录数量 = 检查结果[0]['count'] if 检查结果 else 0
        
        return 记录数量 > 0
        
    except Exception as e:
        错误日志器.error(f"数据_检查微信好友记录是否存在失败: {str(e)}")
        return False


async def 异步数据_通过识别ID更新微信好友信息(
    我方微信id: int, 
    好友识别ID: str, 
    更新字段: Dict[str, Any]
) -> Dict[str, Any]:
    """
    数据层：通过识别ID更新微信好友信息
    
    Args:
        我方微信id: 我方微信账号ID
        好友识别ID: 好友识别ID
        更新字段: 需要更新的字段及其值的映射
        
    Returns:
        Dict[str, Any]: 包含影响行数等操作结果的字典
    """
    try:
        # 1. 先检查记录是否存在
        记录存在 = await 异步数据_检查微信好友记录是否存在(我方微信id, 好友识别ID)
        if not 记录存在:
            return {
                "影响行数": 0,
                "错误信息": "微信好友记录不存在"
            }
        
        # 2. 构建动态更新SQL
        字段映射 = {
            "是否失效": "是否失效",
            "发送请求时间": "发送请求时间", 
            "好友入库时间": "好友入库时间",
            "我方最后一条消息发送时间": "我方最后一条消息发送时间",
            "对方最后一条消息发送时间": "对方最后一条消息发送时间",
            "备注": "备注"
        }
        
        更新SQL片段 = []
        更新参数 = []
        参数索引 = 1

        for 字段名, 字段值 in 更新字段.items():
            if 字段名 in 字段映射:
                更新SQL片段.append(f"{字段映射[字段名]} = ${参数索引}")
                更新参数.append(字段值)
                参数索引 += 1

        if not 更新SQL片段:
            return {
                "影响行数": 0,
                "错误信息": "没有有效的更新字段"
            }

        # 3. 执行更新操作
        更新参数.extend([我方微信id, 好友识别ID])
        更新SQL = f"""
        UPDATE 微信好友表
        SET {', '.join(更新SQL片段)}
        WHERE 我方微信号id = $1 AND 识别id = $2
        """

        影响行数 = await 异步连接池实例.执行更新(更新SQL, tuple(更新参数))
        
        return {
            "影响行数": 影响行数,
            "更新字段数": len(更新SQL片段)
        }
        
    except Exception as e:
        错误日志器.error(f"数据_通过识别ID更新微信好友信息失败: {str(e)}")
        return {
            "影响行数": 0,
            "错误信息": f"数据库操作失败: {str(e)}"
        }


async def 异步数据_获取微信好友详细信息_通过识别ID(我方微信id: int, 好友识别ID: str) -> Optional[Dict[str, Any]]:
    """
    数据层：通过识别ID获取微信好友详细信息
    
    Args:
        我方微信id: 我方微信账号ID
        好友识别ID: 好友识别ID
        
    Returns:
        Optional[Dict[str, Any]]: 好友详细信息，如果不存在则返回None
    """
    try:
        查询SQL = """
        SELECT 
            id,
            我方微信号id,
            对方微信号id,
            识别id,
            是否失效,
            发送请求时间,
            好友入库时间,
            我方最后一条消息发送时间,
            对方最后一条消息发送时间,
            备注,
            创建时间,
            更新时间
        FROM 微信好友表
        WHERE 我方微信号id = $1 AND 识别id = $2
        """
        
        查询结果 = await 异步连接池实例.执行查询(查询SQL, (我方微信id, 好友识别ID))
        
        if 查询结果:
            return 查询结果[0]
        
        return None
        
    except Exception as e:
        错误日志器.error(f"数据_获取微信好友详细信息_通过识别ID失败: {str(e)}")
        return None


async def 异步数据_获取用户绑定的微信账号列表(用户id: int) -> list:
    """
    数据层：获取用户绑定的微信账号列表
    
    Args:
        用户id: 用户标识
        
    Returns:
        list: 用户绑定的微信账号列表
    """
    try:
        查询SQL = """
        SELECT 
            w.id as 微信id,
            w.微信号,
            w.昵称,
            w.绑定手机号,
            w.头像,
            uw.绑定时间,
            uw.状态,
            uw.备注
        FROM 用户微信关联表 uw
        INNER JOIN 微信信息表 w ON uw.微信id = w.id
        WHERE uw.用户id = $1 AND uw.状态 = 1
        ORDER BY uw.绑定时间 DESC
        """
        
        查询结果 = await 异步连接池实例.执行查询(查询SQL, (用户id,))
        
        return 查询结果 or []
        
    except Exception as e:
        错误日志器.error(f"数据_获取用户绑定的微信账号列表失败: {str(e)}")
        return [] 