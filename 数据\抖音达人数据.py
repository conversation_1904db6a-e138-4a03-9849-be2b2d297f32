"""
抖音达人数据操作
基于asyncpg实现的抖音达人数据访问层

特性：
1. 使用PostgreSQL原生语法和特性
2. 支持高效的文本搜索和模糊匹配
3. 使用$1, $2参数占位符，防止SQL注入
4. 优化的分页查询和排序
5. 智能的搜索策略（UID、抖音号、昵称）
6. 完整的达人认领管理功能
7. 优化的头像处理和默认值

性能优化建议 - 推荐创建以下数据库索引：
-- 基础查询索引
CREATE INDEX IF NOT EXISTS idx_talent_basic ON 达人表(昵称, uid_number) WHERE 昵称 IS NOT NULL AND uid_number IS NOT NULL;
-- 搜索优化索引
CREATE INDEX IF NOT EXISTS idx_talent_search ON 达人表 USING gin(昵称 gin_trgm_ops, account_douyin gin_trgm_ops);
-- 排序优化索引
CREATE INDEX IF NOT EXISTS idx_talent_update_time ON 达人表(update_time DESC);
-- 粉丝数筛选索引
CREATE INDEX IF NOT EXISTS idx_talent_fans ON 达人表(粉丝数) WHERE 粉丝数 > 0;
-- 认领状态查询索引
CREATE INDEX IF NOT EXISTS idx_claim_talent ON 用户达人关联表(达人id, 平台, 状态) WHERE 状态 = 1;
"""

import re
import time
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 数据库日志器, 错误日志器


def _是UID格式(文本: str) -> bool:
    """检查文本是否为UID格式（纯数字，长度合理）"""
    return 文本.isdigit() and 6 <= len(文本) <= 20


def _是抖音号格式(文本: str) -> bool:
    """检查文本是否为抖音号格式"""
    # 抖音号通常以字母开头，包含字母、数字、下划线、点号
    return bool(re.match(r'^[a-zA-Z][a-zA-Z0-9._-]*$', 文本)) and len(文本) >= 3


async def 搜索达人(
    关键词: Optional[str] = None,
    uid: Optional[str] = None,
    页码: int = 1,
    每页数量: int = 20,
    排序字段: str = "id",
    排序方向: str = "DESC"
) -> Tuple[List[Dict[str, Any]], int]:
    """
    搜索达人信息

    Args:
        关键词: 搜索关键词（支持UID、抖音号、昵称）
        uid: 精确的UID查询
        页码: 页码（从1开始）
        每页数量: 每页记录数
        排序字段: 排序字段
        排序方向: 排序方向（ASC/DESC）

    Returns:
        Tuple[达人列表, 总数量]
    """
    try:
        # 构建WHERE条件
        where_conditions = []
        params = []
        param_count = 0

        if uid:
            param_count += 1
            where_conditions.append(f"uid_number = ${param_count}")
            params.append(uid)
        elif 关键词:
            关键词 = 关键词.strip()
            if 关键词:
                if _是UID格式(关键词):
                    # UID搜索
                    param_count += 1
                    where_conditions.append(f"uid_number = ${param_count}")
                    params.append(关键词)
                elif _是抖音号格式(关键词):
                    # 抖音号搜索
                    param_count += 1
                    where_conditions.append(f"account_douyin ILIKE ${param_count}")
                    params.append(f"%{关键词}%")
                else:
                    # 昵称搜索
                    param_count += 1
                    where_conditions.append(f"昵称 ILIKE ${param_count}")
                    params.append(f"%{关键词}%")

        # 构建WHERE子句
        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

        # 验证排序字段
        valid_sort_fields = ["id", "uid_number", "昵称", "account_douyin", "粉丝数", "关注数", "update_time"]
        if 排序字段 not in valid_sort_fields:
            排序字段 = "update_time"

        # 验证排序方向
        排序方向 = 排序方向.upper()
        if 排序方向 not in ["ASC", "DESC"]:
            排序方向 = "DESC"

        # 计算偏移量
        offset = (页码 - 1) * 每页数量

        # 查询总数
        count_sql = f"""
        SELECT COUNT(*) as total
        FROM 达人表
        WHERE {where_clause}
        """
        count_result = await 异步连接池实例.执行查询(count_sql, params)
        总数量 = count_result[0]["total"] if count_result else 0

        # 查询数据
        data_sql = f"""
        SELECT 
            id, uid_number, 昵称, account_douyin, avatar,
            粉丝数, 关注数, introduction, update_time,
            sec_uid, 接口状态_UID
        FROM 达人表
        WHERE {where_clause}
        ORDER BY {排序字段} {排序方向}
        LIMIT ${param_count + 1} OFFSET ${param_count + 2}
        """
        params.extend([每页数量, offset])

        达人列表 = await 异步连接池实例.执行查询(data_sql, params)

        数据库日志器.info(f"搜索达人完成: 关键词={关键词}, UID={uid}, 页码={页码}, 总数={总数量}")
        return 达人列表, 总数量

    except Exception as e:
        错误日志器.error(f"搜索达人失败: {str(e)}")
        raise


async def 获取达人详情(达人id: int) -> Optional[Dict[str, Any]]:
    """
    获取达人详细信息

    Args:
        达人id: 达人id

    Returns:
        达人详细信息字典，如果不存在则返回None
    """
    try:
        sql = """
        SELECT 
            id, uid_number, 昵称, account_douyin, avatar,
            粉丝数, 关注数, introduction, update_time,
            sec_uid, 接口状态_UID
        FROM 达人表
        WHERE id = $1
        LIMIT 1
        """
        
        结果 = await 异步连接池实例.执行查询(sql, (达人id,))
        
        if 结果:
            数据库日志器.info(f"获取达人详情成功: 达人id={达人id}")
            return 结果[0]
        else:
            数据库日志器.warning(f"达人不存在: 达人id={达人id}")
            return None
            
    except Exception as e:
        错误日志器.error(f"获取达人详情失败: 达人id={达人id}, 错误={str(e)}")
        raise


async def 通过UID获取达人(uid_number: str) -> Optional[Dict[str, Any]]:
    """
    通过UID获取达人信息

    Args:
        uid_number: 达人UID

    Returns:
        达人信息字典，如果不存在则返回None
    """
    try:
        sql = """
        SELECT 
            id, uid_number, 昵称, account_douyin, avatar,
            粉丝数, 关注数, introduction, update_time,
            sec_uid, 接口状态_UID
        FROM 达人表
        WHERE uid_number = $1
        LIMIT 1
        """
        
        结果 = await 异步连接池实例.执行查询(sql, (uid_number,))
        
        if 结果:
            数据库日志器.info(f"通过UID获取达人成功: UID={uid_number}")
            return 结果[0]
        else:
            数据库日志器.warning(f"达人不存在: UID={uid_number}")
            return None
            
    except Exception as e:
        错误日志器.error(f"通过UID获取达人失败: UID={uid_number}, 错误={str(e)}")
        raise


async def 创建或更新达人(
    uid_number: str,
    account_douyin: Optional[str] = None,
    昵称: Optional[str] = None,
    avatar: Optional[str] = None,
    粉丝数: Optional[int] = None,
    关注数: Optional[int] = None,
    introduction: Optional[str] = None,
    sec_uid: Optional[str] = None
) -> Dict[str, Any]:
    """
    创建或更新达人信息

    Args:
        uid_number: 达人UID（必需）
        account_douyin: 抖音号
        昵称: 昵称
        avatar: 头像URL
        粉丝数: 粉丝数量
        关注数: 关注数量
        introduction: 简介
        sec_uid: SecUID

    Returns:
        操作结果字典
    """
    try:
        当前时间戳 = int(time.time())
        
        # 检查达人是否已存在
        现有达人 = await 通过UID获取达人(uid_number)
        
        if 现有达人:
            # 更新现有达人
            update_fields = []
            params = []
            param_count = 0
            
            if account_douyin is not None:
                param_count += 1
                update_fields.append(f"account_douyin = ${param_count}")
                params.append(account_douyin)
                
            if 昵称 is not None:
                param_count += 1
                update_fields.append(f"昵称 = ${param_count}")
                params.append(昵称)
                
            if avatar is not None:
                param_count += 1
                update_fields.append(f"avatar = ${param_count}")
                params.append(avatar)
                
            if 粉丝数 is not None:
                param_count += 1
                update_fields.append(f"粉丝数 = ${param_count}")
                params.append(粉丝数)
                
            if 关注数 is not None:
                param_count += 1
                update_fields.append(f"关注数 = ${param_count}")
                params.append(关注数)
                
            if introduction is not None:
                param_count += 1
                update_fields.append(f"introduction = ${param_count}")
                params.append(introduction)
                
            if sec_uid is not None:
                param_count += 1
                update_fields.append(f"sec_uid = ${param_count}")
                params.append(sec_uid)
            
            # 总是更新时间戳
            param_count += 1
            update_fields.append(f"update_time = ${param_count}")
            params.append(当前时间戳)
            
            # 添加WHERE条件的参数
            param_count += 1
            params.append(uid_number)
            
            if update_fields:
                update_sql = f"""
                UPDATE 达人表
                SET {', '.join(update_fields)}
                WHERE uid_number = ${param_count}
                """
                
                await 异步连接池实例.执行更新(update_sql, params)
                数据库日志器.info(f"更新达人成功: UID={uid_number}")
                
            return {
                "状态": "成功",
                "操作": "更新",
                "达人id": 现有达人["id"],
                "UID": uid_number
            }
        else:
            # 创建新达人
            insert_sql = """
            INSERT INTO 达人表 
            (uid_number, account_douyin, 昵称, avatar, 粉丝数, 关注数, introduction, sec_uid, update_time)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            RETURNING id
            """
            
            结果 = await 异步连接池实例.执行查询(
                insert_sql,
                (uid_number, account_douyin, 昵称, avatar, 粉丝数, 关注数, introduction, sec_uid, 当前时间戳)
            )
            
            新达人id = 结果[0]["id"] if 结果 else None
            数据库日志器.info(f"创建达人成功: UID={uid_number}, 达人id={新达人id}")
            
            return {
                "状态": "成功",
                "操作": "创建",
                "达人id": 新达人id,
                "UID": uid_number
            }
            
    except Exception as e:
        错误日志器.error(f"创建或更新达人失败: UID={uid_number}, 错误={str(e)}")
        raise


async def 批量获取达人信息(uid_list: List[str]) -> List[Dict[str, Any]]:
    """
    批量获取达人信息

    Args:
        uid_list: UID列表

    Returns:
        达人信息列表
    """
    try:
        if not uid_list:
            return []
            
        # 构建IN查询的占位符
        placeholders = ', '.join([f'${i+1}' for i in range(len(uid_list))])
        
        sql = f"""
        SELECT 
            id, uid_number, 昵称, account_douyin, avatar,
            粉丝数, 关注数, introduction, update_time,
            sec_uid, 接口状态_UID
        FROM 达人表
        WHERE uid_number IN ({placeholders})
        ORDER BY id
        """
        
        结果 = await 异步连接池实例.执行查询(sql, uid_list)
        数据库日志器.info(f"批量获取达人信息成功: 请求{len(uid_list)}个, 返回{len(结果)}个")
        
        return 结果
        
    except Exception as e:
        错误日志器.error(f"批量获取达人信息失败: {str(e)}")
        raise


async def 统计达人数量(筛选条件: Optional[Dict[str, Any]] = None) -> int:
    """
    统计达人数量

    Args:
        筛选条件: 可选的筛选条件

    Returns:
        达人总数量
    """
    try:
        where_conditions = []
        params = []
        param_count = 0
        
        if 筛选条件:
            if "最小粉丝数" in 筛选条件:
                param_count += 1
                where_conditions.append(f"粉丝数 >= ${param_count}")
                params.append(筛选条件["最小粉丝数"])
                
            if "最大粉丝数" in 筛选条件:
                param_count += 1
                where_conditions.append(f"粉丝数 <= ${param_count}")
                params.append(筛选条件["最大粉丝数"])
        
        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
        
        sql = f"""
        SELECT COUNT(*) as total
        FROM 达人表
        WHERE {where_clause}
        """
        
        结果 = await 异步连接池实例.执行查询(sql, params)
        总数量 = 结果[0]["total"] if 结果 else 0
        
        数据库日志器.info(f"统计达人数量: {总数量}")
        return 总数量
        
    except Exception as e:
        错误日志器.error(f"统计达人数量失败: {str(e)}")
        raise


# ==================== 达人认领管理功能 ====================

async def 异步认领达人(用户id: int, 达人id: int) -> bool:
    """
    认领达人

    Args:
        用户id: 用户id
        达人id: 达人id

    Returns:
        认领是否成功
    """
    try:
        # 检查达人是否存在
        达人信息 = await 获取达人详情(达人id)
        if not 达人信息:
            raise ValueError(f"达人不存在: 达人id={达人id}")

        # 检查是否已经被认领
        检查SQL = """
        SELECT id FROM 用户达人关联表
        WHERE 达人id = $1 AND 平台 = '抖音' AND 状态 = 1
        LIMIT 1
        """
        现有认领 = await 异步连接池实例.执行查询(检查SQL, (达人id,))

        if 现有认领:
            raise ValueError(f"达人已被认领: 达人id={达人id}")

        # 检查用户是否已经认领过这个达人
        用户认领检查SQL = """
        SELECT id FROM 用户达人关联表
        WHERE 用户id = $1 AND 达人id = $2 AND 平台 = '抖音'
        LIMIT 1
        """
        用户现有认领 = await 异步连接池实例.执行查询(用户认领检查SQL, (用户id, 达人id))

        当前时间 = datetime.now()

        if 用户现有认领:
            # 更新现有记录为有效状态
            更新SQL = """
            UPDATE 用户达人关联表
            SET 状态 = 1, 认领时间 = $1
            WHERE 用户id = $2 AND 达人id = $3 AND 平台 = '抖音'
            """
            await 异步连接池实例.执行更新(更新SQL, (当前时间, 用户id, 达人id))
        else:
            # 创建新的认领记录
            插入SQL = """
            INSERT INTO 用户达人关联表 (用户id, 达人id, 平台, 状态, 认领时间)
            VALUES ($1, $2, '抖音', 1, $3)
            """
            await 异步连接池实例.执行插入(插入SQL, (用户id, 达人id, 当前时间))

        数据库日志器.info(f"认领达人成功: 用户id={用户id}, 达人id={达人id}")
        return True

    except Exception as e:
        错误日志器.error(f"认领达人失败: 用户id={用户id}, 达人id={达人id}, 错误={str(e)}")
        raise


async def 异步取消认领达人(用户id: int, 达人id: int) -> bool:
    """
    取消认领达人

    Args:
        用户id: 用户id
        达人id: 达人id

    Returns:
        取消认领是否成功
    """
    try:
        # 检查用户是否认领了这个达人
        检查SQL = """
        SELECT id FROM 用户达人关联表
        WHERE 用户id = $1 AND 达人id = $2 AND 平台 = '抖音' AND 状态 = 1
        LIMIT 1
        """
        现有认领 = await 异步连接池实例.执行查询(检查SQL, (用户id, 达人id))

        if not 现有认领:
            raise ValueError(f"用户未认领此达人: 用户id={用户id}, 达人id={达人id}")

        # 更新认领状态为无效
        更新SQL = """
        UPDATE 用户达人关联表
        SET 状态 = 0
        WHERE 用户id = $1 AND 达人id = $2 AND 平台 = '抖音' AND 状态 = 1
        """
        影响行数 = await 异步连接池实例.执行更新(更新SQL, (用户id, 达人id))

        if 影响行数 == 0:
            raise ValueError(f"取消认领失败: 用户id={用户id}, 达人id={达人id}")

        数据库日志器.info(f"取消认领达人成功: 用户id={用户id}, 达人id={达人id}")
        return True

    except Exception as e:
        错误日志器.error(f"取消认领达人失败: 用户id={用户id}, 达人id={达人id}, 错误={str(e)}")
        raise


async def 异步获取用户认领达人列表(
    用户id: int,
    页码: int = 1,
    每页数量: int = 20,
    排序字段: str = "认领时间",
    排序方式: str = "desc"
) -> Dict[str, Any]:
    """
    获取用户认领的达人列表

    Args:
        用户id: 用户id
        页码: 页码
        每页数量: 每页数量
        排序字段: 排序字段
        排序方式: 排序方式

    Returns:
        包含达人列表和分页信息的字典
    """
    try:
        # 计算偏移量
        offset = (页码 - 1) * 每页数量

        # 验证排序字段
        valid_sort_fields = ["认领时间", "粉丝数", "昵称", "抖音号", "抖音UID"]
        if 排序字段 not in valid_sort_fields:
            排序字段 = "认领时间"

        # 映射排序字段到数据库字段
        sort_field_mapping = {
            "认领时间": "uda.认领时间",
            "粉丝数": "d.粉丝数",
            "昵称": "d.昵称",
            "抖音号": "d.account_douyin",
            "抖音UID": "d.uid_number"
        }
        db_sort_field = sort_field_mapping.get(排序字段, "uda.认领时间")

        # 验证排序方式
        排序方式 = 排序方式.upper()
        if 排序方式 not in ["ASC", "DESC"]:
            排序方式 = "DESC"

        # 查询总数
        count_sql = """
        SELECT COUNT(*) as total
        FROM 用户达人关联表 uda
        INNER JOIN 达人表 d ON uda.达人id = d.id
        WHERE uda.用户id = $1 AND uda.平台 = '抖音' AND uda.状态 = 1
        """
        count_result = await 异步连接池实例.执行查询(count_sql, (用户id,))
        总数量 = count_result[0]["total"] if count_result else 0

        # 查询数据
        data_sql = f"""
        SELECT
            d.id, d.uid_number, d.昵称, d.account_douyin, d.avatar,
            d.粉丝数, d.关注数, d.introduction, d.update_time,
            uda.认领时间, uda.id as 关联id
        FROM 用户达人关联表 uda
        INNER JOIN 达人表 d ON uda.达人id = d.id
        WHERE uda.用户id = $1 AND uda.平台 = '抖音' AND uda.状态 = 1
        ORDER BY {db_sort_field} {排序方式}
        LIMIT $2 OFFSET $3
        """

        达人列表 = await 异步连接池实例.执行查询(data_sql, (用户id, 每页数量, offset))

        数据库日志器.info(f"获取用户认领达人列表成功: 用户id={用户id}, 页码={页码}, 总数={总数量}")

        return {
            "达人列表": 达人列表,
            "总数": 总数量,
            "当前页": 页码,
            "每页数量": 每页数量,
            "总页数": (总数量 + 每页数量 - 1) // 每页数量
        }

    except Exception as e:
        错误日志器.error(f"获取用户认领达人列表失败: 用户id={用户id}, 错误={str(e)}")
        raise


async def 异步搜索用户认领达人列表(
    用户id: int,
    页码: int = 1,
    每页数量: int = 20,
    排序字段: str = "认领时间",
    排序方式: str = "desc",
    筛选条件: Optional[Dict[str, Any]] = None,
    关键词: Optional[str] = None
) -> Dict[str, Any]:
    """
    搜索用户认领的达人列表，支持筛选和关键词搜索

    Args:
        用户id: 用户id
        页码: 页码
        每页数量: 每页数量
        排序字段: 排序字段
        排序方式: 排序方式
        筛选条件: 筛选条件（如粉丝数范围等）
        关键词: 搜索关键词

    Returns:
        包含达人列表和分页信息的字典
    """
    try:
        # 构建WHERE条件
        where_conditions = ["uda.用户id = $1", "uda.平台 = '抖音'", "uda.状态 = 1"]
        params = [用户id]
        param_count = 1

        # 添加关键词搜索条件
        if 关键词:
            关键词 = 关键词.strip()
            if 关键词:
                param_count += 1
                where_conditions.append(f"(d.昵称 ILIKE ${param_count} OR d.account_douyin ILIKE ${param_count} OR d.uid_number ILIKE ${param_count})")
                params.append(f"%{关键词}%")

        # 添加筛选条件
        if 筛选条件:
            if "最小粉丝数" in 筛选条件:
                param_count += 1
                where_conditions.append(f"d.粉丝数 >= ${param_count}")
                params.append(筛选条件["最小粉丝数"])

            if "最大粉丝数" in 筛选条件:
                param_count += 1
                where_conditions.append(f"d.粉丝数 <= ${param_count}")
                params.append(筛选条件["最大粉丝数"])

        where_clause = " AND ".join(where_conditions)

        # 验证排序字段
        valid_sort_fields = ["认领时间", "粉丝数", "昵称", "抖音号", "抖音UID"]
        if 排序字段 not in valid_sort_fields:
            排序字段 = "认领时间"

        # 映射排序字段到数据库字段
        sort_field_mapping = {
            "认领时间": "uda.认领时间",
            "粉丝数": "d.粉丝数",
            "昵称": "d.昵称",
            "抖音号": "d.account_douyin",
            "抖音UID": "d.uid_number"
        }
        db_sort_field = sort_field_mapping.get(排序字段, "uda.认领时间")

        # 验证排序方式
        排序方式 = 排序方式.upper()
        if 排序方式 not in ["ASC", "DESC"]:
            排序方式 = "DESC"

        # 计算偏移量
        offset = (页码 - 1) * 每页数量

        # 查询总数
        count_sql = f"""
        SELECT COUNT(*) as total
        FROM 用户达人关联表 uda
        INNER JOIN 达人表 d ON uda.达人id = d.id
        WHERE {where_clause}
        """
        count_result = await 异步连接池实例.执行查询(count_sql, params)
        总数量 = count_result[0]["total"] if count_result else 0

        # 查询数据
        data_sql = f"""
        SELECT
            d.id, d.uid_number, d.昵称, d.account_douyin, d.avatar,
            d.粉丝数, d.关注数, d.introduction, d.update_time,
            uda.认领时间, uda.id as 关联id
        FROM 用户达人关联表 uda
        INNER JOIN 达人表 d ON uda.达人id = d.id
        WHERE {where_clause}
        ORDER BY {db_sort_field} {排序方式}
        LIMIT ${param_count + 1} OFFSET ${param_count + 2}
        """
        params.extend([每页数量, offset])

        达人列表 = await 异步连接池实例.执行查询(data_sql, params)

        数据库日志器.info(f"搜索用户认领达人列表成功: 用户id={用户id}, 关键词={关键词}, 总数={总数量}")

        return {
            "达人列表": 达人列表,
            "总数": 总数量,
            "当前页": 页码,
            "每页数量": 每页数量,
            "总页数": (总数量 + 每页数量 - 1) // 每页数量
        }

    except Exception as e:
        错误日志器.error(f"搜索用户认领达人列表失败: 用户id={用户id}, 错误={str(e)}")
        raise


async def 异步获取达人列表(
    页码: int = 1,
    每页数量: int = 20,
    最后ID: int = 0,
    筛选条件: Optional[Dict[str, Any]] = None,
    有联系方式: Optional[bool] = None,
    关键词: Optional[str] = None,
    当前用户id: Optional[int] = None,
    当前团队id: Optional[int] = None,  # 保留参数以兼容现有调用
    uid: Optional[str] = None
) -> Dict[str, Any]:
    """
    获取达人公海列表（优化版）

    优化特性：
    1. 移除团队认领状态限制，显示所有达人
    2. 自动处理空头像，提供默认头像
    3. 添加认领状态标识
    4. 优化查询性能，使用LEFT JOIN
    5. 放宽数据过滤条件，确保数据完整性

    Args:
        页码: 页码
        每页数量: 每页数量
        最后ID: 上一页最后的达人id（暂未使用）
        筛选条件: 筛选条件（粉丝数范围等）
        有联系方式: 是否有联系方式
        关键词: 搜索关键词（昵称、抖音号）
        当前用户id: 当前用户id（暂未使用）
        当前团队id: 当前团队id（保留兼容性，不影响查询）
        uid: 精准查询的uid

    Returns:
        包含达人列表和分页信息的字典，每个达人包含认领状态和处理后的头像
    """
    try:
        # 设置默认排序参数 - 使用ID排序提升性能
        排序字段 = "id"
        排序方向 = "ASC"

        # 构建WHERE条件 - 只显示未被认领的达人
        where_conditions = []
        params = []
        param_count = 0

        # 处理精准UID查询
        if uid:
            param_count += 1
            where_conditions.append(f"d.uid_number = ${param_count}")
            params.append(uid)

        # 处理关键词搜索
        if 关键词:
            param_count += 1
            where_conditions.append(f"(d.昵称 ILIKE ${param_count} OR d.account_douyin ILIKE ${param_count})")
            params.append(f"%{关键词}%")

        # 添加筛选条件
        if 筛选条件:
            if "最小粉丝数" in 筛选条件:
                param_count += 1
                where_conditions.append(f"d.粉丝数 >= ${param_count}")
                params.append(筛选条件["最小粉丝数"])

            if "最大粉丝数" in 筛选条件:
                param_count += 1
                where_conditions.append(f"d.粉丝数 <= ${param_count}")
                params.append(筛选条件["最大粉丝数"])

        # 处理联系方式筛选
        if 有联系方式 is not None:
            if 有联系方式:
                # 只显示有联系方式的达人
                where_conditions.append("""
                d.id IN (
                    SELECT DISTINCT uda.达人id
                    FROM 用户达人关联表 uda
                    JOIN 用户达人补充信息表 udsi ON uda.id = udsi.用户达人关联表id
                    WHERE uda.平台 = '抖音' AND udsi.联系方式 IS NOT NULL AND TRIM(udsi.联系方式) != ''
                )
                """)
            else:
                # 只显示没有联系方式的达人
                where_conditions.append("""
                d.id NOT IN (
                    SELECT DISTINCT uda.达人id
                    FROM 用户达人关联表 uda
                    JOIN 用户达人补充信息表 udsi ON uda.id = udsi.用户达人关联表id
                    WHERE uda.平台 = '抖音' AND udsi.联系方式 IS NOT NULL AND TRIM(udsi.联系方式) != ''
                )
                """)

        # 简化的基础WHERE条件：达人公海不考虑团队和认领状态
        base_where = """
        d.昵称 IS NOT NULL AND TRIM(d.昵称) != ''
        AND d.account_douyin IS NOT NULL AND TRIM(d.account_douyin) != ''
        """

        if where_conditions:
            where_clause = base_where + " AND " + " AND ".join(where_conditions)
        else:
            where_clause = base_where

        # 验证排序字段
        valid_sort_fields = ["id", "uid_number", "昵称", "account_douyin", "粉丝数", "关注数"]
        if 排序字段 not in valid_sort_fields:
            排序字段 = "id"

        # 验证排序方向
        排序方向 = 排序方向.upper()
        if 排序方向 not in ["ASC", "DESC"]:
            排序方向 = "ASC"

        # 计算偏移量
        offset = (页码 - 1) * 每页数量

        # 优化：只在第一页时查询总数，减少性能开销
        if 页码 == 1:
            count_sql = f"""
            SELECT COUNT(*) as total
            FROM 达人表 d
            WHERE {where_clause}
            """
            count_result = await 异步连接池实例.执行查询(count_sql, params)
            总数量 = count_result[0]["total"] if count_result else 0
        else:
            # 非第一页使用估算值，避免重复 COUNT 查询
            总数量 = (页码 - 1) * 每页数量 + 每页数量

        # 优化的数据查询：减少字段，优化 JOIN
        data_sql = f"""
        SELECT
            d.id, d.uid_number, d.昵称, d.account_douyin,
            COALESCE(NULLIF(TRIM(d.avatar), ''), '/images/default_avatar.svg') as avatar,
            d.粉丝数, d.关注数, d.introduction, d.update_time,
            d.sec_uid, d.接口状态_UID,
            EXISTS (
                SELECT 1 FROM 用户达人关联表 uda
                WHERE uda.达人id = d.id AND uda.平台 = '抖音' AND uda.状态 = 1
            ) as 已认领
        FROM 达人表 d
        WHERE {where_clause}
        ORDER BY d.{排序字段} {排序方向}
        LIMIT ${param_count + 1} OFFSET ${param_count + 2}
        """
        params.extend([每页数量, offset])

        达人列表 = await 异步连接池实例.执行查询(data_sql, params)

        # 后处理：确保数据完整性
        for 达人 in 达人列表:
            # 确保头像字段存在
            if not 达人.get('avatar'):
                达人['avatar'] = '/images/default_avatar.svg'

            # 确保粉丝数为数字
            if 达人.get('粉丝数') is None:
                达人['粉丝数'] = 0

        数据库日志器.info(f"获取达人公海列表成功: 页码={页码}, 总数={总数量}, 返回={len(达人列表)}条")

        return {
            "达人列表": 达人列表,
            "总数": 总数量,
            "当前页": 页码,
            "每页数量": 每页数量,
            "总页数": (总数量 + 每页数量 - 1) // 每页数量,
            "最后ID": 达人列表[-1]["id"] if 达人列表 else 0,
            "下一页最后ID": 达人列表[-1]["id"] if len(达人列表) == 每页数量 else None
        }

    except Exception as e:
        错误日志器.error(f"获取达人列表失败: 错误={str(e)}")
        raise


async def 异步更新达人信息(达人id: int, 更新数据: Dict[str, Any]) -> Dict[str, Any]:
    """
    更新达人信息

    Args:
        达人id: 达人id
        更新数据: 要更新的数据字典

    Returns:
        更新结果字典
    """
    try:
        # 检查达人是否存在
        达人信息 = await 获取达人详情(达人id)
        if not 达人信息:
            raise ValueError(f"达人不存在: 达人id={达人id}")

        # 构建更新字段
        update_fields = []
        params = []
        param_count = 0

        # 允许更新的字段
        allowed_fields = {
            "昵称": "昵称",
            "account_douyin": "account_douyin",
            "avatar": "avatar",
            "粉丝数": "粉丝数",
            "关注数": "关注数",
            "introduction": "introduction",
            "sec_uid": "sec_uid",
            "接口状态_UID": "接口状态_UID"
        }

        for field, db_field in allowed_fields.items():
            if field in 更新数据:
                param_count += 1
                update_fields.append(f"{db_field} = ${param_count}")
                params.append(更新数据[field])

        if not update_fields:
            return {"状态": "成功", "消息": "没有需要更新的字段", "达人id": 达人id}

        # 总是更新时间戳
        param_count += 1
        update_fields.append(f"update_time = ${param_count}")
        params.append(int(time.time()))

        # 添加WHERE条件的参数
        param_count += 1
        params.append(达人id)

        # 执行更新
        update_sql = f"""
        UPDATE 达人表
        SET {', '.join(update_fields)}
        WHERE id = ${param_count}
        """

        影响行数 = await 异步连接池实例.执行更新(update_sql, params)

        if 影响行数 == 0:
            raise ValueError(f"更新失败，可能达人不存在: 达人id={达人id}")

        数据库日志器.info(f"更新达人信息成功: 达人id={达人id}, 更新字段={list(更新数据.keys())}")

        return {
            "状态": "成功",
            "消息": "成功更新达人信息",
            "达人id": 达人id,
            "更新字段": list(更新数据.keys())
        }

    except Exception as e:
        错误日志器.error(f"更新达人信息失败: 达人id={达人id}, 错误={str(e)}")
        raise


async def 异步查询或更新达人(uid_number: str) -> Dict[str, Any]:
    """
    查询或更新达人信息（通过外部API）

    Args:
        uid_number: 达人UID

    Returns:
        操作结果字典
    """
    try:
        # 首先检查达人是否存在
        现有达人 = await 通过UID获取达人(uid_number)

        if 现有达人:
            # 这里可以添加调用外部API更新达人信息的逻辑
            # 目前先返回现有信息
            数据库日志器.info(f"查询达人成功: UID={uid_number}")
            return {
                "状态": "成功",
                "操作": "查询",
                "达人信息": 现有达人,
                "消息": "查询达人成功",
                "达人id": 现有达人["id"],
                "UID": uid_number
            }
        else:
            # 达人不存在，可以在这里添加从外部API获取并创建达人的逻辑
            数据库日志器.warning(f"达人不存在: UID={uid_number}")
            return {
                "状态": "失败",
                "操作": "查询",
                "消息": f"达人不存在: UID={uid_number}",
                "UID": uid_number
            }

    except Exception as e:
        错误日志器.error(f"查询或更新达人失败: UID={uid_number}, 错误={str(e)}")
        raise



