"""
用户微信配置数据访问层

负责用户微信添加配置表的所有数据库操作
包括配置的创建、查询、更新和删除功能
"""


from typing import Dict, Any, Optional
from datetime import datetime
import 状态
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 错误日志器, 系统日志器

# 简单的内存缓存，避免频繁查询配置
配置缓存 = {}
缓存过期时间 = 300  # 5分钟缓存

# 默认配置缓存
默认配置缓存 = {}
默认配置缓存过期时间 = 3600  # 1小时缓存


async def 获取默认配置值() -> Dict[str, Any]:
    """
    从用户配置表的第一条记录获取默认配置值

    返回：
        Dict[str, Any]: 默认配置值字典
    """
    当前时间 = datetime.now()
    缓存键 = "default_config"

    # 检查缓存
    if 缓存键 in 默认配置缓存:
        缓存数据 = 默认配置缓存[缓存键]
        if (当前时间 - 缓存数据["缓存时间"]).total_seconds() < 默认配置缓存过期时间:
            return 缓存数据["数据"]

    try:
        async with 异步连接池实例.获取连接() as 数据库连接:
            # 获取第一条记录作为默认配置模板
            查询SQL = """
                SELECT * FROM "用户_微信添加配置表"
                ORDER BY id LIMIT 1
            """

            # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
            默认记录 = await 数据库连接.fetchrow(查询SQL)

            if not 默认记录:
                # 如果没有记录，抛出异常
                raise Exception("数据库中没有默认配置记录，请先创建默认配置")

            # 直接使用数据库记录
            默认配置 = dict(默认记录)

            # 更新缓存
            默认配置缓存[缓存键] = {
                "数据": 默认配置,
                "缓存时间": 当前时间
            }

            系统日志器.debug("从数据库第一条记录加载默认配置")
            return 默认配置

    except Exception as e:
        错误日志器.error(f"获取默认配置失败: {str(e)}")
        raise Exception(f"获取默认配置失败: {str(e)}")





async def 构建配置参数(用户id: int, 配置数据: Dict[str, Any]) -> tuple:
    """构建配置参数元组"""
    try:
        默认配置 = await 获取默认配置值()
    except Exception:
        # 如果获取默认配置失败，抛出异常让上层处理
        raise

    return (
        用户id,
        配置数据["微信信息表id"],
        配置数据.get("配置名称", 默认配置["配置名称"]),
        配置数据.get("每日最大添加次数", 默认配置["每日最大添加次数"]),
        配置数据.get("最小添加间隔分钟", 默认配置["最小添加间隔分钟"]),
        配置数据.get("最大添加间隔分钟", 默认配置["最大添加间隔分钟"]),
        配置数据.get("工作开始时间", 默认配置["工作开始时间"]),
        配置数据.get("工作结束时间", 默认配置["工作结束时间"]),
        配置数据.get("午休开始时间", 默认配置["午休开始时间"]),
        配置数据.get("午休结束时间", 默认配置["午休结束时间"]),
        1 if 配置数据.get("是否启用午休", 默认配置["是否启用午休"]) else 0,
        1 if 配置数据.get("周末是否添加", 默认配置["周末是否添加"]) else 0,
        配置数据.get("周末每日最大添加次数", 默认配置["周末每日最大添加次数"]),
        配置数据.get("连续添加次数上限", 默认配置["连续添加次数上限"]),
        配置数据.get("批次休息最小分钟", 默认配置["批次休息最小分钟"]),
        配置数据.get("批次休息最大分钟", 默认配置["批次休息最大分钟"]),
        配置数据.get("随机延迟最小分钟", 默认配置["随机延迟最小分钟"]),
        配置数据.get("随机延迟最大分钟", 默认配置["随机延迟最大分钟"]),
        配置数据.get("成功率模拟概率", 默认配置["成功率模拟概率"]),
        配置数据.get("每小时最大添加次数", 默认配置["每小时最大添加次数"]),
        配置数据.get("异常检测暂停分钟", 默认配置["异常检测暂停分钟"]),
        配置数据.get("验证消息模板", 默认配置["验证消息模板"]),
        配置数据.get("好友备注模板", 默认配置["好友备注模板"])
    )


def 清除配置缓存(用户id: int, 微信信息表id: int):
    """清除指定配置的缓存"""
    配置缓存.pop(f"config_{用户id}_{微信信息表id}", None)


def 清除默认配置缓存():
    """清除默认配置缓存"""
    默认配置缓存.clear()








async def 异步保存用户微信添加配置数据访问服务(用户id: int, 配置数据: Dict[str, Any]) -> Dict[str, Any]:
    """
    保存用户微信添加配置数据访问服务
    
    功能说明：
    - 保存或更新用户为指定微信号设置的添加配置参数
    - 如果配置已存在则更新，不存在则创建新配置
    - 支持所有配置参数的完整保存
    
    参数：
        用户id (int): 当前用户的ID
        配置数据 (Dict[str, Any]): 包含所有配置参数的字典
    
    返回：
        Dict[str, Any]: 包含操作状态和配置id的响应
    """
    try:
        async with 异步连接池实例.获取连接() as 数据库连接:
            # 检查配置是否已存在
            检查配置存在SQL = """
                SELECT id FROM "用户_微信添加配置表" 
                WHERE "用户id" = $1 AND "微信信息表id" = $2
            """
            
            # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
            现有配置记录 = await 数据库连接.fetchrow(检查配置存在SQL, 用户id, 配置数据["微信信息表id"])

            if 现有配置记录:
                # 更新现有配置
                更新配置SQL = """
                    UPDATE "用户_微信添加配置表" SET
                        `配置名称` = $1,
                        `每日最大添加次数` = $1,
                        `最小添加间隔分钟` = $1,
                        `最大添加间隔分钟` = $1,
                        `工作开始时间` = $1,
                        `工作结束时间` = $1,
                        `午休开始时间` = $1,
                        `午休结束时间` = $1,
                        `是否启用午休` = $1,
                        `周末是否添加` = $1,
                        `周末每日最大添加次数` = $1,
                        `连续添加次数上限` = $1,
                        `批次休息最小分钟` = $1,
                        `批次休息最大分钟` = $1,
                        `随机延迟最小分钟` = $1,
                        `随机延迟最大分钟` = $1,
                        `成功率模拟概率` = $1,
                        `每小时最大添加次数` = $1,
                        `异常检测暂停分钟` = $1,
                        `验证消息模板` = $1,
                        `好友备注模板` = $1,
                        "更新时间" = NOW()
                    WHERE "用户id" = $1 AND "微信信息表id" = $2
                """

                try:
                    配置参数 = await 构建配置参数(用户id, 配置数据)
                    更新参数 = 配置参数[2:] + (用户id, 配置数据["微信信息表id"])
                except Exception as e:
                    return {
                        "status": 状态.微信.更新对接进度失败,
                        "message": f"获取默认配置失败: {str(e)}",
                        "data": None
                    }

                await 数据库连接.execute(更新配置SQL, *更新参数)
                配置id = 现有配置记录["id"]
                操作类型 = "更新"

            else:
                # 创建新配置
                插入配置SQL = """
                    INSERT INTO "用户_微信添加配置表" (
                        `用户id`, `微信信息表id`, `配置名称`,
                        `每日最大添加次数`, `最小添加间隔分钟`, `最大添加间隔分钟`,
                        `工作开始时间`, `工作结束时间`, `午休开始时间`, `午休结束时间`, `是否启用午休`,
                        `周末是否添加`, `周末每日最大添加次数`,
                        `连续添加次数上限`, `批次休息最小分钟`, `批次休息最大分钟`,
                        `随机延迟最小分钟`, `随机延迟最大分钟`, `成功率模拟概率`,
                        `每小时最大添加次数`, `异常检测暂停分钟`, `验证消息模板`, `好友备注模板`
                    ) VALUES (
                        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23
                    )
                """

                try:
                    插入参数 = await 构建配置参数(用户id, 配置数据)
                except Exception as e:
                    return {
                        "status": 状态.微信.更新对接进度失败,
                        "message": f"获取默认配置失败: {str(e)}",
                        "data": None
                    }

                配置id = await 数据库连接.fetchval(插入配置SQL + " RETURNING id", *插入参数)
                操作类型 = "创建"

            # 清除相关缓存
            清除配置缓存(用户id, 配置数据["微信信息表id"])

            系统日志器.info(f"用户微信配置{操作类型}成功: 用户id={用户id}, 微信信息表id={配置数据['微信信息表id']}, 配置id={配置id}")

            return {
                "status": 状态.通用.成功,
                "message": f"配置{操作类型}成功",
                "data": {
                    "配置id": 配置id,
                    "操作类型": 操作类型,
                    "微信信息表id": 配置数据["微信信息表id"]
                }
            }
                
    except Exception as e:
        错误日志器.error(f"保存用户微信添加配置数据访问异常: 用户id={用户id}, 错误={str(e)}")
        return {
            "status": 状态.微信.更新对接进度失败,
            "message": "配置保存失败，请稍后重试",
            "data": None
        }


async def 异步创建用户微信添加配置数据访问服务(用户id: int, 配置数据: Dict[str, Any]) -> Dict[str, Any]:
    """
    创建用户微信添加配置数据访问服务

    功能说明：
    - 专门用于创建新的微信添加配置
    - 如果配置已存在则返回错误，不允许重复创建
    - 确保同一微信号只能有一个配置

    参数：
        用户id (int): 当前用户的ID
        配置数据 (Dict[str, Any]): 包含所有配置参数的字典

    返回：
        Dict[str, Any]: 包含操作状态和配置id的响应
    """
    try:
        async with 异步连接池实例.获取连接() as 数据库连接:
            # 检查配置是否已存在
            检查配置存在SQL = """
                SELECT id, 配置名称 FROM "用户_微信添加配置表"
                WHERE "用户id" = $1 AND "微信信息表id" = $2
            """

            # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
            现有配置记录 = await 数据库连接.fetchrow(检查配置存在SQL, 用户id, 配置数据["微信信息表id"])

            if 现有配置记录:
                # 配置已存在，返回错误
                return {
                    "status": 状态.微信.配置已存在,
                    "message": f"该微信号已存在配置「{现有配置记录['配置名称']}」，请使用更新功能或选择其他微信号",
                    "data": {
                        "现有配置id": 现有配置记录["id"],
                        "现有配置名称": 现有配置记录["配置名称"]
                    }
                }

            # 创建新配置
            插入配置SQL = """
                INSERT INTO "用户_微信添加配置表" (
                    `用户id`, `微信信息表id`, `配置名称`,
                    `每日最大添加次数`, `最小添加间隔分钟`, `最大添加间隔分钟`,
                    `工作开始时间`, `工作结束时间`, `午休开始时间`, `午休结束时间`, `是否启用午休`,
                    `周末是否添加`, `周末每日最大添加次数`,
                    `连续添加次数上限`, `批次休息最小分钟`, `批次休息最大分钟`,
                    `随机延迟最小分钟`, `随机延迟最大分钟`, `成功率模拟概率`,
                    `每小时最大添加次数`, `异常检测暂停分钟`, `验证消息模板`, `好友备注模板`
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23
                )
            """

            try:
                插入参数 = await 构建配置参数(用户id, 配置数据)
            except Exception as e:
                return {
                    "status": 状态.微信.更新对接进度失败,
                    "message": f"获取默认配置失败: {str(e)}",
                    "data": None
                }

            新配置id = await 数据库连接.fetchval(插入配置SQL + " RETURNING id", *插入参数)

            # 清除相关缓存
            清除配置缓存(用户id, 配置数据["微信信息表id"])

            系统日志器.info(f"创建用户微信添加配置成功: 用户id={用户id}, 配置id={新配置id}, 微信信息表id={配置数据['微信信息表id']}")

            return {
                "status": 状态.通用.成功,
                "message": "配置创建成功",
                "data": {
                    "配置id": 新配置id,
                    "配置名称": 配置数据.get("配置名称", "默认配置"),
                    "微信信息表id": 配置数据["微信信息表id"]
                }
            }

    except Exception as e:
        错误日志器.error(f"创建用户微信添加配置失败: 用户id={用户id}, 微信信息表id={配置数据.get('微信信息表id')}, 错误={str(e)}")
        return {
            "status": 状态.微信.配置创建失败,
            "message": "配置创建失败，请稍后重试",
            "data": None
        }


# 原函数已重构为通用函数，此处保留为向后兼容


async def 异步获取用户所有微信配置数据访问服务(用户id: int) -> Dict[str, Any]:
    """
    获取用户所有微信配置数据访问服务
    
    功能说明：
    - 获取当前用户名下所有微信号的添加配置
    - 返回配置列表和概览信息
    - 包含配置状态和主要参数
    
    参数：
        用户id (int): 当前用户的ID
    
    返回：
        Dict[str, Any]: 包含配置列表的响应
    """
    try:
        async with 异步连接池实例.获取连接() as 数据库连接:
            # 查询用户所有微信配置
            查询所有配置SQL = """
                SELECT c.*, w."微信昵称", w."微信号" 
                FROM "用户_微信添加配置表" c
                LEFT JOIN "微信信息表" w ON c."微信信息表id" = w."id"
                WHERE c."用户id" = $1
                ORDER BY c."更新时间" DESC
            """
            
            # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
            配置记录列表 = await 数据库连接.fetch(查询所有配置SQL, 用户id)

            配置列表数据 = []
            for 记录 in 配置记录列表:
                    配置项 = {
                        "配置id": 记录["id"],
                        "微信信息表id": 记录["微信信息表id"],
                        "微信昵称": 记录.get("微信昵称", "未知"),
                        "微信号": 记录.get("微信号", "未知"),
                        "配置名称": 记录["配置名称"],
                        "每日最大添加次数": 记录["每日最大添加次数"],
                        "添加间隔范围": f"{记录['最小添加间隔分钟']}-{记录['最大添加间隔分钟']}分钟",
                        "工作时间": f"{记录['工作开始时间']}-{记录['工作结束时间']}",
                        "是否启用午休": bool(记录["是否启用午休"]),
                        "周末是否添加": bool(记录["周末是否添加"]),
                        "连续添加上限": 记录["连续添加次数上限"],
                        "创建时间": 记录["创建时间"],
                        "更新时间": 记录["更新时间"]
                    }
                    配置列表数据.append(配置项)

            系统日志器.info(f"获取用户所有微信配置成功: 用户id={用户id}, 配置数量={len(配置列表数据)}")

            return {
                "status": 状态.通用.成功,
                "message": "配置列表获取成功",
                "data": {
                    "配置列表": 配置列表数据,
                    "配置总数": len(配置列表数据)
                }
            }
                
    except Exception as e:
        错误日志器.error(f"获取用户所有微信配置数据访问异常: 用户id={用户id}, 错误={str(e)}")
        return {
            "status": 状态.微信.获取对接进度列表失败,
            "message": "配置列表获取失败，请稍后重试",
            "data": None
        }


async def 异步删除用户微信配置数据访问服务(用户id: int, 配置id: int) -> Dict[str, Any]:
    """
    删除用户微信配置数据访问服务
    
    功能说明：
    - 删除用户指定的微信添加配置
    - 验证配置归属权限
    - 软删除（设置为禁用状态）
    
    参数：
        用户id (int): 当前用户的ID
        配置id (int): 要删除的配置id
    
    返回：
        Dict[str, Any]: 包含删除结果的响应
    """
    try:
        async with 异步连接池实例.获取连接() as 数据库连接:
            # 验证配置归属
            验证归属SQL = """
                SELECT id, 微信信息表id FROM "用户_微信添加配置表" 
                WHERE "id" = $1 AND "用户id" = $2
            """
            
            # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
            配置记录 = await 数据库连接.fetchrow(验证归属SQL, 配置id, 用户id)

            if not 配置记录:
                return {
                    "status": 状态.微信.获取对接进度列表失败,
                    "message": "配置不存在或您无权限删除",
                    "data": None
                }

            # 删除配置
            删除配置SQL = """
                DELETE FROM "用户_微信添加配置表"
                WHERE "id" = $1 AND "用户id" = $2
            """

            await 数据库连接.execute(删除配置SQL, 配置id, 用户id)

            # 清除相关缓存
            清除配置缓存(用户id, 配置记录["微信信息表id"])

            系统日志器.info(f"删除用户微信配置成功: 用户id={用户id}, 配置id={配置id}, 微信信息表id={配置记录['微信信息表id']}")

            return {
                "status": 状态.通用.成功,
                "message": "配置删除成功",
                "data": {
                    "配置id": 配置id,
                    "微信信息表id": 配置记录["微信信息表id"]
                }
            }
                
    except Exception as e:
        错误日志器.error(f"删除用户微信配置数据访问异常: 用户id={用户id}, 配置id={配置id}, 错误={str(e)}")
        return {
            "status": 状态.微信.更新对接进度失败,
            "message": "配置删除失败，请稍后重试",
            "data": None
        }


async def 异步获取用户微信配置概览统计数据访问服务(用户id: int) -> Dict[str, Any]:
    """
    获取用户微信配置概览统计数据访问服务
    
    功能说明：
    - 获取用户配置相关的概览统计信息
    - 包括配置总数、启用数量、绑定微信账号数、今日添加好友数
    - 用于管理页面的概览卡片展示
    
    参数：
        用户id (int): 当前用户的ID
    
    返回：
        Dict[str, Any]: 包含统计信息的响应
    """
    try:
        async with 异步连接池实例.获取连接() as 数据库连接:
            # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
            # 获取配置相关统计
            配置统计SQL = """
                SELECT
                    COUNT(*) as 配置总数,
                    COUNT(DISTINCT 微信信息表id) as 绑定微信账号数
                FROM 用户_微信添加配置表
                WHERE 用户id = $1
            """

            配置统计结果_原始 = await 数据库连接.fetchrow(配置统计SQL, 用户id)
            配置统计结果 = dict(配置统计结果_原始) if 配置统计结果_原始 else {}

            # 获取今日添加好友数
            今日添加统计SQL = """
                SELECT COUNT(*) as 今日添加好友数
                FROM 用户_联系方式_微信添加记录表
                WHERE 用户表id = $1
                AND 创建时间::date = CURRENT_DATE
                AND 好友请求状态 = 1
            """

            今日统计结果_原始 = await 数据库连接.fetchrow(今日添加统计SQL, 用户id)
            今日统计结果 = dict(今日统计结果_原始) if 今日统计结果_原始 else {}

            统计数据 = {
                "配置总数": 配置统计结果.get("配置总数", 0),
                "启用配置数": 配置统计结果.get("启用配置数", 0),
                "绑定微信账号数": 配置统计结果.get("绑定微信账号数", 0),
                "今日添加好友数": 今日统计结果.get("今日添加好友数", 0)
            }

            系统日志器.info(f"获取用户微信配置概览统计成功: 用户id={用户id}, 统计数据={统计数据}")

            return {
                "status": 状态.通用.成功,
                "message": "概览统计获取成功",
                "data": 统计数据
            }

    except Exception as e:
        错误日志器.error(f"获取用户微信配置概览统计数据访问异常: 用户id={用户id}, 错误={str(e)}")
        return {
            "status": 状态.微信.获取对接进度列表失败,
            "message": "概览统计获取失败，请稍后重试",
            "data": None
        }


async def 异步获取用户微信账号列表数据访问服务(用户id: int) -> Dict[str, Any]:
    """
    获取用户微信账号列表数据访问服务
    
    功能说明：
    - 获取用户可用的微信账号列表
    - 用于配置表单中的微信账号选择下拉框
    - 返回微信号、备注等基本信息
    
    参数：
        用户id (int): 当前用户的ID
    
    返回：
        Dict[str, Any]: 包含微信账号列表的响应
    """
    try:
        async with 异步连接池实例.获取连接() as 数据库连接:
            # 查询用户关联的微信账号
            查询微信账号SQL = """
                SELECT DISTINCT w.id, w.微信号, w.修改时间, '绑定微信账号' as 备注
                FROM 微信信息表 w
                INNER JOIN 用户微信关联表 u ON w.id = u.微信id
                WHERE u.用户id = $1
                ORDER BY w.修改时间 DESC
            """
            
            # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
            微信账号记录列表 = await 数据库连接.fetch(查询微信账号SQL, 用户id)

            账号列表数据 = []
            for 记录 in 微信账号记录列表:
                账号项 = {
                    "id": 记录["id"],
                    "微信号": 记录["微信号"],
                    "备注": 记录["备注"]
                }
                账号列表数据.append(账号项)

            系统日志器.info(f"获取用户微信账号列表成功: 用户id={用户id}, 账号数量={len(账号列表数据)}")

            return {
                "status": 状态.通用.成功,
                "message": "微信账号列表获取成功",
                "data": 账号列表数据
            }
                
    except Exception as e:
        错误日志器.error(f"获取用户微信账号列表数据访问异常: 用户id={用户id}, 错误={str(e)}")
        return {
            "status": 状态.微信.获取对接进度列表失败,
            "message": "微信账号列表获取失败，请稍后重试",
            "data": None
        }


async def 异步获取用户微信配置列表数据访问服务(用户id: int, 页码: int = 1, 每页数量: int = 10, 搜索关键词: Optional[str] = None) -> Dict[str, Any]:
    """
    获取用户微信配置列表数据访问服务

    功能说明：
    - 获取用户微信自动化配置列表（简化版）
    - 支持分页查询和搜索功能
    - 只返回必要的基本信息：配置名称、微信账号、状态等

    参数：
        用户id (int): 当前用户的ID
        页码 (int): 页码，从1开始
        每页数量 (int): 每页显示的配置数量
        搜索关键词 (str): 搜索关键词，用于配置名称搜索

    返回：
        Dict[str, Any]: 包含分页配置列表的响应（简化版）
    """
    try:
        async with 异步连接池实例.获取连接() as 数据库连接:
            # 计算偏移量
            偏移量 = (页码 - 1) * 每页数量
            
            # 构建查询条件
            查询条件 = "WHERE c.用户id = $1"
            查询参数: list = [用户id]
            参数索引 = 2

            if 搜索关键词:
                # {{ AURA-X: Modify - 修复PostgreSQL参数占位符索引错误. Approval: 寸止(ID:1735372800). }}
                # {{ Source: PostgreSQL参数占位符最佳实践 }}
                查询条件 += f" AND c.配置名称 LIKE ${参数索引}"
                查询参数.append(f"%{搜索关键词}%")
                参数索引 += 1
            
            # 查询配置总数
            统计SQL = f"""
                SELECT COUNT(*) as 总数
                FROM 用户_微信添加配置表 c
                LEFT JOIN 微信信息表 w ON c.微信信息表id = w.id
                {查询条件}
            """
            
            # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
            统计结果_原始 = await 数据库连接.fetchrow(统计SQL, *查询参数)
            统计结果 = dict(统计结果_原始) if 统计结果_原始 else {}
            总数 = 统计结果.get("总数", 0)

            # 查询分页数据
            列表SQL = f"""
                SELECT c.*, w.微信号 as 微信账号名称
                FROM 用户_微信添加配置表 c
                LEFT JOIN 微信信息表 w ON c.微信信息表id = w.id
                {查询条件}
                ORDER BY c.更新时间 DESC
                LIMIT ${参数索引} OFFSET ${参数索引 + 1}
            """

            分页参数 = 查询参数 + [每页数量, 偏移量]
            配置记录列表 = await 数据库连接.fetch(列表SQL, *分页参数)

            配置列表数据 = []
            for 记录 in 配置记录列表:
                配置项 = {
                    "id": 记录["id"],
                    "配置名称": 记录["配置名称"],
                    "微信信息表id": 记录["微信信息表id"],
                    "微信账号名称": 记录.get("微信账号名称", "未绑定"),
                    "创建时间": 记录["创建时间"],
                    "更新时间": 记录["更新时间"]
                }
                配置列表数据.append(配置项)

            系统日志器.info(f"获取用户微信配置列表成功: 用户id={用户id}, 页码={页码}, 总数={总数}, 当前页数量={len(配置列表数据)}")

            return {
                "status": 状态.通用.成功,
                "message": "配置列表获取成功",
                "data": {
                    "列表": 配置列表数据,
                    "总数": 总数,
                    "页码": 页码,
                    "每页数量": 每页数量,
                    "总页数": (总数 + 每页数量 - 1) // 每页数量
                }
            }
                
    except Exception as e:
        错误日志器.error(f"获取用户微信配置列表数据访问异常: 用户id={用户id}, 错误={str(e)}")
        return {
            "status": 状态.微信.获取对接进度列表失败,
            "message": "配置列表获取失败，请稍后重试",
            "data": None
        }


async def 异步更新用户微信添加配置数据访问服务(用户id: int, 配置id: int, 配置数据: Dict[str, Any]) -> Dict[str, Any]:
    """
    更新用户微信添加配置数据访问服务
    
    功能说明：
    - 更新现有的微信自动化添加配置
    - 验证配置id的归属权限
    - 更新配置参数到数据库
    
    参数：
        用户id (int): 当前用户的ID
        配置id (int): 要更新的配置id
        配置数据 (Dict[str, Any]): 包含更新配置参数的字典
    
    返回：
        Dict[str, Any]: 包含操作状态和配置信息的响应
    """
    try:
        async with 异步连接池实例.获取连接() as 数据库连接:
            # 验证配置归属
            验证归属SQL = """
                SELECT id FROM "用户_微信添加配置表" 
                WHERE "id" = $1 AND "用户id" = $2
            """
            
            配置记录 = await 数据库连接.fetchrow(验证归属SQL, 配置id, 用户id)
            
            if not 配置记录:
                return {
                    "status": 状态.微信.获取对接进度列表失败,
                    "message": "配置不存在或您无权限修改",
                    "data": None
                }
            
            # 更新配置
            更新配置SQL = """
                    UPDATE "用户_微信添加配置表" SET
                        `配置名称` = $1,
                        `微信信息表id` = $1,
                        `每日最大添加次数` = $1,
                        `最小添加间隔分钟` = $1,
                        `最大添加间隔分钟` = $1,
                        `工作开始时间` = $1,
                        `工作结束时间` = $1,
                        `午休开始时间` = $1,
                        `午休结束时间` = $1,
                        `是否启用午休` = $1,
                        `周末是否添加` = $1,
                        `周末每日最大添加次数` = $1,
                        `连续添加次数上限` = $1,
                        `批次休息最小分钟` = $1,
                        `批次休息最大分钟` = $1,
                        `随机延迟最小分钟` = $1,
                        `随机延迟最大分钟` = $1,
                        `成功率模拟概率` = $1,
                        `每小时最大添加次数` = $1,
                        `异常检测暂停分钟` = $1,
                        `验证消息模板` = $1,
                        `好友备注模板` = $1,
                        "更新时间" = NOW()
                    WHERE "id" = $1 AND "用户id" = $2
                """
                
                # 使用构建配置参数函数，避免硬编码默认值
            try:
                    配置参数 = await 构建配置参数(用户id, 配置数据)
                    # 重新排列参数：配置名称, 微信信息表id, 其他配置参数, 配置id, 用户id
                    更新参数 = (配置参数[2],) + (配置参数[1],) + 配置参数[3:] + (配置id, 用户id)
            except Exception as e:
                    return {
                        "status": 状态.微信.更新对接进度失败,
                        "message": f"获取默认配置失败: {str(e)}",
                        "data": None
                    }

            # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
            await 数据库连接.execute(更新配置SQL, *更新参数)

            # 清除相关缓存
            if 配置数据.get("微信信息表id"):
                清除配置缓存(用户id, 配置数据["微信信息表id"])

            系统日志器.info(f"更新用户微信配置成功: 用户id={用户id}, 配置id={配置id}")

            return {
                    "status": 状态.通用.成功,
                    "message": "配置更新成功",
                    "data": {
                        "配置id": 配置id,
                        "操作类型": "更新",
                        "微信信息表id": 配置数据.get("微信信息表id")
                    }
                }
                
    except Exception as e:
        错误日志器.error(f"更新用户微信添加配置数据访问异常: 用户id={用户id}, 配置id={配置id}, 错误={str(e)}")
        return {
            "status": 状态.微信.更新对接进度失败,
            "message": "配置更新失败，请稍后重试",
            "data": None
        }


# 重构：合并两个冗余的配置获取函数为一个通用函数
async def 异步获取用户微信配置数据访问服务(用户id: int, 配置id: int) -> Dict[str, Any]:
    """
    获取用户微信配置数据访问服务

    参数：
        用户id (int): 当前用户的ID
        配置id (int): 配置id

    返回：
        Dict[str, Any]: 包含配置信息的响应
    """

    try:
        async with 异步连接池实例.获取连接() as 数据库连接:
            # 根据配置id查询
            查询SQL = """
                SELECT * FROM "用户_微信添加配置表"
                WHERE "id" = $1 AND "用户id" = $2
            """

            配置记录 = await 数据库连接.fetchrow(查询SQL, 配置id, 用户id)

            if not 配置记录:
                return {
                    "status": 状态.微信.获取对接进度列表失败,
                    "message": "配置不存在或您无权限查看",
                    "data": None
                }

            # 构建配置数据
            配置数据 = {
                "id": 配置记录["id"],
                "配置名称": 配置记录["配置名称"],
                    "微信信息表id": 配置记录["微信信息表id"],
                    "每日最大添加次数": 配置记录["每日最大添加次数"],
                    "最小添加间隔分钟": 配置记录["最小添加间隔分钟"],
                    "最大添加间隔分钟": 配置记录["最大添加间隔分钟"],
                    "工作开始时间": 配置记录["工作开始时间"],
                    "工作结束时间": 配置记录["工作结束时间"],
                    "午休开始时间": 配置记录["午休开始时间"],
                    "午休结束时间": 配置记录["午休结束时间"],
                    "是否启用午休": int(配置记录["是否启用午休"]),
                    "周末是否添加": int(配置记录["周末是否添加"]),
                    "周末每日最大添加次数": 配置记录["周末每日最大添加次数"],
                    "连续添加次数上限": 配置记录["连续添加次数上限"],
                    "批次休息最小分钟": 配置记录["批次休息最小分钟"],
                    "批次休息最大分钟": 配置记录["批次休息最大分钟"],
                    "随机延迟最小分钟": 配置记录["随机延迟最小分钟"],
                    "随机延迟最大分钟": 配置记录["随机延迟最大分钟"],
                    "成功率模拟概率": float(配置记录["成功率模拟概率"]),
                    "每小时最大添加次数": 配置记录["每小时最大添加次数"],
                    "异常检测暂停分钟": 配置记录["异常检测暂停分钟"],
                    "验证消息模板": 配置记录.get("验证消息模板", "你好，我是{达人昵称}的朋友，想和你交流一下"),
                    "好友备注模板": 配置记录.get("好友备注模板", "{达人昵称}-{联系方式类型}"),
                    "创建时间": 配置记录["创建时间"],
                    "更新时间": 配置记录["更新时间"],
                    "配置来源": "用户自定义"
                }

            系统日志器.info(f"获取用户微信配置成功: 用户id={用户id}, 配置id={配置id}")

            return {
                    "status": 状态.通用.成功,
                    "message": "配置获取成功",
                    "data": 配置数据
                }

    except Exception as e:
        错误信息 = f"获取用户微信配置失败: 用户id={用户id}, 配置id={配置id}, 错误={str(e)}"
        错误日志器.error(错误信息)
        return {
            "status": 状态.通用.服务器错误,
            "message": "获取配置失败，请稍后重试",
            "data": None
        }


async def 异步获取微信号配置数据访问服务(用户id: int, 微信信息表id: int) -> Dict[str, Any]:
    """
    根据微信信息表ID获取配置（如果没有则返回默认配置）

    参数：
        用户id (int): 当前用户的ID
        微信信息表id (int): 微信信息表ID

    返回：
        Dict[str, Any]: 包含配置信息的响应
    """
    try:
        async with 异步连接池实例.获取连接() as 数据库连接:
            # 根据微信信息表ID查询用户配置
            查询SQL = """
                SELECT * FROM "用户_微信添加配置表"
                WHERE "微信信息表id" = $1 AND "用户id" = $2
            """

            # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
            配置记录 = await 数据库连接.fetchrow(查询SQL, 微信信息表id, 用户id)

            if 配置记录:
                    # 用户有自定义配置
                    配置数据 = {
                        "id": 配置记录["id"],
                        "配置名称": 配置记录["配置名称"],
                        "微信信息表id": 配置记录["微信信息表id"],
                        "每日最大添加次数": 配置记录["每日最大添加次数"],
                        "最小添加间隔分钟": 配置记录["最小添加间隔分钟"],
                        "最大添加间隔分钟": 配置记录["最大添加间隔分钟"],
                        "工作开始时间": 配置记录["工作开始时间"],
                        "工作结束时间": 配置记录["工作结束时间"],
                        "午休开始时间": 配置记录["午休开始时间"],
                        "午休结束时间": 配置记录["午休结束时间"],
                        "是否启用午休": int(配置记录["是否启用午休"]),
                        "周末是否添加": int(配置记录["周末是否添加"]),
                        "周末每日最大添加次数": 配置记录["周末每日最大添加次数"],
                        "连续添加次数上限": 配置记录["连续添加次数上限"],
                        "批次休息最小分钟": 配置记录["批次休息最小分钟"],
                        "批次休息最大分钟": 配置记录["批次休息最大分钟"],
                        "随机延迟最小分钟": 配置记录["随机延迟最小分钟"],
                        "随机延迟最大分钟": 配置记录["随机延迟最大分钟"],
                        "成功率模拟概率": float(配置记录["成功率模拟概率"]),
                        "每小时最大添加次数": 配置记录["每小时最大添加次数"],
                        "异常检测暂停分钟": 配置记录["异常检测暂停分钟"],
                        "验证消息模板": 配置记录.get("验证消息模板", "你好，我是{达人昵称}的朋友，想和你交流一下"),
                        "好友备注模板": 配置记录.get("好友备注模板", "{达人昵称}-{联系方式类型}"),
                        "创建时间": 配置记录["创建时间"],
                        "更新时间": 配置记录["更新时间"],
                        "配置来源": "用户自定义"
                    }
            else:
                # 用户未设置配置，返回系统默认配置
                try:
                    默认配置 = await 获取默认配置值()
                    配置数据 = {
                        "id": 0,
                        "微信信息表id": 微信信息表id,
                        **默认配置,
                        "创建时间": None,
                        "更新时间": None,
                        "配置来源": "系统默认"
                    }
                except Exception as e:
                    return {
                        "status": 状态.微信.获取对接进度列表失败,
                        "message": f"获取默认配置失败: {str(e)}",
                        "data": None
                    }

            系统日志器.info(f"获取微信号配置成功: 用户id={用户id}, 微信信息表id={微信信息表id}, 配置来源={配置数据['配置来源']}")

            return {
                "status": 状态.通用.成功,
                "message": "配置获取成功",
                    "data": 配置数据
                }

    except Exception as e:
        错误信息 = f"获取微信号配置失败: 用户id={用户id}, 微信信息表id={微信信息表id}, 错误={str(e)}"
        错误日志器.error(错误信息)
        return {
            "status": 状态.通用.服务器错误,
            "message": "获取配置失败，请稍后重试",
            "data": None
        }


