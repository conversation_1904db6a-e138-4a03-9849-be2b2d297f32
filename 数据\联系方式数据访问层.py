"""
联系方式数据访问层 - PostgreSQL版本
基于asyncpg实现的联系方式数据访问层

特性：
1. 使用PostgreSQL原生语法和特性
2. 支持高效的联系方式查询和管理
3. 使用$1, $2参数占位符，防止SQL注入
4. 优化的关联查询和数据处理
5. 完整的错误处理和日志记录
"""

from typing import Any, Dict, List, Optional

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 数据库日志器, 错误日志器


class 联系方式数据访问:
    """联系方式表数据访问"""

    @staticmethod
    async def 查询联系方式_通过ID(联系方式ID: int) -> Optional[Dict[str, Any]]:
        """
        通过ID查询联系方式

        Args:
            联系方式ID: 联系方式ID

        Returns:
            联系方式信息或None
        """
        try:
            查询SQL = """
            SELECT
                id, 联系方式, 类型, 来源, 创建时间, 更新时间
            FROM 联系方式表
            WHERE id = $1
            """

            结果 = await 异步连接池实例.执行查询(查询SQL, (联系方式ID,))
            return 结果[0] if 结果 else None

        except Exception as e:
            错误日志器.error(f"查询联系方式失败: ID={联系方式ID}, 错误={str(e)}")
            raise

    @staticmethod
    async def 查询联系方式_通过内容和类型(联系方式: str, 类型: str) -> Optional[Dict[str, Any]]:
        """
        通过联系方式内容和类型查询

        Args:
            联系方式: 联系方式内容
            类型: 联系方式类型

        Returns:
            联系方式信息或None
        """
        try:
            查询SQL = """
            SELECT
                id, 联系方式, 类型, 来源, 创建时间, 更新时间
            FROM 联系方式表
            WHERE 联系方式 = $1 AND 类型 = $2
            """

            结果 = await 异步连接池实例.执行查询(查询SQL, (联系方式, 类型))
            return 结果[0] if 结果 else None

        except Exception as e:
            错误日志器.error(f"查询联系方式失败: 联系方式={联系方式}, 类型={类型}, 错误={str(e)}")
            raise

    @staticmethod
    async def 创建联系方式(
        联系方式: str,
        类型: str,
        来源: Optional[str] = None
    ) -> Optional[int]:
        """
        创建新的联系方式记录

        Args:
            联系方式: 联系方式内容
            类型: 联系方式类型
            来源: 记录来源

        Returns:
            新创建的联系方式ID或None
        """
        try:
            插入SQL = """
            INSERT INTO 联系方式表 (联系方式, 类型, 来源, 创建时间, 更新时间)
            VALUES ($1, $2, $3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            RETURNING id
            """

            结果 = await 异步连接池实例.执行查询(插入SQL, (联系方式, 类型, 来源))

            if 结果:
                联系方式ID = 结果[0]["id"]
                数据库日志器.info(f"创建联系方式成功: ID={联系方式ID}, 联系方式={联系方式}, 类型={类型}")
                return 联系方式ID
            else:
                错误日志器.error("创建联系方式失败，未返回id")
                return None

        except Exception as e:
            错误日志器.error(f"创建联系方式失败: 联系方式={联系方式}, 类型={类型}, 错误={str(e)}")
            raise

    @staticmethod
    async def 更新联系方式(
        联系方式ID: int,
        新联系方式: Optional[str] = None,
        新类型: Optional[str] = None,
        新来源: Optional[str] = None
    ) -> bool:
        """
        更新联系方式信息

        Args:
            联系方式ID: 联系方式ID
            新联系方式: 新的联系方式内容
            新类型: 新的联系方式类型
            新来源: 新的记录来源

        Returns:
            是否更新成功
        """
        try:
            更新字段 = []
            参数列表 = []
            参数索引 = 1

            if 新联系方式 is not None:
                更新字段.append(f"联系方式 = ${参数索引}")
                参数列表.append(新联系方式)
                参数索引 += 1

            if 新类型 is not None:
                更新字段.append(f"类型 = ${参数索引}")
                参数列表.append(新类型)
                参数索引 += 1

            if 新来源 is not None:
                更新字段.append(f"来源 = ${参数索引}")
                参数列表.append(新来源)
                参数索引 += 1

            if not 更新字段:
                数据库日志器.warning(f"更新联系方式时没有提供任何更新字段: ID={联系方式ID}")
                return False

            # 总是更新更新时间
            更新字段.append("更新时间 = CURRENT_TIMESTAMP")

            更新SQL = f"""
            UPDATE 联系方式表
            SET {', '.join(更新字段)}
            WHERE id = ${参数索引}
            """
            参数列表.append(联系方式ID)

            影响行数 = await 异步连接池实例.执行更新(更新SQL, tuple(参数列表))

            if 影响行数 > 0:
                数据库日志器.info(f"更新联系方式成功: ID={联系方式ID}")
                return True
            else:
                数据库日志器.warning(f"更新联系方式未影响任何行: ID={联系方式ID}")
                return False

        except Exception as e:
            错误日志器.error(f"更新联系方式失败: ID={联系方式ID}, 错误={str(e)}")
            raise

    @staticmethod
    async def 删除联系方式(联系方式ID: int) -> bool:
        """
        删除联系方式记录

        Args:
            联系方式ID: 联系方式ID

        Returns:
            是否删除成功
        """
        try:
            删除SQL = "DELETE FROM 联系方式表 WHERE id = $1"

            影响行数 = await 异步连接池实例.执行更新(删除SQL, (联系方式ID,))

            if 影响行数 > 0:
                数据库日志器.info(f"删除联系方式成功: ID={联系方式ID}")
                return True
            else:
                数据库日志器.warning(f"删除联系方式未影响任何行: ID={联系方式ID}")
                return False

        except Exception as e:
            错误日志器.error(f"删除联系方式失败: ID={联系方式ID}, 错误={str(e)}")
            raise

    @staticmethod
    async def 分页查询联系方式(
        页码: int = 1,
        每页数量: int = 20,
        类型筛选: Optional[str] = None,
        来源筛选: Optional[str] = None,
        关键词搜索: Optional[str] = None
    ) -> tuple[List[Dict[str, Any]], int]:
        """
        分页查询联系方式

        Args:
            页码: 页码（从1开始）
            每页数量: 每页记录数
            类型筛选: 按类型筛选
            来源筛选: 按来源筛选
            关键词搜索: 关键词搜索

        Returns:
            (联系方式列表, 总记录数)
        """
        try:
            where_条件 = []
            参数列表 = []
            参数索引 = 1

            if 类型筛选:
                where_条件.append(f"类型 = ${参数索引}")
                参数列表.append(类型筛选)
                参数索引 += 1

            if 来源筛选:
                where_条件.append(f"来源 = ${参数索引}")
                参数列表.append(来源筛选)
                参数索引 += 1

            if 关键词搜索:
                where_条件.append(f"联系方式 ILIKE ${参数索引}")
                参数列表.append(f"%{关键词搜索}%")
                参数索引 += 1

            where_clause = "WHERE " + " AND ".join(where_条件) if where_条件 else ""

            # 查询总数
            计数SQL = f"""
            SELECT COUNT(*) as total
            FROM 联系方式表
            {where_clause}
            """

            总数结果 = await 异步连接池实例.执行查询(计数SQL, tuple(参数列表))
            总数 = 总数结果[0]["total"] if 总数结果 else 0

            # 查询数据
            偏移量 = (页码 - 1) * 每页数量
            查询SQL = f"""
            SELECT
                id, 联系方式, 类型, 来源, 创建时间, 更新时间
            FROM 联系方式表
            {where_clause}
            ORDER BY 创建时间 DESC
            LIMIT ${参数索引} OFFSET ${参数索引 + 1}
            """

            # 添加分页参数
            参数列表.extend([每页数量, 偏移量])

            联系方式列表 = await 异步连接池实例.执行查询(查询SQL, tuple(参数列表))

            数据库日志器.debug(f"分页查询联系方式成功，页码: {页码}, 每页: {每页数量}, 总数: {总数}")
            return 联系方式列表, 总数

        except Exception as e:
            错误日志器.error(f"分页查询联系方式失败，页码: {页码}, 每页: {每页数量}, 错误: {str(e)}")
            raise


class 用户达人关联数据访问:
    """用户达人关联表数据访问"""

    @staticmethod
    async def 查询用户达人关联(
        用户id: int,
        达人id: Optional[int] = None,
        平台: Optional[str] = None,
        平台账号: Optional[str] = None,
    ) -> Optional[Dict[str, Any]]:
        """
        查询用户达人关联记录

        参数:
            用户id: 用户id
            达人id: 达人id（可选）
            平台: 平台类型（可选）
            平台账号: 平台账号（可选）

        返回:
            关联记录或None
        """
        try:
            # 构建查询条件
            条件列表 = ["用户id = $1", "状态 = 1"]
            参数列表: List[Any] = [用户id]
            参数索引 = 2

            if 达人id is not None:
                条件列表.append(f"达人id = ${参数索引}")
                参数列表.append(达人id)
                参数索引 += 1
            else:
                条件列表.append("达人id IS NULL")

            if 平台:
                条件列表.append(f"平台 = ${参数索引}")
                参数列表.append(平台)
                参数索引 += 1

            if 平台账号:
                条件列表.append(f"平台账号 = ${参数索引}")
                参数列表.append(平台账号)
                参数索引 += 1

            查询SQL = f"""
            SELECT id, 用户id, 达人id, 平台, 平台账号, 认领时间, 状态, 备注
            FROM 用户达人关联表
            WHERE {" AND ".join(条件列表)}
            ORDER BY 认领时间 DESC
            LIMIT 1
            """

            结果 = await 异步连接池实例.执行查询(查询SQL, tuple(参数列表))
            return 结果[0] if 结果 else None

        except Exception as e:
            错误日志器.error(
                f"查询用户达人关联失败: 用户id={用户id}, 达人id={达人id}, 平台={平台}, 平台账号={平台账号}, 错误={str(e)}"
            )
            raise

    @staticmethod
    async def 创建用户达人关联(
        用户id: int,
        达人id: Optional[int],
        平台: str,
        平台账号: str,
        备注: Optional[str] = None,
    ) -> int:
        """
        创建用户达人关联记录

        参数:
            用户id: 用户id
            达人id: 达人id（可为None表示虚拟关联）
            平台: 平台类型
            平台账号: 平台账号
            备注: 备注信息

        返回:
            新创建的关联记录ID
        """
        try:
            创建SQL = """
            INSERT INTO 用户达人关联表 (用户id, 达人id, 平台, 平台账号, 认领时间, 状态, 备注)
            VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP, 1, $5)
            RETURNING id
            """

            结果 = await 异步连接池实例.执行查询(创建SQL, (用户id, 达人id, 平台, 平台账号, 备注))

            if 结果:
                关联id = 结果[0]["id"]
                数据库日志器.info(f"创建用户达人关联成功: ID={关联id}, 用户id={用户id}, 平台={平台}")
                return 关联id
            else:
                错误日志器.error("创建用户达人关联失败，未返回id")
                raise Exception("创建用户达人关联失败")

        except Exception as e:
            错误日志器.error(
                f"创建用户达人关联失败: 用户id={用户id}, 达人id={达人id}, 平台={平台}, 平台账号={平台账号}, 错误={str(e)}"
            )
            raise


class 补充信息数据访问:
    """用户达人补充信息表数据访问"""

    @staticmethod
    async def 查询重复联系方式(用户id: int, 联系方式: str) -> Optional[Dict[str, Any]]:
        """
        检查用户是否已有相同联系方式

        Args:
            用户id: 用户id
            联系方式: 联系方式

        Returns:
            重复记录或None
        """
        try:
            查询SQL = """
            SELECT
                s.id as 补充信息id,
                s.联系方式,
                s.联系方式类型,
                s.个人备注,
                s.创建时间,
                u.平台,
                u.平台账号
            FROM 用户达人补充信息表 s
            INNER JOIN 用户达人关联表 u ON s.用户达人关联表id = u.id
            WHERE u.用户id = $1 AND s.联系方式 = $2 AND u.状态 = 1
            LIMIT 1
            """

            结果 = await 异步连接池实例.执行查询(查询SQL, (用户id, 联系方式))
            return 结果[0] if 结果 else None

        except Exception as e:
            错误日志器.error(
                f"查询重复联系方式失败: 用户id={用户id}, 联系方式={联系方式}, 错误={str(e)}"
            )
            raise

    @staticmethod
    async def 创建或更新补充信息(
        关联表ID: int,
        联系方式: str,
        联系方式类型: str,
        个人备注: Optional[str] = None,
        个人标签: Optional[str] = None,
        补充信息: Optional[str] = None,
    ) -> None:
        """
        创建或更新用户达人补充信息

        Args:
            关联表ID: 用户达人关联表ID
            联系方式: 联系方式
            联系方式类型: 联系方式类型
            个人备注: 个人备注
            个人标签: 个人标签
            补充信息: 补充信息
        """
        try:
            # 使用PostgreSQL的UPSERT语法
            upsert_sql = """
            INSERT INTO 用户达人补充信息表
                (用户达人关联表id, 联系方式, 联系方式类型, 个人备注, 个人标签, 补充信息, 创建时间, 更新时间)
            VALUES
                ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            ON CONFLICT (用户达人关联表id)
            DO UPDATE SET
                联系方式 = EXCLUDED.联系方式,
                联系方式类型 = EXCLUDED.联系方式类型,
                个人备注 = EXCLUDED.个人备注,
                个人标签 = EXCLUDED.个人标签,
                补充信息 = EXCLUDED.补充信息,
                更新时间 = CURRENT_TIMESTAMP
            """

            await 异步连接池实例.执行插入(
                upsert_sql,
                (关联表ID, 联系方式, 联系方式类型, 个人备注, 个人标签, 补充信息)
            )

            数据库日志器.info(f"创建或更新补充信息成功: 关联表ID={关联表ID}")

        except Exception as e:
            错误日志器.error(
                f"创建或更新补充信息失败: 关联表ID={关联表ID}, 错误={str(e)}"
            )
            raise


class 达人数据访问:
    """达人表数据访问"""

    @staticmethod
    async def 查询抖音达人(平台账号: str) -> Optional[Dict[str, Any]]:
        """
        根据抖音账号查询达人

        参数:
            平台账号: 抖音账号

        返回:
            达人记录或None
        """
        try:
            查询SQL = """
            SELECT id, 昵称, account_douyin, 粉丝数, avatar, introduction
            FROM 达人表
            WHERE account_douyin = $1 AND (账号状态 IS NULL OR (账号状态 != '已注销' AND 账号状态 != 1))
            LIMIT 1
            """

            结果 = await 异步连接池实例.执行查询(查询SQL, (平台账号,))
            return 结果[0] if 结果 else None

        except Exception as e:
            错误日志器.error(f"查询抖音达人失败: 平台账号={平台账号}, 错误={str(e)}")
            raise

    @staticmethod
    async def 查询微信达人(平台账号: str) -> Optional[Dict[str, Any]]:
        """
        根据微信账号查询达人

        参数:
            平台账号: 微信账号

        返回:
            达人记录或None
        """
        try:
            查询SQL = """
            SELECT id, 昵称, finderUsername, 粉丝数, avatar, introduction
            FROM 微信达人表
            WHERE finderUsername = $1
            LIMIT 1
            """

            结果 = await 异步连接池实例.执行查询(查询SQL, (平台账号,))
            return 结果[0] if 结果 else None

        except Exception as e:
            错误日志器.error(f"查询微信达人失败: 平台账号={平台账号}, 错误={str(e)}")
            raise




# 创建全局实例
联系方式数据访问实例 = 联系方式数据访问()
用户达人关联数据访问实例 = 用户达人关联数据访问()
补充信息数据访问实例 = 补充信息数据访问()
达人数据访问实例 = 达人数据访问()
