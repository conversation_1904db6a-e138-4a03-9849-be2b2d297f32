from typing import Optional, List, Dict, Any

from pydantic import BaseModel, Field, model_validator


class 微信小店达人写入模型(BaseModel):
    """
    微信小店达人写入模型

    用于创建或更新微信小店达人信息

    Attributes:
        finderUsername (str): 达人finder的username，用于唯一识别达人，必填项。
        openfinderid (Optional[str]): 达人finder的openfinderid。
        talentAppid (Optional[str]): 达人小店的AppID。
        昵称 (Optional[str]): 达人昵称。
        头像 (Optional[str]): 达人头像的URL。
        内容类型 (Optional[List[Dict[str, Any]]]): 达人内容类型对象列表。
        带货类目 (Optional[List[Dict[str, Any]]]): 达人带货类目对象列表。
        粉丝数文本 (Optional[str]): 达人粉丝数，文本格式。
        GMV文本 (Optional[str]): 达人GMV（商品交易总额），文本格式。
        有无联系方式 (Optional[int]): 是否有联系方式的标识 (例如: 1表示有, 0表示无)。
    """
    finderUsername: str = Field(..., description="达人finder的username，唯一识别ID")
    openfinderid: Optional[str] = Field(None, description="达人finder的openfinderid")
    talentAppid: Optional[str] = Field(None, description="达人小店的AppID")
    昵称: Optional[str] = Field(None, description="达人昵称")
    头像: Optional[str] = Field(None, description="达人头像的URL")
    内容类型: Optional[List[Dict[str, Any]]] = Field([], description="达人内容类型对象列表")
    带货类目: Optional[List[Dict[str, Any]]] = Field([], description="达人带货类目对象列表")
    粉丝数文本: Optional[str] = Field(None, description="达人粉丝数文本")
    GMV文本: Optional[str] = Field(None, description="达人GMV文本")
    有无联系方式: Optional[int] = Field(None, description="是否有联系方式的标识")
    lastId: Optional[int] = Field(None, description="上一页最后一条记录的ID，用于高效分页")


class 提交微信达人联系方式模型(BaseModel):
    """
    用户提交微信达人联系方式的模型

    Attributes:
        finderUsername (str): 达人的finderUsername，用于唯一识别。
        电话 (Optional[str]): 达人的电话号码。
        微信 (Optional[str]): 达人的微信号。
    """
    finderUsername: str = Field(..., description="达人的finderUsername")
    电话: Optional[str] = Field(None, description="达人的电话号码")
    微信: Optional[str] = Field(None, description="达人的微信号")


class 分页请求模型(BaseModel):
    """
    一个通用的传统分页请求模型（基于页码）。
    """
    页码: int = Field(1, ge=1, description="页码，必须大于等于1")
    每页条数: int = Field(20, ge=1, le=100, description="每页返回的条目数，1到100之间")


class Keyset分页模型(BaseModel):
    """
    一个通用的高效分页请求模型（基于ID/keyset）。
    """
    每页条数: int = Field(20, ge=1, le=100, description="每页返回的条目数，1到100之间")
    lastId: Optional[int] = Field(None, description="上一页最后一条记录的ID，用于高效分页。如果是第一页，则不传此字段。")


class 微信达人公海筛选模型(Keyset分页模型):
    """
    用于筛选微信达人公海列表的模型。
    继承自Keyset分页模型，并添加了多个筛选字段。
    """
    昵称: Optional[str] = Field(None, description="根据达人昵称进行模糊搜索")
    内容类型: Optional[str] = Field(None, description="根据内容类型进行精确搜索")
    带货类目: Optional[str] = Field(None, description="根据带货类目进行精确搜索")
    粉丝数文本: Optional[str] = Field(None, description="根据粉丝数文本标签进行精确搜索，例如 '10-50万'")
    GMV文本: Optional[str] = Field(None, description="根据GMV文本标签进行精确搜索，例如 '小于1万'")
    有无联系方式: Optional[bool] = Field(None, description="是否筛选有联系方式的达人")


class 微信达人操作模型(BaseModel):
    """
    用于认领或取消认领微信达人的模型。
    """
    finderUsername: Optional[str] = Field(None, description="达人的finderUsername")
    id: Optional[int] = Field(None, description="达人的数据库ID")

    @model_validator(mode='after')
    def check_at_least_one_identifier(self):
        if not self.finderUsername and not self.id:
            raise ValueError('必须提供 finderUsername 或 id 中的至少一个')
        return self 