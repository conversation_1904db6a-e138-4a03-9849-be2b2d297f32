import asyncio
import json
import logging
import os
# 已使用统一日志系统替代
import time
from collections import deque
from datetime import datetime
from typing import Dict, List, Optional, Deque

from fastapi import WebSocket, WebSocketDisconnect, Query

from .日志配置 import 应用日志器 as 系统日志器

# 性能设置 - 可以通过环境变量调整
性能设置 = {
    "消息队列大小": int(os.environ.get("日志队列大小", "10000")),
    "历史缓存大小_全部": int(os.environ.get("日志缓存全部", "200")),
    "历史缓存大小_单类型": int(os.environ.get("日志缓存单类型", "100")),
    "最大连接数": int(os.environ.get("最大日志连接数", "50")),
    "批处理大小": int(os.environ.get("日志批处理大小", "10")),
    "批处理间隔": float(os.environ.get("日志批处理间隔", "0.1")),
    "低负载休眠": float(os.environ.get("日志低负载休眠", "0.1")),
    "高负载休眠": float(os.environ.get("日志高负载休眠", "1.0")),
    "负载阈值秒数": float(os.environ.get("日志负载阈值秒数", "60.0")),
}

# 根据系统负载自动调整设置
try:
    import psutil
    CPU核心数 = psutil.cpu_count(logical=False) or 1
    if CPU核心数 < 4:  # 低性能系统
        性能设置["消息队列大小"] = min(性能设置["消息队列大小"], 5000)
        性能设置["历史缓存大小_全部"] = min(性能设置["历史缓存大小_全部"], 100)
        性能设置["历史缓存大小_单类型"] = min(性能设置["历史缓存大小_单类型"], 50)
        性能设置["批处理大小"] = min(性能设置["批处理大小"], 5)
        性能设置["批处理间隔"] = max(性能设置["批处理间隔"], 0.2)
    系统日志器.info(f"实时日志系统性能配置已根据系统负载调整 (CPU核心数: {CPU核心数})")
except ImportError:
    pass

# 日志级别映射
日志级别映射 = {
    "DEBUG": logging.DEBUG,
    "INFO": logging.INFO,
    "WARNING": logging.WARNING,
    "ERROR": logging.ERROR,
    "CRITICAL": logging.CRITICAL
}

# 日志类型列表
有效日志类型 = ["系统", "错误", "接口", "数据库", "安全"]

class 日志WebSocket管理器:
    """管理WebSocket连接和日志广播"""
    
    def __init__(self):
        # 活跃的WebSocket连接
        self.活跃连接: Dict[str, List[WebSocket]] = {
            "all": [],  # 所有日志
            "系统": [],  # 系统日志
            "错误": [],  # 错误日志
            "接口": [],  # 接口日志
            "数据库": [],  # 数据库日志
            "安全": []   # 安全日志
        }
        
        # 客户端级别过滤器
        self.客户端级别: Dict[WebSocket, int] = {}
        self.客户端类型: Dict[WebSocket, str] = {}
        
        # 创建内部消息队列，限制大小
        self.消息队列 = asyncio.Queue(maxsize=性能设置["消息队列大小"])
        
        # 缓存最近的日志，用于新连接立即获取历史记录
        self.日志缓存: Dict[str, Deque] = {
            "all": deque(maxlen=性能设置["历史缓存大小_全部"]),
            "系统": deque(maxlen=性能设置["历史缓存大小_单类型"]),
            "错误": deque(maxlen=性能设置["历史缓存大小_单类型"]),
            "接口": deque(maxlen=性能设置["历史缓存大小_单类型"]),
            "数据库": deque(maxlen=性能设置["历史缓存大小_单类型"]),
            "安全": deque(maxlen=性能设置["历史缓存大小_单类型"])
        }
        
        # 连接速率限制
        self.连接计数 = 0
        self.最大连接数 = 性能设置["最大连接数"]
        self.连接限制锁 = asyncio.Lock()
        
        # 批处理设置
        self.批处理大小 = 性能设置["批处理大小"]
        self.批处理间隔 = 性能设置["批处理间隔"]
        
        # 启动日志处理任务
        self.处理任务 = None
        self.上次活跃时间 = time.time()
        
        # 活跃连接计数
        self.活跃连接计数 = 0
        
        # 性能监控
        self.处理计数 = 0
        self.丢弃计数 = 0
        self.上次统计时间 = time.time()
    
    async def 启动(self):
        """启动日志处理任务"""
        if self.处理任务 is None or self.处理任务.done():
            self.处理任务 = asyncio.create_task(self._处理日志消息())
            系统日志器.info(f"日志WebSocket管理器已启动 (最大连接数: {self.最大连接数}, 批处理大小: {self.批处理大小})")
            # 启动性能统计任务
            asyncio.create_task(self._性能统计任务())
    
    async def _性能统计任务(self):
        """定期记录性能统计信息"""
        try:
            while True:
                await asyncio.sleep(600)  # 每10分钟统计一次
                当前时间 = time.time()
                经过时间 = 当前时间 - self.上次统计时间
                
                if 经过时间 > 0 and self.处理计数 > 0:
                    每秒处理数 = self.处理计数 / 经过时间
                    系统日志器.info(
                        f"实时日志性能统计: 处理 {self.处理计数} 条日志, "
                        f"丢弃 {self.丢弃计数} 条, 平均 {每秒处理数:.2f}/秒, "
                        f"当前队列大小 {self.消息队列.qsize()}, 连接数 {self.活跃连接计数}"
                    )
                    # 重置计数
                    self.处理计数 = 0
                    self.丢弃计数 = 0
                    self.上次统计时间 = 当前时间
        except asyncio.CancelledError:
            pass
        except Exception as e:
            系统日志器.error(f"性能统计任务出错: {e}", exc_info=True)
    
    async def _处理日志消息(self):
        """批量处理日志消息队列"""
        try:
            while True:
                # 获取当前队列大小
                当前队列大小 = self.消息队列.qsize()
                
                # 如果队列为空或没有活跃连接，则休眠一段时间
                if 当前队列大小 == 0 or self.活跃连接计数 == 0:
                    # 如果超过设定时间没有活跃，进入长休眠模式
                    if time.time() - self.上次活跃时间 > 性能设置["负载阈值秒数"]:
                        await asyncio.sleep(性能设置["高负载休眠"])  # 长休眠
                    else:
                        await asyncio.sleep(性能设置["低负载休眠"])  # 短休眠
                    continue
                
                # 确定批处理大小，不超过当前队列大小和设定的批处理大小
                处理数量 = min(当前队列大小, self.批处理大小)
                批处理消息 = []
                
                # 从队列获取多条消息进行批处理
                for _ in range(处理数量):
                    try:
                        日志类型, 日志消息 = await asyncio.wait_for(
                            self.消息队列.get(), 
                            timeout=0.01
                        )
                        批处理消息.append((日志类型, 日志消息))
                        self.消息队列.task_done()
                    except asyncio.TimeoutError:
                        break
                
                # 如果获取到消息，进行处理
                if 批处理消息:
                    self.上次活跃时间 = time.time()
                    self.处理计数 += len(批处理消息)
                    
                    # 分类消息以优化广播
                    分类消息 = {}
                    for 日志类型, 日志消息 in 批处理消息:
                        if 日志类型 not in 分类消息:
                            分类消息[日志类型] = []
                        分类消息[日志类型].append(日志消息)
                        
                        # 同时添加到缓存
                        if 日志类型 in self.日志缓存:
                            self.日志缓存[日志类型].append(日志消息)
                        # 所有类型的缓存都添加
                        if 日志类型 != "all":
                            self.日志缓存["all"].append(日志消息)
                    
                    # 批量广播各类型的消息
                    for 类型, 消息列表 in 分类消息.items():
                        await self._批量广播消息(类型, 消息列表)
                
                # 短暂休眠，控制处理速率
                await asyncio.sleep(self.批处理间隔)
                
        except asyncio.CancelledError:
            系统日志器.info("日志处理任务已取消")
        except Exception as e:
            系统日志器.error(f"日志处理任务出错: {e}", exc_info=True)
            # 尝试重启任务
            self.处理任务 = asyncio.create_task(self._处理日志消息())
    
    async def _批量广播消息(self, 日志类型: str, 日志消息列表: List[dict]):
        """批量广播日志消息到客户端"""
        if not 日志消息列表:
            return
            
        # 为特定类型的连接准备消息
        if 日志类型 in self.活跃连接 and self.活跃连接[日志类型]:
            连接列表 = list(self.活跃连接[日志类型])
            for websocket in 连接列表:
                # 获取客户端级别过滤器
                客户端级别 = self.客户端级别.get(websocket, logging.INFO)
                
                # 筛选符合级别要求的消息
                筛选消息 = [
                    msg for msg in 日志消息列表
                    if 日志级别映射.get(msg.get("level", "INFO"), logging.INFO) >= 客户端级别
                ]
                
                if 筛选消息:
                    try:
                        # 批量发送消息
                        await websocket.send_text(json.dumps({"batch": True, "messages": 筛选消息}))
                    except Exception:
                        # 如果发送失败，移除连接
                        await self.断开连接(websocket)
        
        # 同时广播到订阅"all"类型的连接，如果日志类型不是"all"
        if 日志类型 != "all" and self.活跃连接["all"]:
            连接列表 = list(self.活跃连接["all"])
            for websocket in 连接列表:
                客户端级别 = self.客户端级别.get(websocket, logging.INFO)
                
                # 筛选符合级别要求的消息
                筛选消息 = [
                    msg for msg in 日志消息列表
                    if 日志级别映射.get(msg.get("level", "INFO"), logging.INFO) >= 客户端级别
                ]
                
                if 筛选消息:
                    try:
                        # 批量发送消息
                        await websocket.send_text(json.dumps({"batch": True, "messages": 筛选消息}))
                    except Exception:
                        # 如果发送失败，移除连接
                        await self.断开连接(websocket)
    
    async def 连接(self, websocket: WebSocket, 日志类型: str, 日志级别: str):
        """处理新的WebSocket连接"""
        # 检查连接数限制
        async with self.连接限制锁:
            if self.连接计数 >= self.最大连接数:
                await websocket.close(code=1008, reason="连接数超过限制")
                return
            self.连接计数 += 1
        
        try:
            # 接受WebSocket连接
            await websocket.accept()
            
            # 验证日志类型
            if 日志类型 not in self.活跃连接:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": f"无效的日志类型: {日志类型}，有效类型为: {', '.join(self.活跃连接.keys())}"
                }))
                await websocket.close()
                return
            
            # 设置客户端级别过滤器
            级别 = 日志级别映射.get(日志级别, logging.INFO)
            self.客户端级别[websocket] = 级别
            self.客户端类型[websocket] = 日志类型
            
            # 添加到活跃连接并更新计数
            self.活跃连接[日志类型].append(websocket)
            self.活跃连接计数 += 1
            self.上次活跃时间 = time.time()
            
            # 发送连接确认消息
            await websocket.send_text(json.dumps({
                "type": "system",
                "level": "INFO",
                "logger": "系统",
                "timestamp": datetime.now().isoformat(),
                "message": f"已连接到{日志类型}日志，级别: {日志级别}"
            }))
            
            # 发送缓存的历史日志
            缓存日志 = list(self.日志缓存[日志类型])
            if 缓存日志:
                # 根据级别筛选
                筛选日志 = [
                    msg for msg in 缓存日志
                    if 日志级别映射.get(msg.get("level", "INFO"), logging.INFO) >= 级别
                ]
                
                if 筛选日志:
                    await websocket.send_text(json.dumps({
                        "batch": True,
                        "history": True,
                        "messages": 筛选日志
                    }))
            
            系统日志器.info(f"新的WebSocket连接: 类型={日志类型}, 级别={日志级别}")
        except Exception as e:
            系统日志器.error(f"处理WebSocket连接时出错: {e}")
            # 确保连接计数正确
            async with self.连接限制锁:
                self.连接计数 -= 1
            raise
    
    async def 断开连接(self, websocket: WebSocket):
        """处理WebSocket断开连接"""
        # 从各类型连接中移除
        已移除 = False
        for 类型, 连接列表 in self.活跃连接.items():
            if websocket in 连接列表:
                连接列表.remove(websocket)
                已移除 = True
        
        # 清除客户端级别过滤器和类型
        if websocket in self.客户端级别:
            del self.客户端级别[websocket]
        
        if websocket in self.客户端类型:
            del self.客户端类型[websocket]
        
        # 更新计数
        if 已移除:
            self.活跃连接计数 -= 1
            
            # 释放连接槽位
            async with self.连接限制锁:
                self.连接计数 -= 1
    
    async def 添加日志(self, 日志类型: str, 日志消息: dict):
        """添加日志消息到队列，如果队列满则丢弃旧消息"""
        # 只有当有活跃连接时才添加日志，避免资源浪费
        if 日志类型 in self.活跃连接 and (self.活跃连接[日志类型] or self.活跃连接["all"]):
            try:
                # 非阻塞添加，如果队列满则丢弃
                await asyncio.wait_for(
                    self.消息队列.put((日志类型, 日志消息)),
                    timeout=0.01
                )
            except asyncio.TimeoutError:
                # 队列已满，记录警告并丢弃消息
                self.丢弃计数 += 1
                if self.丢弃计数 % 1000 == 0:  # 每丢弃1000条记录一次警告
                    系统日志器.warning(f"实时日志队列已满，已丢弃 {self.丢弃计数} 条日志消息")
    
    async def 关闭(self):
        """关闭所有连接和任务"""
        # 取消处理任务
        if self.处理任务:
            self.处理任务.cancel()
            try:
                await self.处理任务
            except asyncio.CancelledError:
                pass
        
        # 关闭所有WebSocket连接
        for 类型, 连接列表 in self.活跃连接.items():
            for websocket in list(连接列表):
                try:
                    await websocket.close()
                except Exception:
                    # 忽略关闭连接时的异常
                    pass
            连接列表.clear()
        
        # 清空客户端级别过滤器和类型
        self.客户端级别.clear()
        self.客户端类型.clear()
        
        # 重置计数
        self.活跃连接计数 = 0
        self.连接计数 = 0
        
        # 清空日志缓存
        for 队列 in self.日志缓存.values():
            队列.clear()


# 创建WebSocket管理器实例
实时日志管理器 = 日志WebSocket管理器()


# 自定义日志处理器，将日志转发到WebSocket
class WebSocket日志处理器(logging.Handler):
    """自定义日志处理器，将日志转发到WebSocket客户端"""
    
    def __init__(self, 日志类型: str):
        super().__init__()
        self.日志类型 = 日志类型
    
    def emit(self, record):
        """处理日志记录"""
        try:
            # 如果没有活跃连接，不处理日志
            if 实时日志管理器.活跃连接计数 == 0:
                return
                
            # 格式化日志消息
            消息 = self.format(record)
            
            # 创建JSON格式的日志消息
            日志数据 = {
                "timestamp": datetime.fromtimestamp(record.created).isoformat(),
                "logger": record.name,
                "level": record.levelname,
                "message": 消息
            }
            
            # 对ERROR及以上级别的日志添加详细信息
            if record.levelno >= logging.ERROR:
                日志数据.update({
                    "filename": record.filename,
                    "lineno": record.lineno,
                    "funcName": record.funcName
                })
                
                # 如果有异常信息，添加异常详情
                if record.exc_info:
                    import traceback
                    日志数据["exc_info"] = ''.join(traceback.format_exception(*record.exc_info))
            
            # 使用asyncio添加到队列
            asyncio.create_task(实时日志管理器.添加日志(self.日志类型, 日志数据))
        except Exception as e:
            # 避免处理器内部错误导致无限递归
            print(f"WebSocket日志处理器错误: {e}")


# 初始化函数
async def 初始化实时日志系统():
    """初始化实时日志系统"""
    # 启动WebSocket管理器
    await 实时日志管理器.启动()
    
    # 配置各类型日志记录器的WebSocket处理器
    处理器映射 = {
        "系统": ("系统", logging.getLogger("系统")),
        "错误": ("错误", logging.getLogger("错误")),
        "接口": ("接口", logging.getLogger("接口")),
        "数据库": ("数据库", logging.getLogger("数据库")),
        "安全": ("安全", logging.getLogger("安全"))
    }
    
    # 添加WebSocket处理器到各记录器
    for 类型, (名称, 记录器) in 处理器映射.items():
        # 创建处理器
        处理器 = WebSocket日志处理器(类型)
        处理器.setLevel(logging.DEBUG)  # 设置为最低级别，在客户端进行过滤
        
        # 设置格式化器
        格式化器 = logging.Formatter('%(message)s')
        处理器.setFormatter(格式化器)
        
        # 添加到记录器
        记录器.addHandler(处理器)
    
    系统日志器.info("实时日志系统已初始化")


# 路由依赖项
async def 获取日志类型和级别(
    type: Optional[str] = Query("all", description="日志类型: all, 系统, 错误, 接口, 数据库, 安全"),
    level: str = Query("INFO", description="最低日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL")
):
    """验证并返回日志类型和级别"""
    # 验证日志类型
    日志类型 = type.lower() if type else "all"
    if 日志类型 != "all" and 日志类型 not in [t.lower() for t in 有效日志类型]:
        日志类型 = "all"
    
    # 将小写转换为原始格式
    if 日志类型 != "all":
        for t in 有效日志类型:
            if t.lower() == 日志类型:
                日志类型 = t
                break
    
    # 验证日志级别
    日志级别 = level.upper() if level else "INFO"
    if 日志级别 not in 日志级别映射:
        日志级别 = "INFO"
    
    return 日志类型, 日志级别


# WebSocket连接路由处理函数
async def 处理日志WebSocket连接(websocket: WebSocket, 日志类型: str, 日志级别: str):
    """处理WebSocket连接请求"""
    # 连接到管理器
    await 实时日志管理器.连接(websocket, 日志类型, 日志级别)
    
    try:
        # 保持连接，直到客户端断开
        while True:
            # 等待消息，主要用于检测连接断开
            await websocket.receive_text()
    except WebSocketDisconnect:
        # 处理断开连接
        await 实时日志管理器.断开连接(websocket)
    except Exception as e:
        系统日志器.error(f"WebSocket连接错误: {e}")
        # 确保连接被关闭
        await 实时日志管理器.断开连接(websocket) 