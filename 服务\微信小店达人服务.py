from 数据.微信小店达人数据 import (
    写入或更新微信小店达人,
    根据finderUsername获取微信小店达人,
    获取或创建联系方式,
    关联微信小店达人与联系方式,
    检查用户是否已认领微信小店达人,
    为用户认领微信小店达人,
    获取微信小店达人公海列表,
    获取用户认领的微信小店达人列表,
    根据id获取微信小店达人,
    获取微信小店达人关联的联系方式,
    取消认领微信小店达人
)
from 数据模型.微信小店模型 import (
    微信小店达人写入模型,
    提交微信达人联系方式模型,
    分页请求模型,
    微信达人公海筛选模型,
    微信达人操作模型
)
from 日志.日志配置 import logger

async def 服务_写入或更新微信小店达人(达人数据: 微信小店达人写入模型):
    """
    服务层：处理写入或更新微信小店达人的业务逻辑。
    - 成功时返回统一的成功响应。
    - 失败时返回具体的业务错误码和信息。
    """
    try:
        # 调用数据层函数执行数据库操作
        result = await 写入或更新微信小店达人(达人数据)
        
        # 成功的响应
        return {"status": 100, "message": "OK", "data": result}

    except ValueError as e:
        # 捕获数据校验错误
        logger.warning(f"写入微信小店达人失败，数据校验错误: {e}")
        return {"status": 10011, "message": "您提交的数据不符合要求，请检查后重试。", "data": None}
        
    except IOError as e:
        # 捕获数据库IO错误
        logger.error(f"写入微信小店达人时发生服务层错误: {e}")
        return {"status": 10012, "message": "系统繁忙，保存达人信息失败，请稍后重试。", "data": None}
        
    except Exception as e:
        # 捕获其他所有未知异常
        logger.exception(f"处理微信小店达人写入时发生未知异常: {e}")
        return {"status": 10013, "message": "服务器发生未知错误", "data": None}

async def 服务_提交微信小店达人联系方式(联系方式数据: 提交微信达人联系方式模型, 用户id: int):
    """
    服务层：处理用户提交微信达人联系方式的业务逻辑。
    """
    try:
        # 1. 根据finderUsername查找达人
        达人 = await 根据finderUsername获取微信小店达人(联系方式数据.finderUsername)
        if not 达人:
            return {"status": 404, "message": "未找到指定的达人"}

        微信达人id = 达人['id']
        processed_contacts = []

        # 2. 处理电话
        if 联系方式数据.电话:
            电话ID = await 获取或创建联系方式(联系方式数据.电话, "电话")
            await 关联微信小店达人与联系方式(微信达人id, 电话ID)
            processed_contacts.append("电话")

        # 3. 处理微信
        if 联系方式数据.微信:
            微信id = await 获取或创建联系方式(联系方式数据.微信, "微信")
            await 关联微信小店达人与联系方式(微信达人id, 微信id)
            processed_contacts.append("微信")

        if not processed_contacts:
            return {"status": 400, "message": "请至少提供一种联系方式（电话或微信）"}

        # 4. 检查并为用户认领达人
        已认领 = await 检查用户是否已认领微信小店达人(用户id, 微信达人id)
        if not 已认领:
            await 为用户认领微信小店达人(用户id, 微信达人id)
            claim_status = "首次认领成功"
        else:
            claim_status = "您已认领过该达人"

        return {
            "status": 100,
            "message": "联系方式处理成功",
            "data": {
                "processed_contacts": processed_contacts,
                "claim_status": claim_status
            }
        }

    except Exception as e:
        logger.exception(f"提交达人联系方式时发生未知异常: {e}")
        return {"status": 500, "message": "服务器内部错误"}

async def 服务_获取微信小店达人公海(筛选条件: 微信达人公海筛选模型):
    """服务层：获取未被认领的微信小店达人列表。"""
    try:
        records = await 获取微信小店达人公海列表(筛选条件)
        return {
            "status": 100,
            "message": "OK",
            "data": {"list": records}
        }
    except Exception as e:
        logger.exception(f"获取达人公海列表时发生未知异常: {e}")
        return {"status": 500, "message": "服务器内部错误"}

async def 服务_获取用户认领的微信小店达人列表(用户id: int, 分页: 分页请求模型):
    """服务层：获取当前用户认领的达人列表。"""
    try:
        records, total = await 获取用户认领的微信小店达人列表(用户id, 分页.页码, 分页.每页条数)
        return {
            "status": 100,
            "message": "OK",
            "data": {"list": records, "total": total, "page": 分页.页码, "pageSize": 分页.每页条数}
        }
    except Exception as e:
        logger.exception(f"获取用户认领的达人列表时发生未知异常: {e}")
        return {"status": 500, "message": "服务器内部错误"}

async def 服务_获取微信小店达人详情(操作数据: 微信达人操作模型):
    """服务层：获取单个微信小店达人的详细信息。"""
    try:
        达人信息 = None
        if 操作数据.id:
            达人信息 = await 根据id获取微信小店达人(操作数据.id)
        elif 操作数据.finderUsername:
            达人信息 = await 根据finderUsername获取微信小店达人(操作数据.finderUsername)

        if not 达人信息:
            return {"status": 404, "message": "未找到指定的达人"}

        联系方式 = await 获取微信小店达人关联的联系方式(达人信息['id'])
        达人信息['联系方式'] = 联系方式

        return {"status": 100, "message": "OK", "data": 达人信息}
    except Exception as e:
        logger.exception(f"获取达人详情时发生未知异常: {e}")
        return {"status": 500, "message": "服务器内部错误"}

async def 服务_认领微信小店达人(操作数据: 微信达人操作模型, 用户id: int):
    """服务层：认领一个微信小店达人。"""
    try:
        达人信息 = None
        if 操作数据.id:
            达人信息 = await 根据id获取微信小店达人(操作数据.id)
        elif 操作数据.finderUsername:
            达人信息 = await 根据finderUsername获取微信小店达人(操作数据.finderUsername)

        if not 达人信息:
            return {"status": 404, "message": "未找到指定的达人"}

        微信达人id = 达人信息['id']
        已认领 = await 检查用户是否已认领微信小店达人(用户id, 微信达人id)
        if 已认领:
            return {"status": 400, "message": "您已经认领过该达人，请勿重复操作"}

        await 为用户认领微信小店达人(用户id, 微信达人id)
        return {"status": 100, "message": "认领成功"}
    except Exception as e:
        logger.exception(f"认领达人时发生未知异常: {e}")
        return {"status": 500, "message": "服务器内部错误"}

async def 服务_取消认领微信小店达人(操作数据: 微信达人操作模型, 用户id: int):
    """服务层：取消认领一个微信小店达人。"""
    try:
        达人信息 = None
        if 操作数据.id:
            达人信息 = await 根据id获取微信小店达人(操作数据.id)
        elif 操作数据.finderUsername:
            达人信息 = await 根据finderUsername获取微信小店达人(操作数据.finderUsername)

        if not 达人信息:
            return {"status": 404, "message": "未找到指定的达人"}

        微信达人id = 达人信息['id']
        rows_affected = await 取消认领微信小店达人(用户id, 微信达人id)

        if rows_affected > 0:
            return {"status": 100, "message": "取消认领成功"}
        else:
            return {"status": 400, "message": "您尚未认领该达人或操作失败"}
    except Exception as e:
        logger.exception(f"取消认领达人时发生未知异常: {e}")
        return {"status": 500, "message": "服务器内部错误"} 