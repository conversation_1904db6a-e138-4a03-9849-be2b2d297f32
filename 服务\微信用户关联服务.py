"""
微信用户关联服务
用于处理用户与微信账号的关联管理
"""

import traceback
from typing import Any, Dict, Optional

# 导入状态码
import 状态

# 导入正确的数据库连接
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

# 导入统一日志系统
from 日志 import 应用日志器, 错误日志器


async def 异步获取用户绑定微信服务(用户id: int) -> Dict[str, Any]:
    """
    获取用户绑定的所有微信账号 - 优化版本

    包含完整的时间统计和活跃度分析

    Args:
        用户id: 用户id

    Returns:
        Dict: 包含状态和数据的字典
    """
    try:
        from 工具.微信绑定工具 import 微信绑定工具

        应用日志器.info(f"开始获取用户绑定微信账号，用户id: {用户id}")

        # 查询用户绑定的微信账号，包含完整时间信息和好友数量统计
        查询SQL = """
        SELECT 
            uwx.id as 关联id,
            uwx.微信id,
            uwx.绑定时间,
            uwx.更新时间,
            uwx.状态,
            uwx.备注,
            wx.微信号,
            wx.修改时间,
            COALESCE(friend_count.好友数量, 0) as 好友数量,
            -- {{ AURA-X: Modify - 修复PostgreSQL时间函数语法. Approval: 寸止(ID:1735372800). }}
            -- {{ Source: PostgreSQL时间函数文档 }}
            EXTRACT(DAY FROM (NOW() - uwx.绑定时间)) as 绑定天数,
            CASE
                WHEN uwx.更新时间 >= NOW() - INTERVAL '7 days' THEN '近期活跃'
                WHEN uwx.更新时间 >= NOW() - INTERVAL '30 days' THEN '较少活跃'
                ELSE '很少活跃'
            END as 活跃状态
        FROM 用户微信关联表 uwx
        INNER JOIN 微信信息表 wx ON uwx.微信id = wx.id
        LEFT JOIN (
            SELECT 我方微信号id, COUNT(*) as 好友数量
            FROM 微信好友表
            GROUP BY 我方微信号id
        ) friend_count ON wx.id = friend_count.我方微信号id
        WHERE uwx.用户id = $1 AND uwx.状态 = 1
        ORDER BY uwx.绑定时间 DESC
        """

        结果 = await 异步连接池实例.执行查询(查询SQL, [用户id])

        # 处理查询结果，转换datetime对象为字符串
        总好友数 = 0
        活跃账号数 = 0

        for 记录 in 结果:
            # 处理时间字段
            if "绑定时间" in 记录 and 记录["绑定时间"]:
                记录["绑定时间"] = 记录["绑定时间"].strftime("%Y-%m-%d %H:%M:%S")
            if "更新时间" in 记录 and 记录["更新时间"]:
                记录["更新时间"] = 记录["更新时间"].strftime("%Y-%m-%d %H:%M:%S")
            if "修改时间" in 记录 and 记录["修改时间"]:
                记录["修改时间"] = 记录["修改时间"].strftime("%Y-%m-%d %H:%M:%S")

            # 累计统计
            总好友数 += 记录.get("好友数量", 0)
            if 记录.get("活跃状态") == "近期活跃":
                活跃账号数 += 1

        # 获取详细的时间统计信息
        时间统计 = await 微信绑定工具.获取用户绑定时间统计(用户id)

        应用日志器.info(
            f"用户 {用户id} 绑定的微信账号数量: {len(结果)}, 总好友数: {总好友数}, 活跃账号数: {活跃账号数}"
        )

        return {
            "status": 状态.通用.成功,
            "message": "获取用户绑定微信账号成功",
            "data": {
                "绑定微信列表": 结果,
                "总数": len(结果),
                "总好友数": 总好友数,
                "活跃账号数": 活跃账号数,
                "时间统计": 时间统计,
                "统计信息": {
                    "微信账号数": len(结果),
                    "总好友数": 总好友数,
                    "平均好友数": round(总好友数 / len(结果), 1)
                    if len(结果) > 0
                    else 0,
                    "活跃账号数": 活跃账号数,
                    "活跃率": round((活跃账号数 / len(结果)) * 100, 1)
                    if len(结果) > 0
                    else 0,
                },
            },
        }

    except Exception as e:
        错误消息 = f"获取用户绑定微信账号失败: {str(e)}"
        错误日志器.error(f"{错误消息}\n{traceback.format_exc()}")
        return {"status": 状态.通用.服务器错误, "message": 错误消息, "data": None}


def 构建好友排序SQL(排序字段: Optional[str] = None, 排序方向: str = "DESC") -> str:
    """
    构建好友列表排序SQL子句

    Args:
        排序字段: 排序字段名称
        排序方向: 排序方向 ASC/DESC

    Returns:
        str: ORDER BY SQL子句
    """
    # 字段映射：前端字段名 -> 数据库字段名
    字段映射 = {
        "对方最后发消息时间": "f.对方最后一条消息发送时间",
        "我方最后发消息时间": "f.我方最后一条消息发送时间",
        "入库时间": "f.好友入库时间"
    }

    # 验证排序方向
    if 排序方向 not in ["ASC", "DESC"]:
        排序方向 = "DESC"

    # 如果指定了排序字段且字段有效
    if 排序字段 and 排序字段 in 字段映射:
        数据库字段 = 字段映射[排序字段]
        return f" ORDER BY {数据库字段} {排序方向}"

    # 默认排序：按好友关系建立时间降序
    return " ORDER BY COALESCE(f.好友通过时间, f.发送请求时间, f.好友入库时间, f.创建时间) DESC"


async def 异步获取用户所有微信好友服务(查询参数: Dict[str, Any]) -> Dict[str, Any]:
    """
    获取用户所有绑定微信的好友列表

    Args:
        查询参数: 包含用户id、分页等信息的字典

    Returns:
        Dict: 包含状态和数据的字典
    """
    try:
        用户id = 查询参数.get("用户id")
        页码 = 查询参数.get("页码", 1)
        每页条数 = 查询参数.get("每页条数", 20)
        微信id = 查询参数.get("微信id")
        关键词 = 查询参数.get("关键词")
        排序字段 = 查询参数.get("排序字段")
        排序方向 = 查询参数.get("排序方向", "DESC")
        # 好友类型 = 查询参数.get("好友类型")  # 暂时不使用

        应用日志器.info(f"开始获取用户所有微信好友，用户id: {用户id}, 排序: {排序字段} {排序方向}")

        # 构建基础查询SQL
        基础SQL = """
        SELECT
            f.我方微信号id,
            f.对方微信号id,
            f.识别id,
            f.备注,
            f.创建时间,
            f.发送请求时间,
            f.好友通过时间,
            f.好友入库时间,
            f.我方最后一条消息发送时间,
            f.对方最后一条消息发送时间,
            my_wx.微信号 as 我方微信号,
            friend_wx.微信号 as 对方微信号
        FROM 微信好友表 f
        INNER JOIN 用户微信关联表 uwx ON f.我方微信号id = uwx.微信id
        INNER JOIN 微信信息表 my_wx ON f.我方微信号id = my_wx.id
        INNER JOIN 微信信息表 friend_wx ON f.对方微信号id = friend_wx.id
        WHERE uwx.用户id = $1 AND uwx.状态 = 1
        """

        查询条件 = [用户id]
        参数索引 = 2

        # 添加筛选条件
        if 微信id:
            基础SQL += f" AND f.我方微信号id = ${参数索引}"
            查询条件.append(微信id)
            参数索引 += 1

        if 关键词:
            基础SQL += f" AND (friend_wx.微信号 LIKE ${参数索引})"
            查询条件.append(f"%{关键词}%")
            参数索引 += 1

        # 计算总数
        计数SQL = f"SELECT COUNT(*) as total FROM ({基础SQL}) as count_query"
        总数结果 = await 异步连接池实例.执行查询(计数SQL, 查询条件)
        总数 = 总数结果[0]["total"] if 总数结果 else 0

        # 添加排序和分页 - 使用动态排序逻辑
        排序SQL = 构建好友排序SQL(排序字段, 排序方向)
        基础SQL += f"{排序SQL} LIMIT ${参数索引} OFFSET ${参数索引 + 1}"
        查询条件.extend([每页条数, (页码 - 1) * 每页条数])

        # 执行查询
        结果 = await 异步连接池实例.执行查询(基础SQL, 查询条件)

        # 处理查询结果，转换datetime对象为字符串
        for 记录 in 结果:
            # 处理所有时间字段
            时间字段列表 = ["创建时间", "发送请求时间", "好友通过时间", "好友入库时间", "我方最后一条消息发送时间", "对方最后一条消息发送时间"]
            for 时间字段 in 时间字段列表:
                if 时间字段 in 记录 and 记录[时间字段]:
                    记录[时间字段] = 记录[时间字段].strftime("%Y-%m-%d %H:%M:%S")

        应用日志器.info(
            f"用户 {用户id} 的微信好友总数: {总数}, 当前页数据: {len(结果)}"
        )

        return {
            "status": 状态.通用.成功,
            "message": "获取用户微信好友成功",
            "data": {
                "好友列表": 结果,
                "总数": 总数,
                "页码": 页码,
                "每页条数": 每页条数,
                "总页数": (总数 + 每页条数 - 1) // 每页条数 if 总数 > 0 else 0,
            },
        }

    except Exception as e:
        错误消息 = f"获取用户微信好友失败: {str(e)}"
        错误日志器.error(f"{错误消息}\n{traceback.format_exc()}")
        return {"status": 状态.通用.服务器错误, "message": 错误消息, "data": None}


async def 异步绑定用户微信服务(绑定数据: Dict[str, Any]) -> Dict[str, Any]:
    """
    绑定微信账号到用户 - 终极优化版本

    使用工具函数实现高效、优雅的绑定逻辑

    Args:
        绑定数据: 包含用户id、微信号等信息的字典

    Returns:
        Dict: 包含状态和数据的字典
    """
    try:
        from 工具.微信绑定工具 import 微信绑定工具

        用户id = 绑定数据.get("用户id")
        微信号 = 绑定数据.get("微信号", "").strip()
        备注 = 绑定数据.get("备注", "")
        # 新增支持的字段
        昵称 = 绑定数据.get("昵称", "")
        绑定手机号 = 绑定数据.get("绑定手机号", "")
        头像 = 绑定数据.get("头像", "")

        # 1. 基础参数验证
        if not 用户id:
            return {
                "status": 状态.通用.参数错误,
                "message": "用户id不能为空",
                "data": None,
            }

        # 2. 微信号格式验证
        格式验证结果 = 微信绑定工具.验证微信号格式(微信号)
        if not 格式验证结果.get("有效"):
            return {
                "status": 状态.通用.参数错误,
                "message": 格式验证结果.get("错误"),
                "data": None,
            }

        微信号 = 格式验证结果.get("微信号")  # 使用处理后的微信号

        # 3. 备注内容验证
        备注验证结果 = 微信绑定工具.验证备注内容(备注)
        if not 备注验证结果.get("有效"):
            return {
                "status": 状态.通用.参数错误,
                "message": 备注验证结果.get("错误"),
                "data": None,
            }

        备注 = 备注验证结果.get("备注")  # 使用处理后的备注

        应用日志器.info(f"开始绑定微信账号，用户id: {用户id}, 微信号: {微信号}")

        # 4. 检查是否被其他用户绑定
        重复检查结果 = await 微信绑定工具.检查微信号重复绑定(微信号, 用户id)
        if 重复检查结果.get("已绑定"):
            return {
                "status": 状态.通用.参数错误,
                "message": 重复检查结果.get("错误"),
                "data": None,
            }

        # 5. 检查当前用户的绑定状态
        绑定状态结果 = await 微信绑定工具.检查用户绑定状态(用户id, 微信号)

        if 绑定状态结果.get("存在绑定"):
            if 绑定状态结果.get("是否激活"):
                return {
                    "status": 状态.通用.参数错误,
                    "message": "该微信号已绑定到当前用户",
                    "data": None,
                }

            # 重新激活已存在的绑定关系
            关联id = 绑定状态结果.get("关联id")
            微信id = 绑定状态结果.get("微信id")

            await 异步连接池实例.执行更新(
                "UPDATE 用户微信关联表 SET 状态 = 1, 备注 = $1, 更新时间 = NOW() WHERE id = $2",
                (备注, 关联id),
            )
            应用日志器.info(f"重新激活绑定关系，关联id: {关联id}")
        else:
            # 6. 创建新的绑定关系
            # 首先确保微信信息表中有该微信号记录
            查询微信SQL = "SELECT id FROM 微信信息表 WHERE 微信号 = $1"
            微信记录 = await 异步连接池实例.执行查询(查询微信SQL, (微信号,))

            if 微信记录:
                微信id = 微信记录[0]["id"]
                # 如果提供了额外字段，更新微信信息表
                if 昵称 or 绑定手机号 or 头像:
                    更新字段 = []
                    更新参数 = []
                    参数索引 = 1

                    if 昵称:
                        更新字段.append(f"昵称 = ${参数索引}")
                        更新参数.append(昵称)
                        参数索引 += 1
                    if 绑定手机号:
                        更新字段.append(f"绑定手机号 = ${参数索引}")
                        更新参数.append(绑定手机号)
                        参数索引 += 1
                    if 头像:
                        更新字段.append(f"头像 = ${参数索引}")
                        更新参数.append(头像)
                        参数索引 += 1

                    if 更新字段:
                        更新字段.append("修改时间 = NOW()")
                        更新参数.append(微信id)

                        更新SQL = (
                            f"UPDATE 微信信息表 SET {', '.join(更新字段)} WHERE id = $1"
                        )
                        await 异步连接池实例.执行更新(更新SQL, 更新参数)
                        应用日志器.info(f"更新微信信息，微信id: {微信id}")
            else:
                # 创建新微信记录，包含所有提供的字段
                插入字段 = ["微信号", "修改时间"]
                插入值 = [微信号]
                插入占位符 = ["$1", "NOW()"]
                参数索引 = 2

                if 昵称:
                    插入字段.append("昵称")
                    插入值.append(昵称)
                    插入占位符.append(f"${参数索引}")
                    参数索引 += 1
                if 绑定手机号:
                    插入字段.append("绑定手机号")
                    插入值.append(绑定手机号)
                    插入占位符.append(f"${参数索引}")
                    参数索引 += 1
                if 头像:
                    插入字段.append("头像")
                    插入值.append(头像)
                    插入占位符.append(f"${参数索引}")
                    参数索引 += 1

                插入SQL = f"INSERT INTO 微信信息表 ({', '.join(插入字段)}) VALUES ({', '.join(插入占位符)})"
                微信id = await 异步连接池实例.执行插入(插入SQL, 插入值)
                应用日志器.info(f"创建新微信记录，微信id: {微信id}")

            # 创建绑定关系
            关联id = await 异步连接池实例.执行插入(
                """INSERT INTO 用户微信关联表 (用户id, 微信id, 绑定时间, 状态, 备注, 更新时间)
                   VALUES ($1, $2, NOW(), 1, $3, NOW())""",
                (用户id, 微信id, 备注),
            )
            应用日志器.info(f"创建新绑定关系，关联id: {关联id}")

        # 7. 获取用户当前绑定总数和时间统计（用于返回给前端）
        绑定总数 = await 微信绑定工具.获取用户绑定数量(用户id)

        # 8. 获取绑定时间统计信息
        时间统计 = await 微信绑定工具.获取用户绑定时间统计(用户id)

        return {
            "status": 状态.通用.成功,
            "message": "绑定微信账号成功",
            "data": {
                "关联id": 关联id,
                "微信id": 微信id,
                "微信号": 微信号,
                "绑定总数": 绑定总数,
                "绑定时间统计": 时间统计,
            },
        }

    except Exception as e:
        错误消息 = f"绑定微信账号失败: {str(e)}"
        错误日志器.error(f"{错误消息}\n{traceback.format_exc()}")
        return {
            "status": 状态.通用.服务器错误,
            "message": "绑定微信账号服务异常",
            "data": None,
        }


async def 异步解绑用户微信服务(解绑数据: Dict[str, Any]) -> Dict[str, Any]:
    """
    解绑用户微信账号

    Args:
        解绑数据: 包含用户id、微信id的字典

    Returns:
        Dict: 包含状态和数据的字典
    """
    try:
        用户id = 解绑数据.get("用户id")
        微信id = 解绑数据.get("微信id")

        应用日志器.info(f"开始解绑微信账号，用户id: {用户id}, 微信id: {微信id}")

        # 检查绑定关系是否存在且属于该用户
        查询关联SQL = "SELECT id FROM 用户微信关联表 WHERE 用户id = $1 AND 微信id = $2 AND 状态 = 1"
        关联记录 = await 异步连接池实例.执行查询(查询关联SQL, [用户id, 微信id])

        if not 关联记录:
            return {
                "status": 状态.通用.参数错误,
                "message": "未找到有效的绑定关系",
                "data": None,
            }

        # 更新状态为已解绑
        更新状态SQL = (
            "UPDATE 用户微信关联表 SET 状态 = 0, 更新时间 = NOW() WHERE id = $1"
        )
        await 异步连接池实例.执行更新(更新状态SQL, [关联记录[0]["id"]])

        应用日志器.info(f"成功解绑微信账号，关联id: {关联记录[0]['id']}")

        return {
            "status": 状态.通用.成功,
            "message": "解绑微信账号成功",
            "data": {"关联id": 关联记录[0]["id"]},
        }

    except Exception as e:
        错误消息 = f"解绑微信账号失败: {str(e)}"
        错误日志器.error(f"{错误消息}\n{traceback.format_exc()}")
        return {"status": 状态.通用.服务器错误, "message": 错误消息, "data": None}


async def 异步获取指定微信好友服务(查询参数: Dict[str, Any]) -> Dict[str, Any]:
    """
    获取指定微信账号的好友列表

    Args:
        查询参数: 包含用户id、微信id、分页等信息的字典

    Returns:
        Dict: 包含状态和数据的字典
    """
    try:
        用户id = 查询参数.get("用户id")
        微信id = 查询参数.get("微信id")
        页码 = 查询参数.get("页码", 1)
        每页条数 = 查询参数.get("每页条数", 20)
        关键词 = 查询参数.get("关键词")
        排序字段 = 查询参数.get("排序字段")
        排序方向 = 查询参数.get("排序方向", "DESC")
        # 好友类型 = 查询参数.get("好友类型")  # 暂时不使用

        应用日志器.info(f"开始获取指定微信好友，用户id: {用户id}, 微信id: {微信id}, 排序: {排序字段} {排序方向}")

        # 首先验证该微信账号是否属于该用户
        验证关联SQL = "SELECT id FROM 用户微信关联表 WHERE 用户id = $1 AND 微信id = $2 AND 状态 = 1"
        关联验证 = await 异步连接池实例.执行查询(验证关联SQL, [用户id, 微信id])

        if not 关联验证:
            return {
                "status": 状态.通用.失败,
                "message": "该微信账号不属于当前用户",
                "data": None,
            }

        # 构建查询SQL
        基础SQL = """
        SELECT
            f.我方微信号id,
            f.对方微信号id,
            f.识别id,
            f.备注,
            f.创建时间,
            f.发送请求时间,
            f.好友通过时间,
            f.好友入库时间,
            f.我方最后一条消息发送时间,
            f.对方最后一条消息发送时间,
            friend_wx.微信号 as 对方微信号
        FROM 微信好友表 f
        INNER JOIN 微信信息表 friend_wx ON f.对方微信号id = friend_wx.id
        WHERE f.我方微信号id = $1
        """

        查询条件 = [微信id]

        # 添加筛选条件
        if 关键词:
            基础SQL += " AND (friend_wx.微信号 LIKE $2)"
            查询条件.append(f"%{关键词}%")

        # 计算总数
        计数SQL = f"SELECT COUNT(*) as total FROM ({基础SQL}) as count_query"
        总数结果 = await 异步连接池实例.执行查询(计数SQL, 查询条件)
        总数 = 总数结果[0]["total"] if 总数结果 else 0

        # 添加排序和分页 - 使用动态排序逻辑
        排序SQL = 构建好友排序SQL(排序字段, 排序方向)
        基础SQL += f"{排序SQL} LIMIT $2 OFFSET $3"
        查询条件.extend([每页条数, (页码 - 1) * 每页条数])

        # 执行查询
        结果 = await 异步连接池实例.执行查询(基础SQL, 查询条件)

        # 处理查询结果，转换datetime对象为字符串
        for 记录 in 结果:
            # 处理所有时间字段
            时间字段列表 = ["创建时间", "发送请求时间", "好友通过时间", "好友入库时间", "我方最后一条消息发送时间", "对方最后一条消息发送时间"]
            for 时间字段 in 时间字段列表:
                if 时间字段 in 记录 and 记录[时间字段]:
                    记录[时间字段] = 记录[时间字段].strftime("%Y-%m-%d %H:%M:%S")

        应用日志器.info(f"微信id {微信id} 的好友总数: {总数}, 当前页数据: {len(结果)}")

        return {
            "status": 状态.通用.成功,
            "message": "获取微信好友成功",
            "data": {
                "好友列表": 结果,
                "总数": 总数,
                "页码": 页码,
                "每页条数": 每页条数,
                "总页数": (总数 + 每页条数 - 1) // 每页条数 if 总数 > 0 else 0,
            },
        }

    except Exception as e:
        错误消息 = f"获取指定微信好友失败: {str(e)}"
        错误日志器.error(f"{错误消息}\n{traceback.format_exc()}")
        return {"status": 状态.通用.服务器错误, "message": 错误消息, "data": None}


async def 异步获取用户微信统计概览服务(用户id: int) -> Dict[str, Any]:
    """
    获取用户微信和好友的统计概览信息

    Args:
        用户id: 用户id

    Returns:
        Dict: 包含状态和统计数据的字典
    """
    try:
        应用日志器.info(f"开始获取用户微信统计概览，用户id: {用户id}")

        # 查询用户的微信账号和好友统计
        统计SQL = """
        SELECT 
            COUNT(DISTINCT uwx.微信id) as 微信账号数,
            COUNT(DISTINCT f.对方微信号id) as 总好友数,
            COUNT(DISTINCT CASE WHEN uwx.状态 = 1 THEN uwx.微信id END) as 正常微信数,
            MAX(uwx.绑定时间) as 最近绑定时间
        FROM 用户微信关联表 uwx
        LEFT JOIN 微信好友表 f ON uwx.微信id = f.我方微信号id
        WHERE uwx.用户id = $1
        """

        统计结果 = await 异步连接池实例.执行查询(统计SQL, [用户id])

        if 统计结果:
            统计数据 = 统计结果[0]

            # 处理时间字段
            if "最近绑定时间" in 统计数据 and 统计数据["最近绑定时间"]:
                统计数据["最近绑定时间"] = 统计数据["最近绑定时间"].strftime(
                    "%Y-%m-%d %H:%M:%S"
                )

            # 计算平均好友数
            微信数 = 统计数据.get("正常微信数", 0)
            总好友数 = 统计数据.get("总好友数", 0)
            平均好友数 = round(总好友数 / 微信数, 1) if 微信数 > 0 else 0

            统计数据["平均好友数"] = 平均好友数

            应用日志器.info(f"用户 {用户id} 统计概览: 微信{微信数}个, 好友{总好友数}个")

            return {
                "status": 状态.通用.成功,
                "message": "获取统计概览成功",
                "data": {
                    "统计概览": 统计数据,
                    "详细统计": {
                        "微信账号": {
                            "总数": 统计数据.get("微信账号数", 0),
                            "正常数": 统计数据.get("正常微信数", 0),
                            "最近绑定": 统计数据.get("最近绑定时间"),
                        },
                        "好友数据": {"总好友数": 总好友数, "平均好友数": 平均好友数},
                    },
                },
            }
        else:
            return {
                "status": 状态.通用.成功,
                "message": "暂无统计数据",
                "data": {
                    "统计概览": {
                        "微信账号数": 0,
                        "总好友数": 0,
                        "正常微信数": 0,
                        "平均好友数": 0,
                        "最近绑定时间": None,
                    }
                },
            }

    except Exception as e:
        错误消息 = f"获取用户微信统计概览失败: {str(e)}"
        错误日志器.error(f"{错误消息}\n{traceback.format_exc()}")
        return {"status": 状态.通用.服务器错误, "message": 错误消息, "data": None}
