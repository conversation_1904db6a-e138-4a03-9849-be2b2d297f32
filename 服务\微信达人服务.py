"""
微信达人管理服务层

功能概述：
- 提供微信达人管理的业务逻辑处理
- 与抖音达人服务保持一致的接口设计
- 支持达人查询、认领、编辑等核心功能
- 提供统一的错误处理和日志记录

作者: CRM系统开发团队
创建时间: 2024-06-25
"""

from typing import Dict, Optional, Any

# 保留部分MySQL函数（暂时兼容，后续逐步迁移）
from 数据.微信达人数据 import (
    异步获取微信达人列表, 异步获取微信达人详情,
    异步取消认领微信达人, 异步获取用户认领微信达人列表, 异步创建或更新微信达人
)
from 日志 import 错误日志器, 接口日志器


async def 获取微信达人公海列表(
    页码: int = 1,
    每页数量: int = 20,  # 优化为每次加载20个达人
    最后ID: int = 0,
    筛选条件: Optional[Dict[str, Any]] = None,
    有联系方式: Optional[bool] = None,
    关键词: Optional[str] = None,
    当前用户id: Optional[int] = None,
    当前团队id: Optional[int] = None
) -> Dict[str, Any]:
    """
    获取微信达人公海列表的服务
    
    参数:
        页码: 当前页码，默认为1
        每页数量: 每页显示数量，默认为20
        最后ID: 上一页最后的达人id，默认为0
        筛选条件: 可选的筛选条件，如地区、性别等
        有联系方式: 可选，筛选是否有联系方式的达人
        关键词: 可选，通过微信号、昵称搜索达人
        当前用户id: 可选，当前用户id，用于检查达人是否被当前用户认领
        当前团队id: 可选，当前团队id，用于团队维度的认领状态判断
        
    返回:
        包含分页信息和微信达人列表的字典
    """
    try:
        接口日志器.info(f"获取微信达人公海列表 - 页码: {页码}, 每页数量: {每页数量}, 最后ID: {最后ID}")
        
        # 调用数据层获取达人列表
        结果 = await 异步获取微信达人列表(
            页码=页码,
            每页数量=每页数量,
            最后ID=最后ID,
            筛选条件=筛选条件,
            有联系方式=有联系方式,
            关键词=关键词,
            当前用户id=当前用户id,
            当前团队id=当前团队id
        )
        
        接口日志器.info(f"微信达人公海列表获取成功 - 返回 {len(结果.get('达人列表', []))} 条记录")
        return 结果
        
    except Exception as e:
        错误日志器.error(f"获取微信达人公海列表失败: {str(e)}")
        raise


async def 获取微信达人详情(达人id: int, 用户id: int) -> Dict[str, Any]:
    """
    获取微信达人详情信息
    
    参数:
        达人id: 达人的唯一标识ID
        用户id: 当前用户id
        
    返回:
        包含达人详情、联系方式和认领状态的字典
    """
    try:
        接口日志器.info(f"获取微信达人详情 - 达人id: {达人id}, 用户id: {用户id}")
        
        # 调用数据层获取达人详情
        达人详情 = await 异步获取微信达人详情(达人id, 用户id)
        
        if not 达人详情:
            raise ValueError(f"微信达人 {达人id} 不存在")
        
        接口日志器.info(f"微信达人详情获取成功 - 达人id: {达人id}")
        return 达人详情
        
    except Exception as e:
        错误日志器.error(f"获取微信达人详情失败: {str(e)}")
        raise


async def 认领微信达人(达人id: int, 用户id: int, 团队id: Optional[int] = None) -> Dict[str, Any]:
    """
    认领微信达人
    
    参数:
        达人id: 要认领的达人id
        用户id: 认领用户id
        团队id: 可选，团队id
        
    返回:
        认领操作结果
    """
    try:
        接口日志器.info(f"认领微信达人 - 达人id: {达人id}, 用户id: {用户id}, 团队id: {团队id}")
        
        # 调用数据层执行认领操作（使用统一的认领函数）
        from 数据.抖音达人数据 import 异步认领微信达人
        成功 = await 异步认领微信达人(用户id, 达人id)
        
        if 成功:
            接口日志器.info(f"微信达人认领成功 - 达人id: {达人id}, 用户id: {用户id}")
            return {
                "状态": "成功",
                "消息": "微信达人认领成功",
                "达人id": 达人id
            }
        else:
            raise Exception("认领操作失败")
        
    except ValueError as ve:
        # 业务逻辑错误，如达人不存在、已被认领等
        错误日志器.warning(f"微信达人认领失败 - 业务错误: {str(ve)}")
        return {
            "状态": "失败",
            "消息": str(ve),
            "达人id": 达人id
        }
    except Exception as e:
        错误日志器.error(f"微信达人认领失败: {str(e)}")
        raise


async def 取消认领微信达人(达人id: int, 用户id: int) -> Dict[str, Any]:
    """
    取消认领微信达人
    
    参数:
        达人id: 要取消认领的达人id
        用户id: 用户id
        
    返回:
        取消认领操作结果
    """
    try:
        接口日志器.info(f"取消认领微信达人 - 达人id: {达人id}, 用户id: {用户id}")
        
        # 调用数据层执行取消认领操作
        成功 = await 异步取消认领微信达人(达人id, 用户id)
        
        if 成功:
            接口日志器.info(f"微信达人取消认领成功 - 达人id: {达人id}, 用户id: {用户id}")
            return {
                "状态": "成功",
                "消息": "微信达人取消认领成功",
                "达人id": 达人id
            }
        else:
            raise Exception("取消认领操作失败")
        
    except ValueError as ve:
        # 业务逻辑错误，如未认领过等
        错误日志器.warning(f"微信达人取消认领失败 - 业务错误: {str(ve)}")
        return {
            "状态": "失败",
            "消息": str(ve),
            "达人id": 达人id
        }
    except Exception as e:
        错误日志器.error(f"微信达人取消认领失败: {str(e)}")
        raise


async def 搜索用户认领微信达人列表(
    用户id: int,
    页码: int = 1,
    每页数量: int = 20,
    排序字段: str = "认领时间",
    排序方式: str = "desc",
    筛选条件: Optional[Dict[str, Any]] = None,
    关键词: Optional[str] = None
) -> Dict[str, Any]:
    """
    获取用户认领的微信达人列表
    
    参数:
        用户id: 用户id
        页码: 当前页码
        每页数量: 每页显示数量
        排序字段: 排序字段
        排序方式: 排序方式
        筛选条件: 筛选条件
        关键词: 搜索关键词
        
    返回:
        包含分页信息和达人列表的字典
    """
    try:
        接口日志器.info(f"获取用户认领微信达人列表 - 用户id: {用户id}, 页码: {页码}")
        
        # 调用数据层获取用户认领的达人列表
        结果 = await 异步获取用户认领微信达人列表(
            用户id=用户id,
            页码=页码,
            每页数量=每页数量,
            排序字段=排序字段,
            排序方式=排序方式,
            筛选条件=筛选条件,
            关键词=关键词
        )
        
        接口日志器.info(f"用户认领微信达人列表获取成功 - 用户id: {用户id}, 返回 {len(结果.get('达人列表', []))} 条记录")
        return 结果
        
    except Exception as e:
        错误日志器.error(f"获取用户认领微信达人列表失败: {str(e)}")
        raise


async def 查询或更新微信达人(微信号: str, 达人数据: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    通过微信号查询或创建微信达人
    
    参数:
        微信号: 微信号
        达人数据: 可选，达人数据用于创建或更新
        
    返回:
        字典，包含操作结果信息和达人id
    """
    try:
        接口日志器.info(f"查询或更新微信达人 - 微信号: {微信号}")
        
        # 参数校验
        if not 微信号 or not 微信号.strip():
            raise ValueError("微信号不能为空")
        
        # 如果没有提供达人数据，使用默认数据
        if not 达人数据:
            达人数据 = {
                "昵称": None,
                "头像": None,
                "个人简介": None,
                "地区": None,
                "性别": None,
                "账号状态": "正常",
                "好友数": 0,
                "朋友圈发布数": 0
            }
        
        # 调用数据层创建或更新达人
        达人id = await 异步创建或更新微信达人(微信号.strip(), 达人数据)
        
        接口日志器.info(f"微信达人查询或更新成功 - 微信号: {微信号}, 达人id: {达人id}")
        
        return {
            "状态": "成功",
            "消息": "微信达人处理成功",
            "达人id": 达人id,
            "微信号": 微信号.strip()
        }
        
    except ValueError as ve:
        错误日志器.warning(f"微信达人查询或更新失败 - 参数错误: {str(ve)}")
        return {
            "状态": "失败",
            "消息": str(ve),
            "达人id": None
        }
    except Exception as e:
        错误日志器.error(f"微信达人查询或更新失败: {str(e)}")
        return {
            "状态": "失败",
            "消息": f"系统错误: {str(e)}",
            "达人id": None
        }
