"""
联系方式处理服务
负责联系方式相关的数据处理和业务逻辑
包含数据处理、业务服务、达人匹配、重复检测等功能
"""

import json
import re
from typing import Any, Dict, List, Optional

from 日志 import 错误日志器


class 联系方式处理器:
    """联系方式数据处理器"""

    def __init__(self):
        # 联系方式类型识别正则表达式
        self.正则表达式 = {
            "手机号": re.compile(r"^1[3-9]\d{9}$"),
            "微信号": re.compile(r"^[a-zA-Z][a-zA-Z0-9_-]{4,19}$"),
            "邮箱": re.compile(r"^[^\s@]+@[^\s@]+\.[^\s@]+$"),
        }

    def 验证和清理数据(self, 数据项: Dict[str, Any], 索引: int) -> Dict[str, Any]:
        """
        验证和清理单条导入数据

        参数:
            数据项: 原始数据项
            索引: 数据行索引

        返回:
            包含验证结果和清理后数据的字典
        """
        try:
            # 提取和清理基础字段
            平台账号 = str(数据项.get("平台账号", "")).strip()
            联系方式 = str(数据项.get("联系方式", "")).strip()
            个人备注 = str(数据项.get("个人备注", "")).strip()
            个人标签 = 数据项.get("个人标签", [])

            # 验证必填字段
            if not 联系方式:
                return {"有效": False, "错误信息": f"第{索引 + 1}行: 联系方式不能为空"}

            # 联系方式格式验证
            if len(联系方式) < 3:
                return {
                    "有效": False,
                    "错误信息": f"第{索引 + 1}行: 联系方式格式无效（长度过短）",
                }

            # 识别联系方式类型
            联系方式类型 = self.识别联系方式类型(联系方式)

            # 验证联系方式类型是否支持
            允许的类型 = ["微信", "手机", "邮箱"]
            if 联系方式类型 not in 允许的类型:
                return {
                    "有效": False,
                    "错误信息": f"第{索引 + 1}行: 不支持的联系方式类型 '{联系方式类型}'，只支持: {', '.join(允许的类型)}"
                }

            # 处理个人标签
            处理后标签 = self.处理个人标签(个人标签)

            # 数据清理和标准化
            清理后数据 = {
                "平台账号": 平台账号,
                "联系方式": 联系方式,
                "联系方式类型": 联系方式类型,
                "个人备注": 个人备注[:500] if 个人备注 else "",  # 限制备注长度
                "个人标签": 处理后标签,
            }

            return {"有效": True, "数据": 清理后数据}

        except Exception as e:
            错误日志器.error(f"数据验证清理失败: 索引={索引}, 错误={str(e)}")
            return {
                "有效": False,
                "错误信息": f"第{索引 + 1}行: 数据处理异常 - {str(e)}",
            }

    def 识别联系方式类型(self, 联系方式: str) -> str:
        """
        智能识别联系方式类型

        参数:
            联系方式: 联系方式字符串

        返回:
            联系方式类型（手机、邮箱、微信、未知）
        """
        # 预处理：去除空格
        清理后联系方式 = 联系方式.strip().replace(" ", "").replace("-", "")

        # 1. 手机号检测（中国大陆手机号）
        if self.正则表达式["手机号"].match(清理后联系方式):
            return "手机"

        # 2. 邮箱检测
        if self.正则表达式["邮箱"].match(联系方式):  # 邮箱保留原格式检测
            return "邮箱"

        # 3. 微信号检测
        if self.正则表达式["微信号"].match(清理后联系方式):
            return "微信"

        # 4. 宽松匹配规则
        if "@" in 联系方式 and "." in 联系方式:
            return "邮箱"  # 宽松的邮箱匹配
        elif (
            清理后联系方式.isdigit()
            and len(清理后联系方式) == 11
            and 清理后联系方式.startswith("1")
        ):
            return "手机"  # 可能的手机号

        return "未知"

    def 处理个人标签(self, 个人标签: Any) -> List[str]:
        """
        处理和标准化个人标签

        参数:
            个人标签: 原始标签数据（可能是字符串、列表等）

        返回:
            标准化的标签列表
        """
        try:
            if isinstance(个人标签, list):
                # 已经是列表，直接处理
                标签列表 = 个人标签
            elif isinstance(个人标签, str) and 个人标签.strip():
                # 字符串，需要分割
                标签列表 = re.split(r"[,，;；|｜\s]+", 个人标签.strip())
            else:
                # 其他类型或空值
                return []

            # 清理和过滤标签
            处理后标签 = []
            for 标签 in 标签列表:
                if isinstance(标签, str):
                    清理标签 = 标签.strip()
                    if 清理标签 and len(清理标签) <= 20:  # 限制标签长度
                        处理后标签.append(清理标签)

            # 去重并限制数量
            return list(dict.fromkeys(处理后标签))[:10]  # 最多10个标签

        except Exception as e:
            错误日志器.error(f"标签处理失败: 标签={个人标签}, 错误={str(e)}")
            return []

    def 批量验证数据质量(self, 导入数据: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        批量验证数据质量

        参数:
            导入数据: 导入数据列表

        返回:
            数据质量报告
        """
        总数量 = len(导入数据)
        有效数量 = 0
        无效数量 = 0
        质量问题 = []

        # 联系方式类型统计
        类型统计 = {}

        for 索引, 数据项 in enumerate(导入数据):
            验证结果 = self.验证和清理数据(数据项, 索引)

            if 验证结果["有效"]:
                有效数量 += 1
                联系方式类型 = 验证结果["数据"]["联系方式类型"]
                类型统计[联系方式类型] = 类型统计.get(联系方式类型, 0) + 1
            else:
                无效数量 += 1
                质量问题.append(验证结果["错误信息"])

        return {
            "总数量": 总数量,
            "有效数量": 有效数量,
            "无效数量": 无效数量,
            "有效率": round(有效数量 / 总数量 * 100, 2) if 总数量 > 0 else 0,
            "类型统计": 类型统计,
            "质量问题": 质量问题[:20],  # 只返回前20个问题
        }


class 达人匹配器:
    """达人匹配处理器"""

    def __init__(self):
        # 导入数据访问层
        from 数据.联系方式数据访问层 import 用户达人关联数据访问, 达人数据访问

        self.关联数据访问 = 用户达人关联数据访问
        self.达人数据访问 = 达人数据访问

    async def 查找或创建达人关联(
        self, 用户id: int, 平台类型: str, 平台账号: str
    ) -> Optional[int]:
        """
        查找或创建达人关联关系

        参数:
            用户id: 用户id
            平台类型: 平台类型（抖音/微信）
            平台账号: 平台账号
            异步连接池实例: 数据库连接池

        返回:
            关联表ID，失败返回None
        """
        try:
            if 平台类型 == "抖音":
                return await self._处理抖音达人关联(用户id, 平台账号)
            elif 平台类型 == "微信":
                return await self._处理微信达人关联(用户id, 平台账号)
            else:
                错误日志器.error(f"不支持的平台类型: {平台类型}")
                return None

        except Exception as e:
            错误日志器.error(
                f"达人关联处理失败: 用户id={用户id}, 平台={平台类型}, 账号={平台账号}, 错误={str(e)}"
            )
            return None

    async def _处理抖音达人关联(self, 用户id: int, 平台账号: str) -> Optional[int]:
        """处理抖音达人关联"""
        if not 平台账号:
            return None

        # 使用数据访问层查找抖音达人
        达人记录 = await self.达人数据访问.查询抖音达人(平台账号)

        if 达人记录:
            达人id = 达人记录["id"]
            return await self._创建或获取关联(用户id, 达人id, "抖音", 平台账号)
        else:
            # 创建虚拟关联
            return await self._创建虚拟关联(用户id, "抖音", 平台账号)

    async def _处理微信达人关联(self, 用户id: int, 平台账号: str) -> Optional[int]:
        """处理微信达人关联"""
        if not 平台账号:
            return None

        # 使用数据访问层查找微信达人
        达人记录 = await self.达人数据访问.查询微信达人(平台账号)

        if 达人记录:
            达人id = 达人记录["id"]
            return await self._创建或获取关联(用户id, 达人id, "微信", 平台账号)
        else:
            # 创建虚拟关联
            return await self._创建虚拟关联(用户id, "微信", 平台账号)

    async def _创建或获取关联(
        self, 用户id: int, 达人id: int, 平台: str, 平台账号: str
    ) -> int:
        """创建或获取用户达人关联"""
        # 使用数据访问层检查是否已存在关联
        关联记录 = await self.关联数据访问.查询用户达人关联(
            用户id=用户id, 达人id=达人id, 平台=平台, 平台账号=平台账号
        )

        if 关联记录:
            return 关联记录["id"]
        else:
            # 使用数据访问层创建新关联
            return await self.关联数据访问.创建用户达人关联(
                用户id, 达人id, 平台, 平台账号
            )

    async def _创建虚拟关联(self, 用户id: int, 平台: str, 平台账号: str) -> int:
        """创建虚拟达人关联（达人id为NULL）"""
        # 使用数据访问层检查是否已存在相同的虚拟关联
        虚拟关联记录 = await self.关联数据访问.查询用户达人关联(
            用户id=用户id, 达人id=None, 平台=平台, 平台账号=平台账号
        )

        if 虚拟关联记录:
            # 如果已存在相同的虚拟关联，直接返回现有ID
            return 虚拟关联记录["id"]
        else:
            # 使用数据访问层创建新的虚拟关联
            备注 = f"待匹配{平台}号: {平台账号}"
            return await self.关联数据访问.创建用户达人关联(
                用户id, None, 平台, 平台账号, 备注
            )


class 重复数据检测器:
    """重复数据检测和处理器"""

    def __init__(self):
        # 导入数据访问层
        from 数据.联系方式数据访问层 import 补充信息数据访问

        self.补充信息数据访问 = 补充信息数据访问

    async def 检测重复联系方式(self, 用户id: int, 联系方式: str) -> Dict[str, Any]:
        """
        检测用户是否已有相同联系方式

        参数:
            用户id: 用户id
            联系方式: 联系方式
            异步连接池实例: 数据库连接池

        返回:
            检测结果字典
        """
        try:
            # 使用数据访问层检测重复
            重复记录 = await self.补充信息数据访问.查询重复联系方式(用户id, 联系方式)

            if 重复记录:
                return {
                    "存在重复": True,
                    "重复信息": 重复记录,
                    "建议": "该联系方式已存在，建议更新现有记录而非新增",
                }
            else:
                return {"存在重复": False, "建议": "可以安全添加"}

        except Exception as e:
            错误日志器.error(
                f"重复检测失败: 用户id={用户id}, 联系方式={联系方式}, 错误={str(e)}"
            )
            return {"存在重复": False, "建议": "检测失败，建议手动确认"}

    def 去重导入数据(self, 导入数据: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        对导入数据进行去重处理

        参数:
            导入数据: 原始导入数据列表

        返回:
            去重结果和统计信息
        """
        原始数量 = len(导入数据)
        去重后数据 = []
        重复记录 = []

        # 使用联系方式作为去重键
        已见联系方式 = set()

        for 索引, 数据项 in enumerate(导入数据):
            联系方式 = 数据项.get("联系方式", "").strip()

            if 联系方式 and 联系方式 not in 已见联系方式:
                已见联系方式.add(联系方式)
                去重后数据.append(数据项)
            else:
                重复记录.append(
                    {
                        "索引": 索引,
                        "联系方式": 联系方式,
                        "原因": "联系方式重复" if 联系方式 else "联系方式为空",
                    }
                )

        return {
            "原始数量": 原始数量,
            "去重后数量": len(去重后数据),
            "重复数量": len(重复记录),
            "去重后数据": 去重后数据,
            "重复记录": 重复记录[:10],  # 只返回前10条重复记录
        }


class 联系方式导入服务:
    """联系方式导入业务服务"""

    def __init__(self):
        self.处理器 = 联系方式处理器()
        self.匹配器 = 达人匹配器()
        self.检测器 = 重复数据检测器()

    async def 导入联系方式数据(
        self, 用户id: int, 平台类型: str, 导入数据: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        导入联系方式数据的主要业务逻辑

        参数:
            用户id: 用户id
            平台类型: 平台类型（抖音/微信）
            导入数据: 导入数据列表

        返回:
            导入结果统计
        """
        try:
            # 参数验证
            if 平台类型 not in ["抖音", "微信"]:
                return self._构建失败结果("平台类型必须是'抖音'或'微信'")

            if not 导入数据 or len(导入数据) == 0:
                return self._构建失败结果("导入数据不能为空")

            # 初始化统计信息
            统计信息 = {"成功数量": 0, "失败数量": 0, "失败详情": [], "详细结果": []}

            # 数据去重
            去重结果 = self.检测器.去重导入数据(导入数据)
            处理数据 = 去重结果["去重后数据"]

            if 去重结果["重复数量"] > 0:
                统计信息["失败详情"].append(
                    f"发现 {去重结果['重复数量']} 条重复数据已自动去除"
                )

            # 批量处理数据
            for 索引, 数据项 in enumerate(处理数据):
                处理结果 = await self._处理单条数据(用户id, 平台类型, 数据项, 索引)

                if 处理结果["成功"]:
                    统计信息["成功数量"] += 1
                else:
                    统计信息["失败数量"] += 1
                    统计信息["失败详情"].append(处理结果["错误信息"])

                统计信息["详细结果"].append(处理结果["详细信息"])

            return self._构建导入结果(统计信息, len(导入数据))

        except Exception as e:
            错误日志器.error(
                f"导入联系方式数据失败: 用户id={用户id}, 平台={平台类型}, 错误={str(e)}"
            )
            return self._构建失败结果(f"导入处理异常: {str(e)}")

    async def _处理单条数据(
        self, 用户id: int, 平台类型: str, 数据项: Dict[str, Any], 索引: int
    ) -> Dict[str, Any]:
        """
        处理单条导入数据

        参数:
            用户id: 用户id
            平台类型: 平台类型
            数据项: 单条数据
            索引: 数据索引
            异步连接池实例: 数据库连接池

        返回:
            处理结果
        """
        try:
            # 数据验证和清理
            验证结果 = self.处理器.验证和清理数据(数据项, 索引)
            if not 验证结果["有效"]:
                return {
                    "成功": False,
                    "错误信息": 验证结果["错误信息"],
                    "详细信息": self._构建详细信息(
                        数据项, 索引, "error", 验证结果["错误信息"]
                    ),
                }

            清理后数据 = 验证结果["数据"]
            平台账号 = 清理后数据["平台账号"]
            联系方式 = 清理后数据["联系方式"]
            联系方式类型 = 清理后数据["联系方式类型"]
            个人备注 = 清理后数据["个人备注"]
            个人标签 = 清理后数据["个人标签"]

            # 检测重复联系方式
            重复检测结果 = await self.检测器.检测重复联系方式(用户id, 联系方式)
            if 重复检测结果["存在重复"]:
                return {
                    "成功": False,
                    "错误信息": f"第{索引 + 1}行: 联系方式 {联系方式} 已存在",
                    "详细信息": self._构建详细信息(
                        清理后数据, 索引, "error", f"联系方式 {联系方式} 已存在"
                    ),
                }

            # 查找或创建达人关联
            关联表ID = await self.匹配器.查找或创建达人关联(
                用户id=用户id, 平台类型=平台类型, 平台账号=平台账号
            )

            if not 关联表ID:
                return {
                    "成功": False,
                    "错误信息": f"第{索引 + 1}行: 创建达人关联失败",
                    "详细信息": self._构建详细信息(
                        清理后数据, 索引, "error", "创建达人关联失败"
                    ),
                }

            # 先将联系方式写入联系方式表，获取联系方式表ID
            from 数据.线索数据操作 import 获取或创建联系方式并返回id

            联系方式表ID = await 获取或创建联系方式并返回id(
                联系方式=联系方式,
                联系方式类型=联系方式类型,
                来源="批量导入"
            )

            if not 联系方式表ID:
                return {
                    "成功": False,
                    "错误信息": f"第{索引 + 1}行: 创建联系方式记录失败",
                    "详细信息": self._构建详细信息(
                        清理后数据, 索引, "error", "创建联系方式记录失败"
                    ),
                }

            # 保存补充信息
            await self._保存补充信息(
                关联表ID, 联系方式, 联系方式类型, 联系方式表ID, 个人备注, 个人标签, 数据项
            )

            return {
                "成功": True,
                "详细信息": self._构建详细信息(清理后数据, 索引, "success", "导入成功"),
            }

        except Exception as e:
            错误信息 = f"第{索引 + 1}行: {str(e)}"
            错误日志器.error(f"处理单条数据失败: {错误信息}")
            return {
                "成功": False,
                "错误信息": 错误信息,
                "详细信息": self._构建详细信息(数据项, 索引, "error", str(e)),
            }

    async def _保存补充信息(
        self,
        关联表ID: int,
        联系方式: str,
        联系方式类型: str,
        联系方式表ID: int,
        个人备注: str,
        个人标签: List,
        数据项: Dict[str, Any],
    ) -> None:
        """
        保存补充信息到数据库

        参数:
            关联表ID: 关联表ID
            联系方式: 联系方式
            联系方式类型: 联系方式类型
            联系方式表ID: 联系方式表ID
            个人备注: 个人备注
            个人标签: 个人标签列表
            数据项: 原始数据项
            异步连接池实例: 数据库连接池
        """
        try:
            # 导入数据访问层
            from 数据.联系方式数据访问层 import 补充信息数据访问

            # 处理个人标签JSON
            个人标签JSON = (
                json.dumps(个人标签, ensure_ascii=False) if 个人标签 else None
            )

            # 处理补充信息JSON
            补充信息JSON = None
            if "补充信息JSON" in 数据项 and 数据项["补充信息JSON"]:
                try:
                    补充信息JSON = 数据项["补充信息JSON"]
                    if isinstance(补充信息JSON, str):
                        json.loads(补充信息JSON)  # 验证JSON格式
                except (json.JSONDecodeError, TypeError):
                    补充信息JSON = None

            # 保存到数据库
            await 补充信息数据访问.创建或更新补充信息(
                关联表ID=关联表ID,
                联系方式=联系方式,
                联系方式类型=联系方式类型,
                个人备注=个人备注,
                个人标签=个人标签JSON,
                补充信息=补充信息JSON,
            )

        except Exception as e:
            错误日志器.error(
                f"保存补充信息失败: 关联表ID={关联表ID}, 联系方式={联系方式}, 错误={str(e)}"
            )
            raise

    def _构建详细信息(
        self, 数据项: Dict[str, Any], 索引: int, 状态: str, 消息: str
    ) -> Dict[str, Any]:
        """构建详细信息字典"""
        return {
            "index": 索引 + 1,
            "联系方式": 数据项.get("联系方式", ""),
            "联系方式类型": 数据项.get("联系方式类型", ""),
            "平台账号": 数据项.get("平台账号", ""),
            "status": 状态,
            "message": 消息,
        }

    def _构建失败结果(self, 错误消息: str) -> Dict[str, Any]:
        """构建失败结果"""
        return {"状态": "失败", "消息": 错误消息, "数据": None}

    def _构建导入结果(self, 统计信息: Dict[str, Any], 总数量: int) -> Dict[str, Any]:
        """构建导入结果"""
        成功数量 = 统计信息["成功数量"]
        失败数量 = 统计信息["失败数量"]

        结果数据 = {
            "总计": 总数量,
            "成功": 成功数量,
            "失败": 失败数量,
            "成功率": f"{round(成功数量 / 总数量 * 100, 1)}%" if 总数量 > 0 else "0%",
            "失败详情": 统计信息["失败详情"][:5] if 失败数量 > 0 else [],
            "详细结果": 统计信息["详细结果"],
        }

        # 生成响应消息
        if 失败数量 == 0:
            响应消息 = f"导入完成！成功处理 {成功数量} 条联系方式"
        elif 成功数量 == 0:
            响应消息 = f"导入失败，{失败数量} 条数据处理失败"
        else:
            响应消息 = f"部分成功！成功 {成功数量} 条，失败 {失败数量} 条"

        return {"状态": "成功", "消息": 响应消息, "数据": 结果数据}


class 联系方式更新服务:
    """联系方式更新业务服务"""

    @staticmethod
    async def 更新联系方式(
        用户id: int, 达人id: int, 更新数据: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        更新联系方式信息

        参数:
            用户id: 用户id
            达人id: 达人id
            更新数据: 更新数据字典

        返回:
            更新结果
        """
        try:
            # 导入数据访问层
            from 数据.联系方式数据访问层 import 用户达人关联数据访问, 补充信息数据访问

            # 查询用户达人关联
            关联记录 = await 用户达人关联数据访问.查询用户达人关联(
                用户id=用户id, 达人id=达人id
            )

            if not 关联记录:
                return {"状态": "失败", "消息": "您没有认领该达人，无法编辑联系方式"}

            关联表ID = 关联记录["id"]

            # 准备更新字段
            更新字段 = {}

            if 更新数据.get("联系方式") is not None:
                更新字段["联系方式"] = 更新数据["联系方式"]

            if 更新数据.get("联系方式类型") is not None:
                更新字段["联系方式类型"] = 更新数据["联系方式类型"]

            if 更新数据.get("个人备注") is not None:
                更新字段["个人备注"] = 更新数据["个人备注"]

            if 更新数据.get("个人标签") is not None:
                更新字段["个人标签"] = json.dumps(
                    更新数据["个人标签"], ensure_ascii=False
                )

            if 更新数据.get("补充信息") is not None:
                更新字段["补充信息"] = json.dumps(
                    更新数据["补充信息"], ensure_ascii=False
                )

            if not 更新字段:
                return {"状态": "失败", "消息": "没有提供要更新的字段"}

            # 执行更新
            await 补充信息数据访问.更新联系方式信息(关联表ID, **更新字段)

            return {
                "状态": "成功",
                "消息": "联系方式更新成功",
                "数据": {"关联表ID": 关联表ID},
            }

        except Exception as e:
            错误日志器.error(
                f"更新联系方式失败: 用户id={用户id}, 达人id={达人id}, 错误={str(e)}"
            )
            return {"状态": "失败", "消息": f"更新联系方式失败: {str(e)}"}
