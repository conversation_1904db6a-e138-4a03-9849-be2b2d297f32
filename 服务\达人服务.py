import json
from typing import Any, Dict, List, Optional

import httpx

# 导入抖音达人数据操作函数
from 数据.抖音达人数据 import (
    异步取消认领达人,
    异步搜索用户认领达人列表,
    异步更新达人信息,
    异步查询或更新达人,
    异步获取用户认领达人列表,
    异步获取达人列表,
    异步认领达人,
)

# 导入统一日志系统
from 日志 import 应用日志器, 接口日志器, 错误日志器


async def 获取达人公海列表(
    页码: int = 1,
    每页数量: int = 20,
    最后ID: int = 0,
    筛选条件: Optional[Dict[str, Any]] = None,
    有联系方式: Optional[bool] = None,
    关键词: Optional[str] = None,
    当前用户id: Optional[int] = None,
    当前团队id: Optional[int] = None,
    uid: Optional[str] = None,  # 新增：支持通过单个uid进行精准查询
) -> Dict[str, Any]:
    """
    获取达人公海列表的服务

    集成第三方API搜索逻辑：
    - 当有关键词时，先调用第三方API搜索
    - 检查搜索结果在数据库中的存在性
    - 如果不存在，自动保存到数据库
    - 最终返回包含新达人的列表

    参数:
        页码: 当前页码，默认为1
        每页数量: 每页显示数量，默认为20
        最后ID: 上一页最后的达人id，默认为0
        筛选条件: 可选的筛选条件，如粉丝数范围、直播销售额等
        有联系方式: 可选，筛选是否有联系方式的达人
        关键词: 可选，通过抖音号搜索达人（集成第三方API）
        当前用户id: 可选，当前用户id，用于检查达人是否被当前用户认领
        当前团队id: 可选，当前团队id，用于团队维度的认领状态判断

    返回:
        包含分页信息和达人列表的字典
    """
    try:
        # 参数校验
        if 页码 < 1:
            页码 = 1

        if 每页数量 < 1 or 每页数量 > 100:
            每页数量 = 20

        if 最后ID < 0:
            最后ID = 0

        # 注释：第三方API搜索逻辑已移至专门的搜索接口
        # 达人公海列表只进行本地数据库查询，保持性能

        # 执行数据层查询 - 传递团队id参数
        结果 = await 异步获取达人列表(
            页码, 每页数量, 最后ID, 筛选条件, 有联系方式, 关键词, 当前用户id, 当前团队id, uid
        )

        # 日志记录
        达人数量 = len(结果.get("达人列表", []))
        if 当前团队id:
            接口日志器.info(
                f"获取达人公海列表成功（团队模式），团队id: {当前团队id}, 当前页: {结果['当前页']}, 达人数量: {达人数量}"
            )
        else:
            接口日志器.info(
                f"获取达人公海列表成功（兼容模式），当前页: {结果['当前页']}, 达人数量: {达人数量}"
            )

        return 结果
    except Exception as e:
        错误日志器.error(f"获取达人公海列表服务异常: {str(e)}")
        raise Exception(f"获取达人公海列表失败: {str(e)}")


async def 获取达人详情(达人id: int, 当前用户id: int) -> Dict[str, Any]:
    """
    获取单个达人详情的服务，包含联系方式信息

    参数:
        达人id: 达人的唯一标识ID
        当前用户id: 当前用户id，必需参数

    返回:
        包含达人详情和联系方式的字典
    """
    try:
        # 参数校验
        if not 达人id or 达人id <= 0:
            raise ValueError("达人id不能为空且必须大于0")
        if not 当前用户id or 当前用户id <= 0:
            raise ValueError("用户id不能为空且必须大于0")

        # 执行数据层查询
        from 数据.抖音达人数据 import 获取达人详情 as 数据层获取达人详情
        达人详情 = await 数据层获取达人详情(达人id)

        if not 达人详情:
            raise ValueError(f"未找到ID为{达人id}的达人信息")

        # 获取用户相关的联系方式
        from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

        # 查询用户与该达人的所有关联记录
        关联查询SQL = """
        SELECT
            ur.id as 关联id,
            ur.平台,
            ur.平台账号,
            ur.认领时间,
            si.id as 补充信息id,
            si.联系方式,
            si.联系方式类型,
            si.个人备注,
            si.个人标签,
            si.补充信息,
            si.创建时间 as 联系方式创建时间,
            si.更新时间 as 联系方式更新时间
        FROM 用户达人关联表 ur
        LEFT JOIN 用户达人补充信息表 si ON ur.id = si.用户达人关联表id
        WHERE ur.用户id = $1 AND ur.达人id = $2 AND ur.状态 = 1
        ORDER BY ur.认领时间 DESC, si.创建时间 DESC
        """

        关联记录 = await 异步连接池实例.执行查询(关联查询SQL, (当前用户id, 达人id))

        # 处理关联的联系方式数据
        联系方式列表 = []
        关联记录字典 = {}

        # 先按关联id分组，处理一个关联可能有多条补充信息的情况
        for 记录 in 关联记录:
            关联id = 记录["关联id"]

            if 关联id not in 关联记录字典:
                # 创建基础关联信息
                关联记录字典[关联id] = {
                    "关联id": 关联id,
                    "平台": 记录["平台"],
                    "平台账号": 记录["平台账号"],
                    "认领时间": 记录["认领时间"],
                    "补充联系方式列表": [],
                }

            # 如果有联系方式信息，添加到补充联系方式列表
            if 记录.get("联系方式"):
                补充信息 = {
                    "补充信息id": 记录["补充信息id"],
                    "联系方式": 记录["联系方式"],
                    "联系方式类型": 记录["联系方式类型"],
                    "个人备注": 记录["个人备注"],
                    "个人标签": json.loads(记录["个人标签"])
                    if 记录["个人标签"]
                    else [],
                    "补充信息": json.loads(记录["补充信息"])
                    if 记录["补充信息"]
                    else {},
                    "创建时间": 记录["联系方式创建时间"],
                    "更新时间": 记录["联系方式更新时间"],
                }
                关联记录字典[关联id]["补充联系方式列表"].append(补充信息)

        # 转换为列表格式
        for 关联信息 in 关联记录字典.values():
            联系方式列表.append(关联信息)

        # 将联系方式列表添加到达人详情中
        达人详情["我的联系方式列表"] = 联系方式列表
        达人详情["联系方式总数"] = len(联系方式列表)

        # 日志记录
        接口日志器.debug(
            f"获取达人详情成功，达人id: {达人id}, 联系方式数量: {len(联系方式列表)}"
        )

        return 达人详情
    except ValueError as e:
        # 业务逻辑错误
        错误日志器.warning(f"获取达人详情业务错误: {str(e)}")
        raise
    except Exception as e:
        # 系统错误
        错误日志器.error(f"获取达人详情服务异常: {str(e)}")
        raise Exception(f"获取达人详情失败: {str(e)}")


async def 认领达人(用户id: int, 达人id: int) -> bool:
    """
    认领达人服务

    参数:
        用户id: 认领用户的ID
        达人id: 被认领达人的ID

    返回:
        操作是否成功的布尔值
    """
    try:
        # 参数校验
        if not 用户id or 用户id <= 0:
            raise ValueError("用户id不能为空且必须大于0")

        if not 达人id or 达人id <= 0:
            raise ValueError("达人id不能为空且必须大于0")

        # 验证达人是否存在
        from 数据.抖音达人数据 import 获取达人详情 as 数据层获取达人详情
        达人详情 = await 数据层获取达人详情(达人id)
        if not 达人详情:
            raise ValueError(f"未找到ID为{达人id}的达人信息")

        # 执行认领操作
        结果 = await 异步认领达人(用户id, 达人id)

        # 日志记录
        接口日志器.info(f"用户 {用户id} 认领达人 {达人id} 成功")

        return 结果
    except ValueError as e:
        # 业务逻辑错误
        错误日志器.warning(f"认领达人业务错误: {str(e)}")
        raise
    except Exception as e:
        # 系统错误
        错误日志器.error(f"认领达人服务异常: {str(e)}")
        raise Exception(f"认领达人失败: {str(e)}")


async def 取消认领达人(用户id: int, 达人id: int) -> bool:
    """
    取消认领达人服务

    参数:
        用户id: 用户的ID
        达人id: 被取消认领的达人id

    返回:
        操作是否成功的布尔值
    """
    try:
        # 参数校验
        if not 用户id or 用户id <= 0:
            raise ValueError("用户id不能为空且必须大于0")

        if not 达人id or 达人id <= 0:
            raise ValueError("达人id不能为空且必须大于0")

        # 验证达人是否存在
        from 数据.抖音达人数据 import 获取达人详情 as 数据层获取达人详情
        达人详情 = await 数据层获取达人详情(达人id)
        if not 达人详情:
            raise ValueError(f"未找到ID为{达人id}的达人信息")

        # 执行取消认领操作
        结果 = await 异步取消认领达人(用户id, 达人id)

        # 日志记录
        接口日志器.info(f"用户 {用户id} 取消认领达人 {达人id} 成功")

        return 结果
    except ValueError as e:
        # 业务逻辑错误
        错误日志器.warning(f"取消认领达人业务错误: {str(e)}")
        raise
    except Exception as e:
        # 系统错误
        错误日志器.error(f"取消认领达人服务异常: {str(e)}")
        raise Exception(f"取消认领达人失败: {str(e)}")


async def 获取用户认领达人列表(用户id: int) -> List[Dict[str, Any]]:
    """
    获取用户认领的所有达人列表

    参数:
        用户id: 用户的ID

    返回:
        包含达人信息的列表
    """
    try:
        # 参数校验
        if not 用户id or 用户id <= 0:
            raise ValueError("用户id不能为空且必须大于0")

        # 执行数据层查询
        达人列表 = await 异步获取用户认领达人列表(用户id)

        # 日志记录
        接口日志器.info(
            f"获取用户 {用户id} 认领达人列表成功，共 {len(达人列表)} 个达人"
        )

        return 达人列表
    except ValueError as e:
        # 业务逻辑错误
        错误日志器.warning(f"获取用户认领达人列表业务错误: {str(e)}")
        raise
    except Exception as e:
        # 系统错误
        错误日志器.error(f"获取用户认领达人列表服务异常: {str(e)}")
        raise Exception(f"获取用户认领达人列表失败: {str(e)}")


async def 异步从抖音获取达人信息(uid_number: str) -> Optional[Dict[str, Any]]:
    """
    根据uid_number异步从抖音API获取达人信息.
    如果成功. 返回API响应的JSON数据. 否则返回None.
    """
    # 注意：此处的 aid=6383 可能需要根据实际情况调整或保密
    api_url = f"https://live.douyin.com/webcast/user/?aid=6383&live_id=1&device_platform=web&language=zh-CN&target_uid={uid_number}"
    接口日志器.info(f"服务层：开始请求抖音API获取达人 {uid_number} 的信息: {api_url}")
    async with httpx.AsyncClient(timeout=10.0) as 客户端:
        try:
            响应 = await 客户端.get(api_url)
            响应.raise_for_status()
            数据 = 响应.json()
            接口日志器.info(f"服务层：成功从抖音API获取到达人 {uid_number} 的信息.")
            return 数据
        except httpx.HTTPStatusError as exc:
            错误日志器.error(
                f"服务层：请求抖音API返回HTTP错误 (HTTP {exc.response.status_code}) for {uid_number}: {exc.request.url}"
            )
            try:
                错误数据 = exc.response.json()
                # 接口日志器.debug(f"服务层：抖音API错误响应内容 (JSON) for {uid_number}: {错误数据}") # 可能包含敏感信息
                return 错误数据
            except json.JSONDecodeError:
                错误日志器.error(
                    f"服务层：抖音API错误响应内容 (非JSON) for {uid_number}: {exc.response.text}"
                )
                return {
                    "status_code": exc.response.status_code,
                    "error_message": "非JSON响应",
                    "raw_content": exc.response.text,
                }
        except httpx.RequestError as exc:
            错误日志器.error(
                f"服务层：请求抖音API时发生网络错误 for {uid_number}: {exc}"
            )
            return None
        except json.JSONDecodeError:
            错误日志器.error(
                f"服务层：解析抖音API响应JSON失败 for {uid_number}. URL: {api_url}, 响应内容: {响应.text if '响应' in locals() else '未知'}"
            )
            return None


async def 查询或更新达人(uid_number: str) -> Dict[str, Any]:
    """
    通过uid_number查询或创建达人。
    如果创建了新达人，则尝试从抖音获取并更新其信息。
    此函数现在只依赖 uid_number进行初始查询/创建。

    参数:
        uid_number: 达人唯一标识符

    返回:
        字典，包含操作结果信息和达人id(如果找到或创建)
    """
    try:
        # 参数校验
        if not uid_number or not uid_number.strip():
            raise ValueError("uid_number不能为空")

        # 调用数据层进行查询或基础创建
        结果 = await 异步查询或更新达人(uid_number)

        # 检查是否是新创建的达人
        is_newly_created = False
        if 结果["状态"] == "成功" and 结果.get("达人id") is not None:
            message = 结果.get("消息", "")
            if "创建了新的达人记录" in message or "创建新达人记录" in message:
                is_newly_created = True

        # 对所有成功的达人（新创建或已存在）都尝试从抖音获取最新信息
        if 结果["状态"] == "成功" and 结果.get("达人id") is not None:
            达人id = 结果["达人id"]
            
            接口日志器.info(
                f"服务层：{'新' if is_newly_created else '已存在'}达人记录 (ID: {达人id}, UID: {uid_number}). 尝试从抖音获取最新信息。"
            )

            try:
                抖音数据 = await 异步从抖音获取达人信息(uid_number)

                if (
                    抖音数据
                    and 抖音数据.get("status_code", 0) == 0
                    and 抖音数据.get("data")
                    and isinstance(抖音数据["data"], dict)
                ):
                    抖音用户信息 = 抖音数据["data"]
                    if 抖音用户信息:
                        更新数据 = {}
                        # --- 从抖音数据映射到数据库字段 ---
                        接口日志器.debug(f"抖音API返回的数据字段: {list(抖音用户信息.keys())}")
                        
                        # 辅助函数：检查字段是否需要更新
                        def 需要更新字段(字段名, 新值):
                            # 确保新值是有效的（不为空、不为0、不为None）
                            if 新值 is None:
                                return False
                            if isinstance(新值, str) and 新值.strip() == "":
                                return False
                            if isinstance(新值, (int, float)) and 新值 == 0:
                                return False
                            # 如果新值有效，则可以更新
                            return True
                        
                        # 更新昵称
                        if 需要更新字段("昵称", 抖音用户信息.get("昵称")):
                            更新数据["昵称"] = 抖音用户信息["昵称"]
                            接口日志器.debug(f"设置昵称: {抖音用户信息['昵称']}")
                        # 更新头像
                        avatar_thumb = 抖音用户信息.get("avatar_thumb")
                        if avatar_thumb and isinstance(avatar_thumb, dict):
                            url_list = avatar_thumb.get("url_list")
                            if url_list and isinstance(url_list, list) and len(url_list) > 0:
                                if 需要更新字段("avatar", url_list[0]):
                                    更新数据["avatar"] = url_list[0]
                                    接口日志器.debug(f"设置avatar: {url_list[0]}")
                        
                        # 更新简介
                        if 需要更新字段("introduction", 抖音用户信息.get("signature")):
                            更新数据["introduction"] = 抖音用户信息["signature"]
                            接口日志器.debug(f"设置introduction: {抖音用户信息['signature']}")

                        # 更新粉丝数和关注数
                        follow_info = 抖音用户信息.get("follow_info")
                        if follow_info and isinstance(follow_info, dict):
                            # 更新粉丝数
                            if follow_info.get("follower_count") is not None:
                                try:
                                    粉丝数 = int(follow_info["follower_count"])
                                    if 需要更新字段("粉丝数", 粉丝数):
                                        更新数据["粉丝数"] = 粉丝数
                                        接口日志器.debug(f"设置粉丝数: {粉丝数}")
                                except ValueError:
                                    错误日志器.warning(
                                        f"服务层：无法将粉丝数 {follow_info.get('follower_count')} 转换为整数 for {uid_number}"
                                    )
                            # 更新关注数
                            if follow_info.get("following_count") is not None:
                                try:
                                    关注数 = int(follow_info["following_count"])
                                    if 需要更新字段("关注数", 关注数):
                                        更新数据["关注数"] = 关注数
                                        接口日志器.debug(f"设置关注数: {关注数}")
                                except ValueError:
                                    错误日志器.warning(
                                        f"服务层：无法将关注数 {follow_info.get('following_count')} 转换为整数 for {uid_number}"
                                    )

                        # 更新sec_uid字段
                        if 需要更新字段("sec_uid", 抖音用户信息.get("sec_uid")):
                            更新数据["sec_uid"] = 抖音用户信息["sec_uid"]
                            接口日志器.debug(f"设置sec_uid: {抖音用户信息['sec_uid']}")

                        # 更新城市字段
                        if 需要更新字段("city", 抖音用户信息.get("city")):
                            更新数据["city"] = 抖音用户信息["city"]
                            接口日志器.debug(f"设置city: {抖音用户信息['city']}")

                        # 更新抖音号 (优先级：display_id > unique_id > short_id)
                        display_id = str(抖音用户信息.get("display_id", "")).strip()
                        unique_id = str(抖音用户信息.get("unique_id", "")).strip()
                        short_id = str(抖音用户信息.get("short_id", "")).strip()
                        
                        if 需要更新字段("account_douyin", display_id):
                            更新数据["account_douyin"] = display_id
                            接口日志器.debug(f"设置account_douyin (display_id): {display_id}")
                        elif 需要更新字段("account_douyin", unique_id):
                            更新数据["account_douyin"] = unique_id
                            接口日志器.debug(f"设置account_douyin (unique_id): {unique_id}")
                        elif 需要更新字段("account_douyin", short_id):
                            更新数据["account_douyin"] = short_id
                            接口日志器.debug(f"设置account_douyin (short_id): {short_id}")


                        if 更新数据:
                            接口日志器.info(
                                f"服务层：成功获取到抖音信息 (ID: {达人id}, UID: {uid_number}). 准备更新数据库: {list(更新数据.keys())}"
                            )
                            更新操作结果 = await 更新达人信息(达人id, 更新数据)
                            if 更新操作结果.get("状态") == "成功":
                                if is_newly_created:
                                    结果["消息"] += "；并已从抖音同步最新信息。"
                                else:
                                    结果["消息"] = "达人已存在，已从抖音同步最新信息。"
                                接口日志器.info(
                                    f"服务层：成功更新达人 (ID: {达人id}) 信息来自抖音。"
                                )
                            else:
                                if is_newly_created:
                                    结果["消息"] += "；但从抖音同步信息至数据库时失败。"
                                else:
                                    结果["消息"] = "达人已存在，但从抖音同步信息至数据库时失败。"
                                错误日志器.error(
                                    f"服务层：更新达人 (ID: {达人id}) 信息来自抖音时失败: {更新操作结果.get('消息')}"
                                )
                        else:
                            接口日志器.info(
                                f"服务层：从抖音获取的信息中未提取到可更新字段 (ID: {达人id}, UID: {uid_number})。"
                            )
                            结果["消息"] += (
                                "；尝试从抖音获取信息但未提取到有效数据进行更新。"
                            )
                    else:
                        接口日志器.warning(
                            f"服务层：抖音用户信息字典为空或无效 (ID: {达人id}, UID: {uid_number})。抖音数据['data']: {抖音用户信息}"
                        )
                        结果["消息"] += "；尝试从抖音获取信息但处理后数据无效。"
                elif 抖音数据 and 抖音数据.get("status_code", 0) != 0:
                    错误信息 = 抖音数据.get(
                        "message",
                        抖音数据.get("error_message", "抖音API返回错误但无具体消息"),
                    )
                    接口日志器.error(
                        f"服务层：抖音API返回错误 (ID: {达人id}, UID: {uid_number}). Status Code: {抖音数据.get('status_code')}, Message: {错误信息}"
                    )
                    结果["消息"] += (
                        f"；尝试从抖音获取信息失败（API错误码: {抖音数据.get('status_code')}）。"
                    )
                else:
                    接口日志器.warning(
                        f"服务层：未能从抖音获取到达人信息或信息为空/结构不符 (ID: {达人id}, UID: {uid_number})。返回数据: {str(抖音数据)[:500]}"
                    )
                    结果["消息"] += "；尝试从抖音获取信息但未成功或数据无效。"
            except Exception as e_douyin:
                错误日志器.error(
                    f"服务层：调用抖音API或处理其响应时发生异常 (ID: {达人id}, UID: {uid_number}): {str(e_douyin)}"
                )
                结果["消息"] += "；尝试从抖音获取信息时发生内部错误。"

        # 日志记录（无论是否新创建和同步）
        if 结果["状态"] == "成功":
            接口日志器.info(
                f"服务层：最终查询或更新达人结果: {结果['消息']}, 达人id: {结果['达人id']}"
            )
        else:
            接口日志器.warning(f"服务层：最终查询或更新达人未成功: {结果['消息']}")

        return 结果
    except ValueError as e:
        错误日志器.warning(f"服务层：查询或更新达人业务错误 (ValueError): {str(e)}")
        return {"状态": "失败", "消息": str(e), "达人id": None}
    except Exception as e:
        错误日志器.error(f"服务层：查询或更新达人服务发生未捕获异常: {str(e)}")
        return {"状态": "失败", "消息": f"服务异常: {str(e)}", "达人id": None}


async def 更新达人信息(达人id: int, 更新数据: Dict[str, Any]) -> Dict[str, Any]:
    """
    更新达人信息的服务

    参数:
        达人id: 达人的唯一标识ID
        更新数据: 要更新的字段和值的字典

    返回:
        包含操作结果的字典
    """
    try:
        # 参数校验
        if not 达人id or 达人id <= 0:
            raise ValueError("达人id不能为空且必须大于0")

        if not isinstance(更新数据, dict):
            raise ValueError("更新数据必须是字典类型")

        # 执行数据层操作
        结果 = await 异步更新达人信息(达人id, 更新数据)

        # 日志记录
        if 结果["状态"] == "成功":
            接口日志器.info(f"更新达人信息成功，达人id: {达人id}")
        else:
            接口日志器.warning(f"更新达人信息失败: {结果['消息']}")

        return 结果
    except ValueError as e:
        # 业务逻辑错误
        错误日志器.warning(f"更新达人信息业务错误: {str(e)}")
        return {"状态": "失败", "消息": str(e), "达人id": 达人id}
    except Exception as e:
        # 系统错误
        错误日志器.error(f"更新达人信息服务异常: {str(e)}")
        return {"状态": "失败", "消息": f"服务异常: {str(e)}", "达人id": 达人id}


async def 搜索用户认领达人列表(
    用户id: int,
    页码: int = 1,
    每页数量: int = 20,
    排序字段: str = "认领时间",
    排序方式: str = "desc",
    筛选条件: Optional[Dict[str, Any]] = None,
    关键词: Optional[str] = None,
) -> Dict[str, Any]:
    """
    搜索用户认领的达人列表，支持分页、排序和筛选

    参数:
        用户id: 用户的ID
        页码: 当前页码，默认为1
        每页数量: 每页显示数量，默认为20
        排序字段: 排序字段，默认为"认领时间"
        排序方式: 排序方式，"asc"升序或"desc"降序，默认为"desc"
        筛选条件: 可选的筛选条件，如粉丝数范围、类别等
        关键词: 可选的搜索关键词，搜索达人昵称和抖音号

    返回:
        包含达人列表和分页信息的字典
    """
    try:
        # 参数校验
        if not 用户id or 用户id <= 0:
            raise ValueError("用户id不能为空且必须大于0")

        if 页码 < 1:
            页码 = 1

        if 每页数量 < 1 or 每页数量 > 100:
            每页数量 = 20

        # 验证排序字段
        有效排序字段 = ["认领时间", "粉丝数", "昵称", "抖音号", "抖音UID"]
        if 排序字段 not in 有效排序字段:
            排序字段 = "认领时间"

        # 验证排序方式
        if 排序方式.lower() not in ["asc", "desc"]:
            排序方式 = "desc"

        # 执行数据层查询
        结果 = await 异步搜索用户认领达人列表(
            用户id, 页码, 每页数量, 排序字段, 排序方式, 筛选条件, 关键词
        )

        # 日志记录
        接口日志器.info(
            f"搜索用户 {用户id} 认领达人列表成功，共 {结果.get('总数', 0)} 个达人，当前页 {结果.get('当前页', 1)}，每页 {结果.get('每页数量', 20)}"
        )

        return 结果
    except ValueError as e:
        # 业务逻辑错误
        错误日志器.warning(f"搜索用户认领达人列表业务错误: {str(e)}")
        raise
    except Exception as e:
        # 系统错误
        错误日志器.error(f"搜索用户认领达人列表服务异常: {str(e)}")
        raise Exception(f"搜索用户认领达人列表失败: {str(e)}")


def _安全获取查询结果(查询结果: Any, 字段名: str, 描述: str = "") -> int:
    """
    安全获取查询结果中的数值字段

    参数:
        查询结果: 数据库查询返回的结果
        字段名: 要获取的字段名
        描述: 用于日志的描述信息

    返回:
        int: 字段值，如果获取失败返回0
    """
    try:
        if 查询结果 and isinstance(查询结果, list) and len(查询结果) > 0:
            if isinstance(查询结果[0], dict) and 字段名 in 查询结果[0]:
                return 查询结果[0][字段名] or 0
            else:
                接口日志器.warning(f"{描述}查询结果格式异常: {查询结果}")
                return 0
        else:
            接口日志器.warning(f"{描述}查询无结果: {查询结果}")
            return 0
    except Exception as e:
        接口日志器.error(f"{描述}结果处理异常: {str(e)}, 原始结果: {查询结果}")
        return 0





async def _处理第三方搜索并保存(关键词: str) -> None:
    """
    处理第三方API搜索并自动保存新达人到数据库

    参数:
        关键词: 搜索关键词（抖音号）
    """
    try:
        接口日志器.info(f"开始第三方搜索处理，关键词: {关键词}")

        # 导入必要的模块
        from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

        # 步骤1：检查数据库中是否已存在该达人
        检查SQL = """
        SELECT id, 昵称, account_douyin, avatar, 粉丝数, uid_number
        FROM 达人表
        WHERE account_douyin = $1 AND (账号状态 IS NULL OR 账号状态 != 1)
        LIMIT 1
        """

        现有达人 = await 异步连接池实例.执行查询(检查SQL, (关键词,))

        if 现有达人:
            接口日志器.info(f"达人已存在于数据库中，抖音号: {关键词}")
            return

        # 步骤2：调用第三方API搜索
        外部达人搜索API地址 = "https://daduoduo.com/ajax/dyLiveDataAjax.ashx"
        params = {
            "action": "GetSearchTipForPeople",
            "keyword": 关键词,
            "sortType": "1",
        }

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }

        async with httpx.AsyncClient(timeout=15.0) as client:
            response = await client.get(
                外部达人搜索API地址, params=params, headers=headers
            )
            response.raise_for_status()
            搜索结果 = response.json()

            # 解析搜索结果
            is_success = (
                搜索结果.get("code") == 0
                or 搜索结果.get("msg") == "success"
                or 搜索结果.get("status") == "success"
                or "data" in 搜索结果
            )

            if not is_success:
                接口日志器.warning(f"第三方搜索API返回失败状态，关键词: {关键词}")
                return

            # 获取达人列表
            达人列表 = []
            if 搜索结果.get("data", {}).get("data"):
                达人列表 = 搜索结果["data"]["data"]
            elif 搜索结果.get("data") and isinstance(搜索结果["data"], list):
                达人列表 = 搜索结果["data"]
            elif 搜索结果.get("list"):
                达人列表 = 搜索结果["list"]

            if not 达人列表:
                接口日志器.info(f"第三方搜索未找到匹配的达人，关键词: {关键词}")
                return

            # 步骤3：处理搜索结果并保存到数据库
            保存成功数量 = 0
            for 达人 in 达人列表:
                try:
                    uid_number = 达人.get("UserId", "")
                    if not uid_number:
                        continue

                    # 检查该uid是否已存在
                    uid检查SQL = """
                    SELECT id FROM 达人表
                    WHERE uid_number = $1 AND (账号状态 IS NULL OR 账号状态 != 1)
                    LIMIT 1
                    """

                    现有uid = await 异步连接池实例.执行查询(uid检查SQL, (uid_number,))
                    if 现有uid:
                        接口日志器.info(f"UID已存在，跳过保存: {uid_number}")
                        continue

                    # 调用现有的查询或更新达人函数来保存新达人
                    结果 = await 查询或更新达人(uid_number)

                    if 结果["状态"] == "成功":
                        保存成功数量 += 1
                        接口日志器.info(
                            f"成功保存新达人，UID: {uid_number}, 昵称: {达人.get('Name', '')}"
                        )

                except Exception as save_error:
                    错误日志器.error(
                        f"保存达人失败，UID: {达人.get('UserId', '')}, 错误: {str(save_error)}"
                    )
                    continue

            接口日志器.info(
                f"第三方搜索处理完成，关键词: {关键词}, 成功保存: {保存成功数量} 个达人"
            )

    except httpx.TimeoutException:
        错误日志器.error(f"第三方搜索超时: {关键词}")
        raise Exception("第三方搜索服务超时")
    except httpx.HTTPStatusError as e:
        错误日志器.error(
            f"第三方搜索HTTP错误: {e.response.status_code}, 关键词: {关键词}"
        )
        raise Exception(f"第三方搜索服务错误: {e.response.status_code}")
    except Exception as e:
        错误日志器.error(f"第三方搜索处理异常: {str(e)}, 关键词: {关键词}")
        raise Exception(f"第三方搜索处理失败: {str(e)}")


async def 检查并插入达人到数据库(
    uid_number: str, 
    昵称: str, 
    avatar: str, 
    follower_count: int
) -> Dict[str, Any]:
    """
    检查达人是否存在于数据库中，如果不存在则插入
    
    参数:
        uid_number: 达人UID
        昵称: 达人昵称
        avatar: 达人头像URL
        follower_count: 粉丝数
    
    返回:
        dict: 包含操作结果的字典
    """
    from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
    
    try:
        # 检查达人是否已存在
        检查SQL = """
        SELECT id, 昵称, avatar, 粉丝数
        FROM 达人表
        WHERE uid_number = $1 AND (账号状态 IS NULL OR 账号状态 != 1)
        LIMIT 1
        """
        
        现有达人 = await 异步连接池实例.执行查询(检查SQL, (uid_number,))
        
        if 现有达人:
            # 达人已存在，返回现有数据
            return {
                "exists": True,
                "action": "found_existing",
                "data": 现有达人[0],
                "message": f"达人 {昵称} 已存在于数据库中"
            }
        
        # 达人不存在，插入新记录
        from datetime import datetime
        当前时间 = datetime.now()

        插入SQL = """
        INSERT INTO 达人表 (
            uid_number,
            昵称,
            avatar,
            粉丝数,
            update_time,
            douyin_info_update_time
        ) VALUES ($1, $2, $3, $4, $5, NOW())
        """

        插入参数 = (
            uid_number,
            昵称,
            avatar,
            follower_count,
            当前时间
        )
        
        插入结果 = await 异步连接池实例.执行更新(插入SQL, 插入参数)
        
        if 插入结果:
            应用日志器.info(f"成功插入新达人: {昵称} (UID: {uid_number})")
            return {
                "exists": False,
                "action": "inserted",
                "data": {
                    "uid_number": uid_number,
                    "昵称": 昵称,
                    "avatar": avatar,
                    "粉丝数": follower_count
                },
                "message": f"达人 {昵称} 已成功添加到数据库"
            }
        else:
            错误日志器.error(f"插入达人失败: {昵称} (UID: {uid_number})")
            return {
                "exists": False,
                "action": "insert_failed",
                "data": None,
                "message": f"达人 {昵称} 插入数据库失败"
            }
            
    except Exception as e:
        错误日志器.error(f"检查并插入达人失败: {str(e)}")
        return {
            "exists": False,
            "action": "error",
            "data": None,
            "message": f"数据库操作失败: {str(e)}"
        }
