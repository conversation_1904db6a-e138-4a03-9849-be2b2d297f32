#!/usr/bin/env python3
"""
测试智能体工具调用功能 - 验证修复后的工具调用
测试智能体ID=5能否正确调用工具，不被干扰性规则影响
"""

import asyncio
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from 服务.LangChain_智能体服务 import LangChain智能体服务实例


async def 测试智能体工具调用():
    """测试智能体工具调用功能 - 验证自动用户ID注入"""
    try:
        print("🚀 开始测试智能体自动用户ID工具调用...")

        # 测试参数
        智能体id = 5

        # 获取智能体配置信息
        智能体配置 = await LangChain智能体服务实例.获取智能体详情(智能体id)
        绑定用户id = 智能体配置.get("用户表id", 0) if 智能体配置 else 0

        if not 绑定用户id or 绑定用户id <= 0:
            print(f"❌ 智能体ID={智能体id}未绑定有效用户ID，配置值: {绑定用户id}")
            return

        # 简化的用户消息 - 不再包含具体的用户ID
        用户消息 = "请查询我的用户信息，返回昵称和手机号"

        print("📋 测试参数:")
        print(f"   智能体ID: {智能体id}")
        print(f"   绑定用户ID: {绑定用户id}")
        print(f"   用户消息: {用户消息}")
        print("   期望: 智能体应该自动使用关联的用户ID调用'查询我的信息'工具")
        print()

        # 调用智能体对话
        print("🔄 调用智能体对话...")
        对话结果 = await LangChain智能体服务实例.智能体对话(
            智能体id=智能体id,
            用户表id=绑定用户id,
            用户消息=用户消息,
            会话id="test-auto-user-id",
        )

        print("📊 对话结果:")
        print(f"   状态码: {对话结果.get('status')}")
        print(f"   消息: {对话结果.get('message', 'N/A')}")

        if 对话结果.get("status") == 100:
            data = 对话结果.get("data", {})
            智能体回复 = data.get("智能体回复", "")
            工具使用信息 = data.get("工具使用信息", [])

            print("✅ 智能体回复:")
            print(智能体回复)
            print()

            # 检查是否调用了工具
            if len(工具使用信息) > 0:
                print(f"🎉 成功！智能体调用了 {len(工具使用信息)} 个工具")

                for i, 工具信息 in enumerate(工具使用信息, 1):
                    工具名称 = 工具信息.get("工具名称", "N/A")
                    工具状态 = 工具信息.get("调用状态", "N/A")
                    工具结果 = 工具信息.get("调用结果", "")
                    工具参数 = 工具信息.get("调用参数", {})

                    print(f"   工具 {i}: {工具名称} (状态: {工具状态})")
                    print(f"     参数: {工具参数}")
                    print(f"     结果: {工具结果[:100]}...")

                    if 工具名称 in ["查询用户信息", "查询我的信息"] and "昵称" in str(工具结果):
                        print("     ✅ 成功获取用户信息")

                print("\n🎉 自动用户ID注入成功！智能体现在能够:")
                print("   1. 自动使用关联的用户ID调用工具")
                print("   2. 无需在用户消息中指定具体用户ID")
                print("   3. 获取准确的用户信息")
                print("   4. 返回结构化的回复")
                print("   5. 正确收集和返回工具使用信息")

            else:
                print("❌ 智能体未调用工具")
                if "哭哭" in 智能体回复:
                    print("   可能原因: 智能体遵循了干扰性规则而非工具调用指令")
                else:
                    print("   可能原因: 智能体未识别到需要调用'查询我的信息'工具")
        else:
            print(f"❌ 对话失败: {对话结果}")

    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback

        traceback.print_exc()


async def main():
    """主函数"""
    print("=" * 60)
    print("智能体自动用户ID工具调用测试")
    print("=" * 60)
    print()

    try:
        # 测试智能体自动用户ID工具调用
        await 测试智能体工具调用()

        print("=" * 60)
        print("测试完成")
        print("=" * 60)
    except Exception as e:
        print(f"❌ 主函数执行失败: {str(e)}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
