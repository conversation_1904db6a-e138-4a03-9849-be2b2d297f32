import traceback
from typing import Optional, Dict

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel

# 导入必需的依赖
from 依赖项.认证 import 获取当前用户
from 日志 import 错误日志器, 接口日志器
from 服务.异步系统服务 import 异步获取最新更新
from 服务.异步通告服务 import 异步获取通告列表, 异步获取通告详情
from 状态 import 状态

系统路由 = APIRouter()

@系统路由.get("/latest_updates", summary="获取用户最新动态", description="获取最新更新信息")
async def 获取最新动态():
    """获取系统最新动态信息"""
    try:
        # 获取最新动态
        最新数据 = await 异步获取最新更新()
        if 最新数据 is not None:
            return {
                "status": 100,
                "message": "成功",
                "data": 最新数据
            }
        else:
            return {
                "status": 状态.通用.无数据,
                "message": "无结果",
                "data": None
            }
    except Exception as e:
        错误日志器.error(f"获取最新动态失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "status": 状态.通用.服务器错误,
                "message": "获取最新动态时发生系统错误",
                "data": None
            }
        )

# 通告列表请求模型
class 通告列表请求(BaseModel):
    """通告列表请求参数模型"""
    页码: int = 1
    每页数量: int = 10
    类型: Optional[str] = None
    不返回内容: bool = True

@系统路由.post("/announcement/list", summary="获取通告列表", description="获取系统通告列表，支持分页和类型筛选，可选择不返回内容字段以提高性能")
async def 获取通告列表(请求参数: 通告列表请求, 当前用户: Dict = Depends(获取当前用户)):
    """获取系统通告列表"""
    接口日志器.info(f"获取通告列表请求: {请求参数}, 用户id: {当前用户.get('id')}")
    
    try:
        # 参数校验
        if 请求参数.页码 < 1:
            return {
                "status": 状态.通用.参数错误,
                "message": "页码必须大于0",
                "data": None
            }
        
        if 请求参数.每页数量 < 1 or 请求参数.每页数量 > 100:
            return {
                "status": 状态.通用.参数错误,
                "message": "每页数量必须在1-100之间",
                "data": None
            }
        
        # 调用服务层获取通告列表，只获取已发布的通告
        结果 = await 异步获取通告列表(
            页码=请求参数.页码,
            每页数量=请求参数.每页数量,
            类型=请求参数.类型
        )
        
        # 处理响应
        if 结果['总数'] > 0:
            return {
                "status": 100,
                "message": "成功",
                "data": 结果
            }
        else:
            return {
                "status": 状态.通用.无数据,
                "message": "暂无通告数据",
                "data": 结果
            }
    except Exception as e:
        错误日志器.error(f"获取通告列表失败: {str(e)}")
        错误日志器.error(traceback.format_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "status": 状态.通用.服务器错误,
                "message": "获取通告列表时发生系统错误",
                "data": None
            }
        )

# 通告详情请求模型
class 通告详情请求(BaseModel):
    """通告详情请求参数模型"""
    通告id: int

@系统路由.post("/announcement/detail", summary="获取通告详情", description="获取单个通告的详细信息")
async def 获取通告详情(请求参数: 通告详情请求, 当前用户: Dict = Depends(获取当前用户)):
    """获取单个通告的详细信息"""
    接口日志器.info(f"获取通告详情请求: {请求参数}, 用户id: {当前用户.get('id')}")
    
    try:
        # 参数校验
        if 请求参数.通告id <= 0:
            return {
                "status": 状态.通用.参数错误,
                "message": "通告ID无效",
                "data": None
            }
        
        # 调用服务层获取通告详情，只获取已发布的通告
        通告详情 = await 异步获取通告详情(请求参数.通告id)
        
        # 处理响应
        if 通告详情:
            return {
                "status": 100,
                "message": "成功",
                "data": 通告详情
            }
        else:
            return {
                "status": 状态.通用.无数据,
                "message": "未找到指定通告",
                "data": None
            }
    except Exception as e:
        错误日志器.error(f"获取通告详情失败: {str(e)}")
        错误日志器.error(traceback.format_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "status": 状态.通用.服务器错误,
                "message": "获取通告详情时发生系统错误",
                "data": None
            }
        )
